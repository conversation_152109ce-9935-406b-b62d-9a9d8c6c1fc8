package com.carplus.subscribe.event.car;

import com.carplus.subscribe.model.crs.CarBaseInfoSearchResponse;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 出車中換車 - 長放車註冊 & 註銷事件
 */
@Getter
public class CarReplaceEvent extends ApplicationEvent {

    private final String orderNo;

    private final CarBaseInfoSearchResponse outCarCrsInfo;

    private final CarBaseInfoSearchResponse inCarCrsInfo;

    private final String memberId;

    public CarReplaceEvent(Object source, String orderNo, CarBaseInfoSearchResponse outCarCrsInfo, CarBaseInfoSearchResponse inCarCrsInfo, String memberId) {
        super(source);
        this.orderNo = orderNo;
        this.outCarCrsInfo = outCarCrsInfo;
        this.inCarCrsInfo = inCarCrsInfo;
        this.memberId = memberId;
    }
}
