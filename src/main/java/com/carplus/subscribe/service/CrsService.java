package com.carplus.subscribe.service;

import carplus.common.enums.HeaderDefine;
import carplus.common.response.Result;
import carplus.common.response.exception.BadRequestException;
import carplus.common.response.exception.ServerException;
import carplus.common.utils.DateUtils;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.enums.BuChangeEnum;
import com.carplus.subscribe.enums.BuIdEnum;
import com.carplus.subscribe.enums.CarControlEnum;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.feign.CrsClient;
import com.carplus.subscribe.feign.InsuranceClient;
import com.carplus.subscribe.model.CustomizePage;
import com.carplus.subscribe.model.CustomizedSearchPage;
import com.carplus.subscribe.model.crs.*;
import com.carplus.subscribe.model.crs.assign.*;
import com.carplus.subscribe.model.insurance.InsurePlanListRequest;
import com.carplus.subscribe.model.insurance.InsurePlanSearchResultResponse;
import com.carplus.subscribe.model.station.CarAreaInfo;
import com.carplus.subscribe.model.station.CarLocationInfo;
import com.carplus.subscribe.utils.CarsUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static com.carplus.subscribe.constant.CarPlusConstant.*;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.*;

@Service
@Slf4j
public class CrsService {

    @Autowired
    @Lazy
    private BuChangeService buChangeService;
    @Autowired
    private ConfigService configService;
    @Autowired
    private CrsClient crsClient;
    @Autowired
    private InsuranceClient insuranceClient;

    /**
     * 檢查是否為訂閱車
     */
    public List<String> checkIsSubscribeCars(String plateNo) {
        List<String> errorMessage = new ArrayList<>();

        Result<CustomizedSearchPage<CarBaseInfoSearchResponse>> result = crsClient.getCars(PLATE_FORM_CODE, HeaderDefine.SystemKind.SUB,
            CarBaseInfoQueryReq.builder().limit(100).isQueryProjectCar(false).plateNoList(Collections.singletonList(plateNo)).build());
        if (result.getStatusCode() == 0) {
            CarBaseInfoSearchResponse carBaseInfoSearchResponse = result.getData().getPage().getList().stream().filter(car -> car.getPlateNo().equalsIgnoreCase(plateNo)).findAny().orElseThrow(() -> new SubscribeException(CAR_NOT_FOUND));
            if (BuIdEnum.isNotValidForSubscribe(carBaseInfoSearchResponse.getBuId())) {
                errorMessage.add(NOT_FOR_SUBSCRIBE.getMsg());
            }
            if (!carBaseInfoSearchResponse.getCarLicense().getLicenseStatus().equals("0")) {
                errorMessage.add(LICENSE_PLATE_NOT_PURCHASED.getMsg());
            }
        }

        return errorMessage;
    }


    public Map<String, CarBaseInfoSearchResponse> getCars(List<String> plateNos) {
        Result<CustomizedSearchPage<CarBaseInfoSearchResponse>> result = crsClient.getCars(PLATE_FORM_CODE, HeaderDefine.SystemKind.SUB, CarBaseInfoQueryReq.builder().limit(10000).isQueryProjectCar(false).plateNoList(plateNos).build());
        if (result.getStatusCode() == 0) {
            List<CarBaseInfoSearchResponse> list = Optional.of(result).map(Result::getData).map(CustomizedSearchPage::getPage).map(CustomizePage::getList).orElse(new ArrayList<>());
            return list.stream().collect(Collectors.toMap(CarBaseInfoSearchResponse::getPlateNo, c -> c, (v1, v2) -> v2));
        }
        return new HashMap<>();
    }

    public Map<String, CarBaseInfoQueryResponse> getCarBaseInfoQueryResponses(List<String> plateNos) {
        Result<List<CarBaseInfoQueryResponse>> result = crsClient.getCarBaseList(PLATE_FORM_CODE, HeaderDefine.SystemKind.SUB, CarBaseQueryReq.builder().plateNoList(plateNos).build());
        if (result.getStatusCode() == 0) {
            return result.getData().stream().collect(Collectors.toMap(CarBaseInfoQueryResponse::getPlateNo, c -> c, (v1, v2) -> v2));
        }
        return new HashMap<>();
    }

    public Map<Integer, CarBaseInfoSearchResponse> getCarsByCrsCarNo(List<Integer> crsCarNos) {
        Result<CustomizedSearchPage<CarBaseInfoSearchResponse>> result = crsClient.getCars(PLATE_FORM_CODE, HeaderDefine.SystemKind.SUB, CarBaseInfoQueryReq.builder().limit(10000).isQueryProjectCar(false).carNoList(crsCarNos).build());
        if (result.getStatusCode() == 0) {
            List<CarBaseInfoSearchResponse> list = Optional.of(result).map(Result::getData).map(CustomizedSearchPage::getPage).map(CustomizePage::getList).orElse(new ArrayList<>());
            return list.stream().collect(Collectors.toMap(CarBaseInfoSearchResponse::getCarNo, c -> c, (v1, v2) -> v2));
        }
        return new HashMap<>();
    }

    public CarBaseInfoSearchResponse getCar(String plateNo) {
        return getCars(Collections.singletonList(plateNo)).get(plateNo);
    }

    public Map<Integer, PurchaseProjectCarSearchResponse> searchProjectCars(List<Integer> crsNos) {
        Result<List<PurchaseProjectCarSearchResponse>> result = crsClient.searchProjectCar(PurchaseProjectCarSearchRequest.builder().carNoList(crsNos).build());
        if (result.getStatusCode() == 0) {
            List<PurchaseProjectCarSearchResponse> list = Optional.of(result).map(Result::getData).orElse(new ArrayList<>());
            return list.stream().collect(Collectors.toMap(PurchaseProjectCarSearchResponse::getCarNo, c -> c, (v1, v2) -> v2));
        }
        return new HashMap<>();
    }

    public PurchaseProjectCarSearchResponse searchProjectCar(Integer crsNo) {
        return searchProjectCars(Collections.singletonList(crsNo)).get(crsNo);
    }

    public Long searchInsurePlan(CarBaseInfoSearchResponse carBaseInfoSearchResponse) {
        InsurePlanListRequest insurePlanListRequest = new InsurePlanListRequest();
        insurePlanListRequest.setBrandCode(carBaseInfoSearchResponse.getCarBase().getBrandCode());
        insurePlanListRequest.setCarSeries(carBaseInfoSearchResponse.getCarSpecInfoResponse().getCarPlusCarSeriesCode());
        insurePlanListRequest.setCode4(carBaseInfoSearchResponse.getCarBase().getCode4());
        insurePlanListRequest.setCarType(carBaseInfoSearchResponse.getCarSpecInfoResponse().getCarPlusCarType());
        insurePlanListRequest.setCarPlusEnergyCode(String.valueOf(carBaseInfoSearchResponse.getCarSpecInfoResponse().getCarPlusEnergyCode()));
        insurePlanListRequest.setStdPriceCode(carBaseInfoSearchResponse.getCarBase().getStdPriceCode());
        insurePlanListRequest.setClassCode(carBaseInfoSearchResponse.getCarBase().getClassCode());
        insurePlanListRequest.setUseTypeId(BuIdEnum.subscribe.getCode());
        Result<List<InsurePlanSearchResultResponse>> result = insuranceClient.searchInsurePlanList(Collections.singletonList(insurePlanListRequest));
        if (result.getStatusCode() == 0 && !CollectionUtils.isEmpty(result.getData())) {
            InsurePlanSearchResultResponse data = result.getData().get(0);
            if (data.getInsurePlanResponse() != null && data.getInsurePlanResponse().getPlanId() != null) {
                return data.getInsurePlanResponse().getPlanId();
            }
        }
        throw new SubscribeException(CAR_INSURANCE_PLAN_ID_NOT_FOUND);
    }

    private AssignAddReq createBaseAssignAddReq(BuChangeEnum.ChangeTypeOfAssign changeType, String memberId, Instant preUseDateStart, Instant preUseDateEnd, String memo) {
        AssignAddReq assignAddReq = new AssignAddReq();
        assignAddReq.setChangeType(changeType);
        assignAddReq.setCompanyCode(CARPLUS_COMPANY_CODE);
        assignAddReq.setUserId(memberId);
        assignAddReq.setPreUseDateStart(preUseDateStart);
        assignAddReq.setPreUseDateEnd(preUseDateEnd);
        assignAddReq.setMemo(memo);
        return assignAddReq;
    }

    private AssignDetailReq createAssignDetailReqForSubscribe(CarBaseInfoSearchResponse carBase, String leaveMemo, String receiveMemo) {
        if (carBase.getSubSystemCarInfo() == null || StringUtils.isBlank(carBase.getSubSystemCarInfo().getLocationId())) {
            throw new SubscribeException(CAR_NOT_HAVE_STATION_CAN_NOT_CHANGE_BU);
        }
        Long planId = searchInsurePlan(carBase);
        return AssignDetailReq.builder()
            .carNo(carBase.getCarNo())
            .plateNo(carBase.getPlateNo())
            .targetBu(BuIdEnum.subscribe.getCode())
            .targetArea(departmentMasterCode)
            .targetAreaName(departmentMasterName)
            .targetRealArea(departmentMasterCode)
            .targetLocation(SUBSCRIBE_MANAGEMENT_DEPT_CODE)
            .targetLocationName(departmentName)
            .targetRealLocation(SUBSCRIBE_MANAGEMENT_DEPT_CODE)
            .fromBu(carBase.getBuId())
            .fromArea(carBase.getSubSystemCarInfo().getAreaIdNew())
            .fromAreaName(carBase.getSubSystemCarInfo().getAreaName())
            .fromRealArea(carBase.getSubSystemCarInfo().getAreaId())
            .fromLocation(carBase.getSubSystemCarInfo().getLocationIdNew())
            .fromLocationName(carBase.getSubSystemCarInfo().getLocationName())
            .fromRealLocation(carBase.getSubSystemCarInfo().getLocationId())
            .insurancePlanNo(planId.toString())
            .leaveMemo(leaveMemo)
            .receiveMemo(receiveMemo).build();
    }

    private AssignDetailReq createAssignDetailReqForReturnSell(CarBaseInfoSearchResponse carBase, Integer targetBuCode,
                                                               String targetArea, String targetAreaName, String targetRealArea,
                                                               String targetLocation, String targetLocationName, String targetRealLocation,
                                                               String insurancePlanNo, String leaveMemo, String receiveMemo) {
        return AssignDetailReq.builder()
            .carNo(carBase.getCarNo())
            .plateNo(carBase.getPlateNo())
            .targetBu(targetBuCode)
            .targetArea(targetArea)
            .targetAreaName(targetAreaName)
            .targetRealArea(targetRealArea)
            .targetLocation(targetLocation)
            .targetLocationName(targetLocationName)
            .targetRealLocation(targetRealLocation)
            .fromBu(carBase.getBuId())
            .fromArea(departmentMasterCode)
            .fromAreaName(departmentMasterName)
            .fromRealArea(departmentMasterCode)
            .fromLocation(SUBSCRIBE_MANAGEMENT_DEPT_CODE)
            .fromLocationName(departmentName)
            .fromRealLocation(SUBSCRIBE_MANAGEMENT_DEPT_CODE)
            .insurancePlanNo(insurancePlanNo)
            .leaveMemo(leaveMemo)
            .receiveMemo(receiveMemo).build();
    }

    private AssignDetailReq createAssignDetailReqForReturnSell(CarBaseInfoSearchResponse carBase, Integer targetBuCode,
                                                               String targetArea, String targetAreaName, String targetRealArea,
                                                               String targetLocation, String targetLocationName, String targetRealLocation,
                                                               String insurancePlanNo) {
        return createAssignDetailReqForReturnSell(carBase, targetBuCode, targetArea, targetAreaName, targetRealArea,
            targetLocation, targetLocationName, targetRealLocation, insurancePlanNo, null, null);
    }

    private Integer executeChangeBu(String orderNo, CarBaseInfoSearchResponse carBase, AssignAddReq assignAddReq) {
        Integer buChangeMasterId = changeBu(assignAddReq);
        buChangeService.addBuChangeLog(orderNo, carBase.getPlateNo(), assignAddReq, buChangeMasterId);
        return buChangeMasterId;
    }

    /**
     * 自動撥車
     */
    public Integer autoChangeBu(String orderNo, CarBaseInfoSearchResponse carBase, String memberId, Instant preUseDateStart, Instant preUseDateEnd, Instant licenseExpDate) {

        AssignAddReq assignAddReq = createBaseAssignAddReq(BuChangeEnum.ChangeTypeOfAssign.AUTO_BATCH_CHANGE, memberId, preUseDateStart, preUseDateEnd, "訂閱自動營業用撥車\n" + orderNo);
        assignAddReq.setLicenseExpDate(licenseExpDate);

        AssignDetailReq assignDetailReq = createAssignDetailReqForSubscribe(carBase, "訂閱自動營業用撥車", "訂閱自動營業用撥車");
        assignAddReq.setAssignDetailList(Collections.singletonList(assignDetailReq));

        return executeChangeBu(orderNo, carBase, assignAddReq);
    }

    /**
     * 營業用撥車
     */
    public Integer batchChangeBu(String orderNo, CarBaseInfoSearchResponse carBase, String memberId, Instant preUseDateStart, Instant preUseDateEnd, List<String> attachmentId) {

        AssignAddReq assignAddReq = createBaseAssignAddReq(BuChangeEnum.ChangeTypeOfAssign.BATCH_CHANGE, memberId, preUseDateStart, preUseDateEnd, String.format("訂閱借調，呈請同意。%s", orderNo));
        assignAddReq.setFileIdList(attachmentId);

        AssignDetailReq assignDetailReq = createAssignDetailReqForSubscribe(carBase, "訂閱營業用撥車", "訂閱營業用撥車");
        assignAddReq.setAssignDetailList(Collections.singletonList(assignDetailReq));

        return executeChangeBu(orderNo, carBase, assignAddReq);
    }

    /**
     * 一般撥車
     */
    public Integer generalChangeBu(String orderNo, CarBaseInfoSearchResponse carBase, String memberId, Instant preUseDateStart, Instant preUseDateEnd, List<String> attachmentId) {

        AssignAddReq assignAddReq = createBaseAssignAddReq(BuChangeEnum.ChangeTypeOfAssign.CHANGE, memberId, preUseDateStart, preUseDateEnd, String.format("訂閱借調，呈請同意。%s", orderNo));
        assignAddReq.setFileIdList(attachmentId);

        AssignDetailReq assignDetailReq = createAssignDetailReqForSubscribe(carBase, "訂閱一般撥車", "訂閱一般撥車");
        assignAddReq.setAssignDetailList(Collections.singletonList(assignDetailReq));

        return executeChangeBu(orderNo, carBase, assignAddReq);
    }

    /**
     * 自動撥還車
     */
    public Integer autoChangeReturnBu(String orderNo, CarBaseInfoSearchResponse carBase, String memberId, Integer documentNo) {

        ChangeListResponse changeListResponse = getChangeBuInfos(documentNo);
        ChangeDetailResponse detailResponse = changeListResponse.getBuChangeDetails().get(0);

        Instant now = Instant.now();
        AssignAddReq assignAddReq = createBaseAssignAddReq(BuChangeEnum.ChangeTypeOfAssign.AUTO_BATCH_CHANGE, memberId, now, now, String.format("訂閱自動營業用撥車(還車)(客戶起租前取消/換車%s)", orderNo));
        assignAddReq.setDocumentNo(documentNo.toString());

        if (StringUtils.isBlank(detailResponse.getFromRealLocation())) {
            throw new SubscribeException(CAR_NOT_HAVE_STATION_CAN_NOT_RETURN_BU);
        }

        AssignDetailReq assignDetailReq = createAssignDetailReqForReturnSell(
            carBase,
            BuIdEnum.secondHand.getCode(),
            detailResponse.getFromArea(),
            detailResponse.getFromAreaName(),
            detailResponse.getFromRealArea(),
            detailResponse.getFromLocation(),
            detailResponse.getFromLocationName(),
            detailResponse.getFromRealLocation(),
            String.valueOf(searchInsurePlan(carBase))
        );
        assignAddReq.setAssignDetailList(Collections.singletonList(assignDetailReq));
        assignAddReq.setInsuranceList(null);

        return executeChangeBu(orderNo, carBase, assignAddReq);
    }

    /**
     * 營業一鍵撥還車
     */
    public void batchChangeReturnBu(String orderNo, CarBaseInfoSearchResponse carBase, String memberId, Integer documentNo) {

        ChangeListResponse changeListResponse = getChangeBuInfos(documentNo);
        ChangeDetailResponse detailResponse = changeListResponse.getBuChangeDetails().get(0);

        Instant now = Instant.now();
        AssignAddReq assignAddReq = createBaseAssignAddReq(BuChangeEnum.ChangeTypeOfAssign.BATCH_RETURN, memberId, now, now, String.format("訂閱取消/換車/還車(%s)", orderNo));
        assignAddReq.setDocumentNo(documentNo.toString());

        if (StringUtils.isBlank(detailResponse.getFromRealLocation())) {
            throw new SubscribeException(CAR_NOT_HAVE_STATION_CAN_NOT_RETURN_BU);
        }

        AssignDetailReq assignDetailReq = createAssignDetailReqForReturnSell(
            carBase,
            detailResponse.getFromBu(),
            detailResponse.getFromArea(),
            detailResponse.getFromAreaName(),
            detailResponse.getFromRealArea(),
            detailResponse.getFromLocation(),
            detailResponse.getFromLocationName(),
            detailResponse.getFromRealLocation(),
            String.valueOf(searchInsurePlan(carBase))
        );
        assignAddReq.setAssignDetailList(Collections.singletonList(assignDetailReq));
        assignAddReq.setInsuranceList(null);

        executeChangeBu(orderNo, carBase, assignAddReq);
    }

    /**
     * 賣車至中古
     */
    public Integer sellToPreowned(@Nullable String orderNo, CarBaseInfoSearchResponse carBase, CarAreaInfo carAreaInfo, String requestMemberId) {

        // memberId 固定 2085
        String memberId = configService.getSubscribeConfig().getSubscribeDefaultMemberId();
        Instant now = Instant.now();
        AssignAddReq assignAddReq = createBaseAssignAddReq(BuChangeEnum.ChangeTypeOfAssign.SELL, memberId, now,
            now.atZone(DateUtils.ZONE_TPE).plusYears(1).toInstant(), String.format("訂閱申請賣車(確認已入庫%s)%s",
                StringUtils.isNotBlank(requestMemberId) ? String.format(";申請人(%s)", requestMemberId) : "",
                orderNo == null ? "" : orderNo));

        CarLocationInfo carLocationInfo = carAreaInfo.getUnderCarInfo().get(0);
        AssignDetailReq assignDetailReq = createAssignDetailReqForReturnSell(
            carBase,
            BuIdEnum.secondHand.getCode(),
            carAreaInfo.getCodeNew(),
            carAreaInfo.getName(),
            carAreaInfo.getCode(),
            carLocationInfo.getCodeNew(),
            carLocationInfo.getName(),
            carLocationInfo.getCode(),
            "",
            "訂閱自動賣車",
            "訂閱自動賣車"
        );
        assignAddReq.setAssignDetailList(Collections.singletonList(assignDetailReq));

        return executeChangeBu(orderNo, carBase, assignAddReq);
    }

    /**
     * 查詢撥車
     */
    public ChangeListResponse getChangeBuInfos(Integer documentNo) {
        return Optional.of(crsClient.getChangeBuInfos(PLATE_FORM_CODE, HeaderDefine.SystemKind.SUB, documentNo))
            .map(Result::getData).orElseThrow(() -> new SubscribeException(CRS_CHANGE_CAR_CHANGE_ID_NOT_FOUND));
    }

    public List<BUChangeSearchResponse> getChangeBuInfoSearch(Integer documentNo) {
        List<BUChangeSearchResponse> list = Optional.of(crsClient.getChangeBuInfoDetails(PLATE_FORM_CODE, HeaderDefine.SystemKind.SUB, new BUChangeSearchReq(documentNo.toString())))
            .map(Result::getData).orElseThrow(() -> new SubscribeException(CRS_CHANGE_CAR_CHANGE_DETAIL_NOT_FOUND));
        if (CollectionUtils.isEmpty(list)) {
            throw new SubscribeException(CRS_CHANGE_CAR_CHANGE_DETAIL_NOT_FOUND);
        }
        return list;
    }

    public BUChangeDetailSearchResponse getChangeBuInfoDetail(Integer documentNo, String plateNo, Integer crsCarNo) {
        List<BUChangeSearchResponse> buChangeDetailSearchResponses = getChangeBuInfoSearch(documentNo);
        if (CollectionUtils.isEmpty(buChangeDetailSearchResponses)) {
            throw new SubscribeException(CRS_CHANGE_CAR_CHANGE_DETAIL_NOT_FOUND);
        } else {
            if (crsCarNo != null) {
                return buChangeDetailSearchResponses.get(0).getBuChangeDetailList().stream().filter(detail -> crsCarNo.equals(detail.getCarNo())).findAny().orElseThrow(() -> new SubscribeException(CRS_CHANGE_CAR_CHANGE_DETAIL_NOT_FOUND));
            }
            return buChangeDetailSearchResponses.get(0).getBuChangeDetailList().stream().filter(detail -> plateNo.equals(detail.getPlateNo())).findAny().orElseThrow(() -> new SubscribeException(CRS_CHANGE_CAR_CHANGE_DETAIL_NOT_FOUND));
        }
    }

    private Integer changeBu(AssignAddReq assignAddReq) {
        Result<Integer> result = crsClient.changeBu(PLATE_FORM_CODE, HeaderDefine.SystemKind.SUB, assignAddReq);
        if (result.getStatusCode() != 0) {
            log.error("CRS撥車失敗,Request:{},result:{}", assignAddReq, result);
            throw new ServerException(result.getMessage());
        } else {
            return Optional.of(result).map(Result::getData).orElse(null);
        }
    }

    public void updateKm(Cars car, int currentMileage, String orderNo) {
        // Check CrsCarNo not null and greater than 0 first
        Integer crsCarNo = car.getCrsCarNo();
        if (crsCarNo != null && crsCarNo > 0 && CarsUtil.isCarPlusCar(car.getVatNo())) {
            Result<List<CarBaseUpdateKmResponse>> result = crsClient.updateKm(Collections.singletonList(CarBaseUpdateKmReq.builder()
                .carNo(crsCarNo)
                .depenceDocType("1")
                .depenceDocNo(orderNo)
                .km(currentMileage)
                .userId("subscribe-updateKm")
                .build()));
            if (!result.getData().isEmpty()) {
                for (CarBaseUpdateKmResponse response : result.getData()) {
                    log.error("更新車輛里程數失敗, CRS 車輛編號: {}, 錯誤訊息: {}", response.getCarNo(), response.getErrMsg());
                }
            } else {
                log.info("更新車輛里程數成功, 訂單編號: {}, CRS 車輛編號: {}, 最新里程數: {}", orderNo, crsCarNo, car.getCurrentMileage());
            }
        }
    }

    public CarBaseInfoToAddResponse getCarBaseInfoToAdd(String plateNo) {
        return Optional.ofNullable(getCar(plateNo))
            .map(CarBaseInfoToAddResponse::new)
            .orElseThrow(() -> new SubscribeException(CRS_CAR_NOT_FOUND));
    }

    /**
     * 取消出車API
     */
    public void cancelLeaveCar(ChangeListResponse changeListResponse, String plateNo, Integer crsCarNo, String orderNo) {
        BUChangeDetailSearchResponse changeBuInfoDetail = getChangeBuInfoDetail(changeListResponse.getId(), plateNo, crsCarNo);
        UnMoveCarReq unMoveCarReq = new UnMoveCarReq();
        unMoveCarReq.setBuChangeMasterId(changeListResponse.getId());
        unMoveCarReq.setUserId("subscribe");
        unMoveCarReq.setCancelType("2");
        unMoveCarReq.setCancelReason(String.format("訂閱收訂%s取消出車", orderNo));
        unMoveCarReq.setBuChangeDetailId(changeBuInfoDetail.getBuChangeDetailId());
        Result result = crsClient.leaveCancel(unMoveCarReq);
        if (result.getStatusCode() != 0) {
            throw new SubscribeException(CRS_CAR_LEVE_CANCEL_FAIL);
        }
    }

    /**
     * 取消撥車申請
     */
    public void cancelChangeCar(ChangeListResponse changeListResponse, String orderNo) {
        BUChangeCancelReq buChangeCancelReq = new BUChangeCancelReq();
        buChangeCancelReq.setBuChangeMasterId(changeListResponse.getId());
        buChangeCancelReq.setUserId("subscribe");
        buChangeCancelReq.setCancelType("2");
        buChangeCancelReq.setCancelReason(String.format("訂閱收訂%s取消撥車", orderNo));
        Result result = crsClient.cancelChange(buChangeCancelReq);
        if (result.getStatusCode() != 0) {
            throw new SubscribeException(CRS_CAR_CHANGE_CANCEL_FAIL);
        }
    }

    /**
     * 車輛管制,訂閱收訂
     */
    public void subscribeCarControl(CarBaseInfoSearchResponse carBase) {
        if (carBase == null) {
            return;
        }
        BuIdEnum buIdEnum = BuIdEnum.ofEnum(carBase.getBuId());
        if (buIdEnum != null && buIdEnum.isCallCrsControl()) {
            CarControlSaveRequest carControlSaveRequest = new CarControlSaveRequest(carBase.getCarNo(), CarControlEnum.ChangeType.subscribeReceive, carBase.getPlateNo(), "subscribe");
            crsClient.carControl(Collections.singletonList(carControlSaveRequest));
        } else {
            log.warn("車輛管制,訂閱收訂,車輛不是中古車,車輛編號:{}", carBase.getPlateNo());
        }
    }

    /**
     * 車輛管制,訂閱收訂
     */
    public void subscribeCarControl(Cars cars) {
        if (!cars.isVirtualCar() && CarsUtil.isCarPlusCar(cars.getVatNo())) {
            CarBaseInfoSearchResponse carBase = getCar(cars.getPlateNo());
            subscribeCarControl(carBase);
            // 收訂時，向 CRS 查是否為專案車後寫入
            Optional.ofNullable(carBase).map(CarBaseInfoSearchResponse::getIsProjectCar).ifPresent(cars::setProjectCar);
        }
    }

    /**
     * 車輛管制,訂閱收訂
     */
    public void subscribeDoneCarControl(CarBaseInfoSearchResponse carBase) {
        if (carBase == null) {
            return;
        }
        BuIdEnum buIdEnum = BuIdEnum.ofEnum(carBase.getBuId());
        if (buIdEnum != null && buIdEnum.isCallCrsControl()) {
            CarControlSaveRequest carControlSaveRequest = new CarControlSaveRequest(carBase.getCarNo(), CarControlEnum.ChangeType.ChangeType_0024, carBase.getPlateNo(), "subscribe");
            crsClient.carControl(Collections.singletonList(carControlSaveRequest));
        } else {
            log.warn("車輛管制,訂閱收訂,車輛不是中古車,車輛編號:{}", carBase.getPlateNo());
        }
    }

    /**
     * 車輛管制,訂閱退訂
     */
    public void unsubscribeCarControl(Cars cars) {
        if (!cars.isVirtualCar() && CarsUtil.isCarPlusCar(cars.getVatNo())) {
            CarBaseInfoSearchResponse carBase = getCar(cars.getPlateNo());
            unsubscribeCarControl(carBase);
        }
    }

    /**
     * 車輛管制,訂閱退訂
     */
    public void unsubscribeCarControl(CarBaseInfoSearchResponse carBase) {
        if (carBase == null) {
            return;
        }
        BuIdEnum buIdEnum = BuIdEnum.ofEnum(carBase.getBuId());
        if (buIdEnum != null && buIdEnum.isCallCrsControl()) {
            CarControlSaveRequest carControlSaveRequest = new CarControlSaveRequest(carBase.getCarNo(), CarControlEnum.ChangeType.subscribeReturn, carBase.getPlateNo(), "subscribe");
            crsClient.carControl(Collections.singletonList(carControlSaveRequest));
        }
    }

    public List<CarLicenseResponse> getCarLicenses(List<Integer> carNo) {
        PurchaseProjectCarSearchRequest purchaseProjectCarSearchRequest = new PurchaseProjectCarSearchRequest();
        purchaseProjectCarSearchRequest.setCarNoList(carNo);
        Result<List<CarLicenseResponse>> result = crsClient.getLicenseDate(purchaseProjectCarSearchRequest);
        if (result.getStatusCode() == 0) {
            return result.getData();
        }
        return new ArrayList<>();
    }

    public CarLicenseResponse getCarLicense(Integer carNo) {
        return getCarLicenses(Collections.singletonList(carNo)).stream().findFirst().orElse(null);
    }

    public void findLocationAndSellToPreowned(String orderNo, CarBaseInfoSearchResponse carBase, String stationCode) {
        CarAreaInfo preownedStation = getPreownedStationByStationCode(stationCode);
        sellToPreowned(orderNo, carBase, preownedStation, null);
    }

    public Integer findLocationAndSellToPreowned(String orderNo, CarBaseInfoSearchResponse carBase, String areaCode, String locationCode, String memberId) {
        CarAreaInfo preownedStation = getPreownedStationByAreaCode(areaCode);
        if (preownedStation.getUnderCarInfo().stream().noneMatch(carLocationInfo -> carLocationInfo.getCode().equals(locationCode))) {
            throw new BadRequestException(String.format("區域代碼與站點代碼不匹配, 區域代碼: %s, 站點代碼: %s", areaCode, locationCode));
        }
        return sellToPreowned(orderNo, carBase, preownedStation, memberId);
    }

    /**
     * 透過站點代碼取得車輛所屬區域與站點資訊 (中古)
     */
    public CarAreaInfo getPreownedStationByStationCode(String stationCode) {
        List<CarAreaInfo> carAreaInfos = crsClient.getReceiveLocation(BuIdEnum.secondHand.getCode(), "ALL")
            .getData()
            .getSubCarInfo();
        return carAreaInfos.stream()
            .filter(carAreaInfo -> carAreaInfo.getUnderCarInfo().stream()
                .anyMatch(carLocationInfo -> carLocationInfo.getCode().equals(stationCode)))
            .findFirst()
            .orElseThrow(() -> new SubscribeException(PREOWNED_INV_CONFIG_NOT_FUND));
    }

    /**
     * 透過區域代碼取得車輛所屬區域與站點資訊 (中古)
     */
    public CarAreaInfo getPreownedStationByAreaCode(String areaCode) {
        return crsClient.getReceiveLocation(BuIdEnum.secondHand.getCode(), areaCode)
            .getData()
            .getSubCarInfo().stream()
            .findFirst()
            .orElseThrow(() -> new SubscribeException(PREOWNED_INV_CONFIG_NOT_FUND));
    }
}
