package com.carplus.subscribe.db.mysql.entity.payment;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;
import org.springframework.lang.NonNull;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

@NoArgsConstructor
@Data
@Entity
@Table(name = "account_details")
public class AccountDetail {

    /**
     * 帳目歷程編號
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 帳目編號
     */
    @Column(name = "accountId", nullable = false)
    private Long accountId;

    /**
     * 金額
     */
    @Column(name = "amount", nullable = false)
    private Integer amount;

    /**
     * 建立日期時間
     */
    @Column(name = "createDate", insertable = false, updatable = false)
    private Date createDate;

    /**
     * 更新日期時間
     */
    @Column(name = "updateDate", insertable = false, updatable = false)
    private Date updateDate;

    /**
     * 是否要立賬
     */
    @Column
    private Boolean isCheckout;

    /**
     *
     */
    @Type(type = "json")
    private List<Integer> orderPriceIds;

    public AccountDetail(@NonNull Long accountId, @NonNull Integer amount, List<Integer> orderPriceIds) {
        this.accountId = accountId;
        this.amount = amount;
        this.orderPriceIds = orderPriceIds;
    }

    @PrePersist
    public void prePersist() {
        isCheckout = true;
    }
}
