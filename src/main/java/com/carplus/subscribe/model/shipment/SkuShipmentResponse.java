package com.carplus.subscribe.model.shipment;

import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.db.mysql.entity.contract.SkuShipment;
import com.carplus.subscribe.enums.PayStatus;
import com.carplus.subscribe.enums.ShipmentStatus;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@NoArgsConstructor
@Data
public class SkuShipmentResponse {
    private String orderNo;
    private Integer orderStatus;
    private Instant orderStartDate;
    private List<SkuOrderPriceInfo> skuList;

    @Data
    public static class SkuOrderPriceInfo {
        private Integer orderPriceInfoId;
        private Integer amount;
        private Integer quantity;
        private PayStatus payStatus;
        private String payStatusName;
        private String skuCode;
        private String skuName;
        private List<ShipmentInfo> skuShipmentList;
    }

    @Data
    public static class ShipmentInfo {
        private Integer id;
        private ShipmentStatus status;
        private String statusName;
        private Instant createDate;
        private Instant updateDate;
        private String creator;
        private String creatorName;
    }

    public SkuShipmentResponse(SkuShipment skuShipment, OrderPriceInfo orderPriceInfo, Orders orders) {
        this.orderNo = skuShipment.getOrderNo();
        this.orderStatus = orders.getStatus();
        this.orderStartDate = Optional.ofNullable(orders.getStartDate()).orElse(orders.getExpectStartDate());

        SkuOrderPriceInfo skuOrderPriceInfo = new SkuOrderPriceInfo();
        skuOrderPriceInfo.setOrderPriceInfoId(orderPriceInfo.getId());
        skuOrderPriceInfo.setAmount(orderPriceInfo.getActualPrice());
        skuOrderPriceInfo.setQuantity(orderPriceInfo.getInfoDetail().getQuantity());
        skuOrderPriceInfo.setPayStatus(orderPriceInfo.getPayStatus());
        skuOrderPriceInfo.setPayStatusName(orderPriceInfo.getPayStatus().getName());
        skuOrderPriceInfo.setSkuCode(orderPriceInfo.getSkuCode());

        ShipmentInfo shipmentInfo = new ShipmentInfo();
        shipmentInfo.setId(skuShipment.getId());
        shipmentInfo.setStatus(skuShipment.getStatus());
        shipmentInfo.setStatusName(skuShipment.getStatus().getDescription());
        shipmentInfo.setCreateDate(skuShipment.getInstantCreateDate());
        shipmentInfo.setUpdateDate(skuShipment.getInstantUpdateDate());
        shipmentInfo.setCreator(skuShipment.getCreator());

        skuOrderPriceInfo.setSkuShipmentList(Collections.singletonList(shipmentInfo));
        this.skuList = Collections.singletonList(skuOrderPriceInfo);
    }
}