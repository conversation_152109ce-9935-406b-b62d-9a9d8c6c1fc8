package com.carplus.subscribe.controller.contract;

import carplus.common.utils.DateUtils;
import com.carplus.subscribe.App;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.model.request.carwishlist.CarWishlistRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

@SpringBootTest(classes = App.class)
@AutoConfigureMockMvc
@Transactional
class ContractPublicControllerTest {

    @Autowired
    private MockMvc mockMvc;
    @Autowired
    private ObjectMapper objectMapper;

    private long getExpectStartDate() {
        return LocalDateTime.now(DateUtils.ZONE_TPE)
            .plusDays(10)
            .atZone(DateUtils.ZONE_TPE)
            .toInstant()
            .toEpochMilli();
    }

    @Test
    void shouldCreateContract_ExternalApi_CheckSysRemarker() throws Exception {
        // 先將車輛加入收藏清單以測試完整的備註邏輯
        String plateNo = "RDJ-7801";
        Integer acctId = 33456914;
        CarWishlistRequest wishlistRequest = new CarWishlistRequest();
        wishlistRequest.setPlateNo(plateNo);

        mockMvc.perform(post("/subscribe/carWishlist")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_ACCT, acctId)
                .content(objectMapper.writeValueAsString(wishlistRequest)))
            .andExpect(jsonPath("$.statusCode").value(0));

        // Arrange: Prepare contract creation JSON
        String createContractJson = "{\n" +
            "    \"plateNo\": \"" + plateNo + "\",\n" +
            "    \"carLevel\": 9,\n" +
            "    \"departStationCode\": \"201\",\n" +
            "    \"returnStationCode\": \"201\",\n" +
            "    \"expectStartDate\": " + getExpectStartDate() + ",\n" +
            "    \"month\": 12,\n" +
            "    \"disclaimer\": false,\n" +
            "    \"premium\": false,\n" +
            "    \"invoice\": {\n" +
            "        \"category\": 5,\n" +
            "        \"id\": \"A193477449\",\n" +
            "        \"title\": \"李志宏\",\n" +
            "        \"type\": 2\n" +
            "    },\n" +
            "    \"custSource\": 2,\n" +
            "    \"custRemark\": \"客戶備註測試\",\n" +
            "    \"merchList\": []\n" + // Empty merchList
            "}";

        // Act and Assert: 分別驗證各個備註條件
        mockMvc.perform(post("/subscribe/contract")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_ACCT, acctId)
                .content(createContractJson))
            .andExpect(jsonPath("$.statusCode").value(0))
            .andExpect(jsonPath("$.data.mainContract.plateNo").value(plateNo))
            .andExpect(jsonPath("$.data.orders[0].remarks[*].content", hasItem("客戶備註測試")))
            .andExpect(jsonPath("$.data.orders[0].remarks[*].content", hasItem(containsString("原始收訂車輛"))))
            .andExpect(jsonPath("$.data.orders[0].remarks[*].content", hasItem(containsString("車體描述"))))
            .andExpect(jsonPath("$.data.orders[0].remarks[*].content", hasItem(containsString("有加入用戶收藏清單"))))
            .andExpect(jsonPath("$.data.orders[0].remarks[*].remarkerName", hasItem("sys")));
    }
}