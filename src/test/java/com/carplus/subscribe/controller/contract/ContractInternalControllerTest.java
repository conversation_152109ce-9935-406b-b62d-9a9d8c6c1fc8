package com.carplus.subscribe.controller.contract;

import carplus.common.response.Result;
import carplus.common.utils.BeanUtils;
import carplus.common.utils.DateUtils;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.App;
import com.carplus.subscribe.config.AppProperties;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.db.mysql.dao.CsatQuestRepository;
import com.carplus.subscribe.db.mysql.dao.CsatRepository;
import com.carplus.subscribe.db.mysql.dao.EtagInfoRepository;
import com.carplus.subscribe.db.mysql.dto.OrderPriceInfoDTO;
import com.carplus.subscribe.db.mysql.entity.ETagInfo;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.db.mysql.entity.contract.*;
import com.carplus.subscribe.db.mysql.entity.csat.Csat;
import com.carplus.subscribe.db.mysql.entity.csat.CsatQuest;
import com.carplus.subscribe.db.mysql.entity.invoice.Invoices;
import com.carplus.subscribe.db.mysql.entity.payment.PaymentInfo;
import com.carplus.subscribe.enums.*;
import com.carplus.subscribe.enums.csat.CsatQuestStatus;
import com.carplus.subscribe.enums.csat.CsatStatus;
import com.carplus.subscribe.event.car.CarDepartEvent;
import com.carplus.subscribe.event.car.CarReturnEvent;
import com.carplus.subscribe.event.order.OrderBookingEvent;
import com.carplus.subscribe.event.order.OrderClosedEvent;
import com.carplus.subscribe.event.order.OrderReopenedEvent;
import com.carplus.subscribe.event.order.OrderReturnedEvent;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.feign.FinServiceBusClient;
import com.carplus.subscribe.model.EmpMileageDiscount;
import com.carplus.subscribe.model.authority.MemberInfo;
import com.carplus.subscribe.model.crs.CarBaseInfoSearchResponse;
import com.carplus.subscribe.model.invoice.Invoice;
import com.carplus.subscribe.model.invoice.InvoiceNewRequest;
import com.carplus.subscribe.model.invoice.InvoiceRequest;
import com.carplus.subscribe.model.invoice.v2.InvoiceDataSearchResponse;
import com.carplus.subscribe.model.invoice.v2.InvoiceMasterSearchResponse;
import com.carplus.subscribe.model.lrental.ContractSearchRep;
import com.carplus.subscribe.model.order.CachedReturnEarlyOrder;
import com.carplus.subscribe.model.order.LrentalContractRequest;
import com.carplus.subscribe.model.payment.AdditionalData;
import com.carplus.subscribe.model.payment.PayAuthResultUrl;
import com.carplus.subscribe.model.payment.req.AccountSettlementRequest;
import com.carplus.subscribe.model.payment.req.PayAuthRequest;
import com.carplus.subscribe.model.payment.req.PaymentRequest;
import com.carplus.subscribe.model.payment.resp.AccountSettlementResponse;
import com.carplus.subscribe.model.priceinfo.PriceInfoDetail;
import com.carplus.subscribe.model.priceinfo.PriceInfoInterface;
import com.carplus.subscribe.model.priceinfo.PriceInfoWrapper;
import com.carplus.subscribe.model.priceinfo.resp.OrderPriceInfoResponse;
import com.carplus.subscribe.model.queue.PaymentQueue;
import com.carplus.subscribe.model.request.carwishlist.CarWishlistRequest;
import com.carplus.subscribe.model.request.contract.CreditApprovalRequest;
import com.carplus.subscribe.model.request.contract.InternalContractCreateReq;
import com.carplus.subscribe.model.request.contract.MerchandiseReq;
import com.carplus.subscribe.model.request.contract.OrderRenewRequest;
import com.carplus.subscribe.model.request.depart.CarDepartRequest;
import com.carplus.subscribe.model.request.dropoff.CarDropOffCompleteRequest;
import com.carplus.subscribe.model.request.dropoff.CarDropOffRequest;
import com.carplus.subscribe.model.request.dropoff.OrderCloseAgreeRequest;
import com.carplus.subscribe.model.request.order.LegalOperationRequest;
import com.carplus.subscribe.model.request.order.OrdersCriteria;
import com.carplus.subscribe.model.request.order.UpdateDepartMileageRequest;
import com.carplus.subscribe.model.request.priceinfo.ExtraFeeRequest;
import com.carplus.subscribe.model.request.priceinfo.MerchandiseInfoRequest;
import com.carplus.subscribe.model.request.priceinfo.MileageDiscountRequest;
import com.carplus.subscribe.model.request.priceinfo.OrderPriceInfoCriteriaRequest;
import com.carplus.subscribe.rabbitmq.PaymentListener;
import com.carplus.subscribe.server.AuthorityServer;
import com.carplus.subscribe.server.cars.LrentalServer;
import com.carplus.subscribe.service.*;
import com.carplus.subscribe.utils.CarsUtil;
import com.carplus.subscribe.utils.DateUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.BufferedReader;
import java.io.StringReader;
import java.nio.charset.Charset;
import java.text.NumberFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.carplus.subscribe.enums.LegalOperationReason.RETURNED_WITH_PAID;
import static com.carplus.subscribe.enums.PayFor.Depart;
import static com.carplus.subscribe.enums.PayFor.Return;
import static com.carplus.subscribe.enums.PaymentCategory.PayAuth;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoCategory.*;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoType.Pay;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.*;
import static com.carplus.subscribe.service.OrderService.ORDER_STATE;
import static com.carplus.subscribe.utils.DateUtil.DASH_FORMATTER;
import static com.carplus.subscribe.utils.DateUtil.SLASH_FORMATTER_WITHOUT_TIME;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest(classes = App.class)
@AutoConfigureMockMvc
@Transactional
@EnableAspectJAutoProxy
@Slf4j
class ContractInternalControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private OrderService orderService;

    @Autowired
    private PriceInfoService priceInfoService;

    @Autowired
    private SkuService skuService;

    @Autowired
    private LrentalServer lrentalServer;

    @Autowired
    private AuthorityServer authorityServer;

    @Autowired
    private PaymentListener paymentListener;

    @Autowired
    private CarsService carsService;

    @Autowired
    private CrsService crsService;

    @Autowired
    private InvoiceServiceV2 invoiceService;

    @Autowired
    private PaymentServiceV2 paymentService;

    @Autowired
    private CheckoutService checkoutService;

    @Autowired
    private EtagInfoRepository etagInfoRepository;

    @Autowired
    private ContractLogic contractLogic;

    @Autowired
    private ETagService etagService;

    @Autowired
    private FinServiceBusClient finServiceBusClient;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private CsatRepository csatRepository;

    @Autowired
    private CsatQuestRepository csatQuestRepository;

    @Value("${station.subscribe}")
    private String subscribeStationCode;

    @Value("${lrental.userId}")
    private String lrentalUserId;

    private final String MEMBER_ID = "K2765";
    private final String ORDER_NO = "M202408059608";
    private final String UPDATE_DEPART_MILEAGE_PATH = "/internal/subscribe/{orderNo}/departMileage";
    private final String CARD_KEY = "4048ee192c24f0ab397f5f8ce997d035737412bbef44ebd8c2112a4ed645c3a3";

    private final String CAR_DEPART_FLOW_REQUEST = "{\n" +
        "  \"departMemberId\": \"" + MEMBER_ID + "\"\n" +
        "}";

    private final String CAR_DROP_OFF_COMPLETE_REQUEST = "{\n" +
        "  \"returnMemberId\": \"" + MEMBER_ID + "\"\n" +
        "}";

    private NumberFormat numberFormat = NumberFormat.getNumberInstance();

    @Test
    void queryOrder() throws Exception {
        MvcResult result = mockMvc.perform(get("/internal/subscribe/order/query")
                .param("orderNo", "M202502058581")
                .param("limit", "10")
                .param("skip", "0")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andReturn();

        String jsonResponse = result.getResponse().getContentAsString();
        JsonNode rootNode = objectMapper.readTree(jsonResponse);

        JsonNode listNode = rootNode.path("data").path("page").path("list");
        assertTrue(listNode.isArray() && !listNode.isEmpty(), "訂單列表不應為空");
        JsonNode orderNode = listNode.get(0);

        // 驗證 contractNo
        assertTrue(orderNode.has("contractNo"), "應包含 contractNo 欄位");
        assertNotNull(orderNode.get("contractNo"), "contractNo 不應為 null");

        // 驗證 isEContractSigned
        assertTrue(orderNode.has("isEContractSigned"), "應包含 isEContractSigned 欄位");
        assertNotNull(orderNode.get("isEContractSigned"), "isEContractSigned 不應為 null");
        assertTrue(orderNode.get("isEContractSigned").isBoolean(), "isEContractSigned 應為布林值");

        ArrayNode remarksArray = (ArrayNode) orderNode.path("remarks");

        // 取得 remarks createTime 並存入 List
        List<Long> createTimes = new ArrayList<>();
        for (JsonNode remark : remarksArray) {
            createTimes.add(remark.get("createTime").asLong());
        }

        // 驗證 createTime 是否為降序排序
        assertEquals(createTimes.stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList()), createTimes, "remarks createTime 應為降序排序");
    }

    @Test
    void departCar() throws Exception {

        // 須先建立一筆已訂車的訂單，第一期費用也要先付款 以及 手動開立發票及收支登打，不手動建立車籍契約
        Orders order = orderService.getOrdersByStatus(OrderStatus.BOOKING).stream().max(Comparator.comparing(Orders::getInstantCreateDate))
            .orElseThrow(() -> new SubscribeException(ORDER_NOT_FOUND));
        Contract contract = order.getContract();
        String plateNo = contract.getMainContract().getPlateNo();
        String orderNo = order.getOrderNo();
        CarBaseInfoSearchResponse crsCar = crsService.getCar(plateNo);

        // 建立原始長租契約
        String originalLrentalContractNo = createOriginalLrentalContract(order);
        order = orderService.getOrder(orderNo);
        assertEquals(originalLrentalContractNo, order.getLrentalContractNo());

        // 記錄原始長租契約資訊
        ContractSearchRep originalContract = lrentalServer.getContractInfo(originalLrentalContractNo);
        String originalStartDate = originalContract.getDadt1().trim();
        String originalEndDate = originalContract.getDadt2().trim();

        Thread.sleep(2000);

        // 出車資料確認更新訂單，並設定與原長租契約不同的日期
        CarDepartRequest departRequest = new CarDepartRequest();
        departRequest.setDepartDate(Date.from(order.getExpectStartDate().plus(-10, ChronoUnit.DAYS))); // 設定不同的出車日期
        departRequest.setDepartMileage(crsCar.getCarBase().getKm() + 1);
        departRequest.setPlateNo(plateNo);
        departRequest.setDepartRemark("測試出車備註改儲存至訂單備註");
        ArrayList<String> errorMessages = orderService.departUpdateOrder(orderNo, departRequest, MEMBER_ID);
        assert errorMessages.isEmpty();

        // 出車 - 此時應該會觸發重建長租契約
        MockHttpServletRequestBuilder requestBuilder = post("/internal/subscribe/{orderNo}/depart", orderNo)
            .contentType(MediaType.APPLICATION_JSON)
            .content(CAR_DEPART_FLOW_REQUEST);

        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value("0"));

        // 驗證長租契約已重建
        order = orderService.getOrder(orderNo);
        assertNotNull(order.getLrentalContractNo());
        assertNotEquals(originalLrentalContractNo, order.getLrentalContractNo());

        // 驗證新建長租契約日期是否正確更新
        ContractSearchRep newContract = lrentalServer.getContractInfo(order.getLrentalContractNo());
        // 驗證新契約的起始日期是否與實際出車日期一致
        String newStartDate = newContract.getDadt1().trim();
        String newEndDate = newContract.getDadt2().trim();
        assertEquals(
            DateUtil.transferADDateToMinguoDate(departRequest.getDepartDate().toInstant()),
            newStartDate
        );
        // 驗證新契約的結束日期是否與訂單結束日期一致
        assertEquals(
            DateUtil.transferADDateToMinguoDate(order.getExpectEndDate()),
            newEndDate
        );
        // 確認新契約日期與原契約不同
        assertNotEquals(originalStartDate, newStartDate);
        assertNotEquals(originalEndDate, newEndDate);
        // 驗證出車備註改儲存至訂單備註
        assertThat(order.getRemarks(), hasItem(hasProperty("content", containsString(departRequest.getDepartRemark()))));
        // 驗證新契約業務名稱是否為訂閱營業副理名稱
        MemberInfo memberInfo = authorityServer.getMemberInfos(lrentalUserId).stream().findFirst().orElseThrow(() -> new SubscribeException(MEMBER_INFO_NOT_FOUND));
        assertEquals(memberInfo.getMemberName(), newContract.getDasalena().trim());

        Cars car = carsService.findByPlateNo(plateNo);
        assert car.getCrsCarNo() != null && car.getCrsCarNo() > 0;
        CarBaseInfoSearchResponse carBaseInfoSearchResponse = crsService.getCar(plateNo);
        // 確認出車後車輛里程數已與 CRS 同步
        assert Objects.equals(car.getCurrentMileage(), carBaseInfoSearchResponse.getCarBase().getKm());
    }

    private String createOriginalLrentalContract(Orders order) {
        LrentalContractRequest lrentalContractRequest = new LrentalContractRequest();
        lrentalContractRequest.setOrderNo(order.getOrderNo());
        lrentalContractRequest.setMemo(String.format("格上官網／新單／中古車\n" +
                "%s：%s - %s\n" +
                "%s：%s - %s（租期%d個月）\n",
            order.getContractNo(),
            DateUtil.getFormatString(order.getContract().getExpectStartDate(), SLASH_FORMATTER_WITHOUT_TIME),
            DateUtil.getFormatString(order.getContract().getExpectEndDate(), SLASH_FORMATTER_WITHOUT_TIME),
            order.getOrderNo(),
            DateUtil.getFormatString(order.getExpectStartDate(), SLASH_FORMATTER_WITHOUT_TIME),
            DateUtil.getFormatString(order.getExpectEndDate(), SLASH_FORMATTER_WITHOUT_TIME),
            order.getMonth()));

        Orders updatedOrder = orderService.createLrentalContract(lrentalContractRequest, MEMBER_ID);
        return updatedOrder.getLrentalContractNo();
    }

    @Test
    void returnCar() throws Exception {

        Orders order = orderService.getOrder(ORDER_NO);
        String plateNo = order.getPlateNo();

        MockHttpServletRequestBuilder requestBuilder = post("/internal/subscribe/{orderNo}/return", ORDER_NO)
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
            .contentType(MediaType.APPLICATION_JSON)
            .content(CAR_DROP_OFF_COMPLETE_REQUEST);

        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value("0"));

        Cars car = carsService.findByPlateNo(plateNo);
        assert car.getCrsCarNo() != null && car.getCrsCarNo() > 0;
        CarBaseInfoSearchResponse crsCar = crsService.getCar(plateNo);
        // 確認完成還車後車輛里程數已與 CRS 同步
        assert Objects.equals(car.getCurrentMileage(), crsCar.getCarBase().getKm());
    }

    private String getRequestJson(int month) {
        long expectStartDate = getExpectStartDate();

        return "{\n" +
            "    \"plateNo\": \"RAP-2003\",\n" +
            "    \"carLevel\": 2,\n" +
            "    \"departStationCode\": \"201\",\n" +
            "    \"expectStartDate\": " + expectStartDate + ",\n" +
            "    \"month\": " + month + ",\n" +
            "    \"mainContractNo\": \"\",\n" +
            "    \"disclaimer\": false,\n" +
            "    \"premium\": false,\n" +
            "    \"mileageDiscounts\": null,\n" +
            "    \"yesChargingPoint\": null,\n" +
            "    \"merchList\": [\n" +
            "        {\n" +
            "            \"skuCode\": \"airFresher001\",\n" +
            "            \"quantity\": 2,\n" +
            "            \"actualUnitPrice\": 220\n" + // 設定自訂單價
            "        },\n" +
            "        {\n" +
            "            \"skuCode\": \"recorder001\",\n" +
            "            \"quantity\": 1,\n" +
            "            \"actualUnitPrice\": 2800\n" + // 設定自訂單價
            "        },\n" +
            "        {\n" +
            "            \"skuCode\": \"towel001\",\n" +
            "            \"quantity\": 2\n" + // 未設定 actualUnitPrice,使用原始單價
            "        }\n" +
            "    ]\n" +
            "}";
    }

    private String getRequestJsonWithInvalidActualUnitPrice() {
        long expectStartDate = getExpectStartDate();

        return "{\n" +
            "    \"plateNo\": \"RAP-2003\",\n" +
            "    \"carLevel\": 2,\n" +
            "    \"departStationCode\": \"201\",\n" +
            "    \"expectStartDate\": " + expectStartDate + ",\n" +
            "    \"month\": 6,\n" +
            "    \"mainContractNo\": \"\",\n" +
            "    \"disclaimer\": false,\n" +
            "    \"premium\": false,\n" +
            "    \"mileageDiscounts\": null,\n" +
            "    \"yesChargingPoint\": null,\n" +
            "    \"merchList\": [\n" +
            "        {\n" +
            "            \"skuCode\": \"wipe001\",\n" +
            "            \"quantity\": 2,\n" +
            "            \"actualUnitPrice\": 0\n" + // 設定無效單價
            "        }\n" +
            "    ]\n" +
            "}";
    }

    private long getExpectStartDate() {
        return LocalDateTime.now(DateUtils.ZONE_TPE)
            .plusDays(1)
            .atZone(DateUtils.ZONE_TPE)
            .toInstant()
            .toEpochMilli();
    }

    @Test
    void calculatePrice_6_months() throws Exception {

        int month = 6;
        mockMvc.perform(post("/internal/subscribe/calculate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(getRequestJson(month)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.data.size()").value(getDataSize(month)))
            .andExpect(jsonPath("$.data[?(@.stage == 2 && @.category == 'MonthlyFee')].infoDetail.month").value(3))
            .andExpect(jsonPath("$.data[?(@.category == 'MileageFee')].infoDetail.discountMileage", hasItems(600, 600, 600)));
    }

    @Test
    void calculatePrice_6_months_enableDiscountLevel() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = patch("/internal/subscribe/subscribeLevel")
            .contentType(MediaType.APPLICATION_JSON)
            .content("{\n" +
                "    \"id\": 2,\n" +
                "    \"level\": 2,\n" +
                "    \"discountLevel\": 1,\n" + // 修改對照超激優惠方案為 1
                "    \"securityDeposit\": 10000,\n" +
                "    \"monthlyFee\": 8800,\n" +
                "    \"mileageFee\": 2.8,\n" +
                "    \"autoCredit\": true,\n" +
                "    \"discountMonthlyFee\": 6800,\n" +
                "    \"type\": \"SEASON\"\n" +
                "}");
        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk());

        mockMvc.perform(patch("/internal/subscribe/cars/{plateNo}", "RAP-2087")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\n" +
                    "    \"carState\": \"OLD\",\n" +
                    "    \"carModelCode\": \"S001H\",\n" +
                    "    \"seat\": 5,\n" +
                    "    \"energyType\": \"GASOLINE\",\n" +
                    "    \"fuelType\": \"petrol95\",\n" +
                    "    \"displacement\": 1499,\n" +
                    "    \"currentMileage\": 10,\n" +
                    "    \"equipIds\": null,\n" +
                    "    \"subscribeLevel\": 2,\n" +
                    "    \"tagIds\": [4],\n" +
                    "    \"cnDesc\": null,\n" +
                    "    \"type\": \"sedan\",\n" +
                    "    \"gearType\": \"at\",\n" +
                    "    \"mfgYear\": \"2020\",\n" +
                    "    \"colorDesc\": \"灰\",\n" +
                    "    \"prepWorkdays\": 10,\n" +
                    "    \"isSealandLaunched\": false\n" +
                    "}"))
            .andExpect(status().isOk());

        int month = 6;
        mockMvc.perform(post("/internal/subscribe/calculate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(getRequestJson(month)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.data.size()").value(getDataSize(month)))
            .andExpect(jsonPath("$.data[?(@.category == 'MonthlyFee')].infoDetail.monthlyFee", hasItems(6800, 6800, 6800))) // 啟用超激優惠 月費 8800 -> 6800
            .andExpect(jsonPath("$.data[?(@.category == 'MileageFee')].infoDetail.mileageFee", hasItems(2.24, 2.24, 2.24))); // 啟用超激優惠 里程費 2.8 -> 2.24
    }

    @Test
    void calculatePrice_7_months() throws Exception {

        int month = 7;
        mockMvc.perform(post("/internal/subscribe/calculate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(getRequestJson(month)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.data.size()").value(getDataSize(month)))
            .andExpect(jsonPath("$.data[?(@.stage == 3 && @.category == 'MonthlyFee')].infoDetail.month").value(1))
            .andExpect(jsonPath("$.data[?(@.category == 'MileageFee')].infoDetail.discountMileage", hasItems(200, 200, 200)));
    }

    @Test
    void calculatePrice_8_months() throws Exception {

        int month = 8;
        mockMvc.perform(post("/internal/subscribe/calculate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(getRequestJson(month)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.data.size()").value(getDataSize(month)))
            .andExpect(jsonPath("$.data[?(@.stage == 3 && @.category == 'MonthlyFee')].infoDetail.month").value(2))
            .andExpect(jsonPath("$.data[?(@.category == 'MileageFee')].infoDetail.discountMileage", hasItems(200, 200, 200)));
    }

    @Test
    void calculatePrice_9_months() throws Exception {

        int month = 9;
        mockMvc.perform(post("/internal/subscribe/calculate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(getRequestJson(month)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.data.size()").value(getDataSize(month)))
            .andExpect(jsonPath("$.data[?(@.stage == 3 && @.category == 'MonthlyFee')].infoDetail.month").value(3))
            .andExpect(jsonPath("$.data[?(@.category == 'MileageFee')].infoDetail.discountMileage", hasItems(300, 300, 300)));
    }

    @Test
    void calculatePrice_10_months() throws Exception {

        int month = 10;
        mockMvc.perform(post("/internal/subscribe/calculate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(getRequestJson(month)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.data.size()").value(getDataSize(month)))
            .andExpect(jsonPath("$.data[?(@.stage == 4 && @.category == 'MonthlyFee')].infoDetail.month").value(1))
            .andExpect(jsonPath("$.data[?(@.category == 'MileageFee')].infoDetail.discountMileage", hasItems(300, 300, 300)));
    }

    @Test
    void calculatePrice_11_months() throws Exception {

        int month = 11;
        mockMvc.perform(post("/internal/subscribe/calculate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(getRequestJson(month)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.data.size()").value(getDataSize(month)))
            .andExpect(jsonPath("$.data[?(@.stage == 4 && @.category == 'MonthlyFee')].infoDetail.month").value(2))
            .andExpect(jsonPath("$.data[?(@.category == 'MileageFee')].infoDetail.discountMileage", hasItems(300, 300, 300)));
    }

    @Test
    void calculatePrice_12_months() throws Exception {

        int month = 12;
        int discountMileage = getDiscountMileage(month);
        mockMvc.perform(post("/internal/subscribe/calculate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(getRequestJson(month)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.data.size()").value(getDataSize(month)))
            .andExpect(jsonPath("$.data[?(@.stage == " + getStages(month) + " && @.category == 'MonthlyFee')].infoDetail.month").value(getMonthLastStage(month)))
            .andExpect(jsonPath("$.data[?(@.category == 'MileageFee')].infoDetail.discountMileage", hasItems(discountMileage, discountMileage, discountMileage)));
    }

    @Test
    void calculatePrice_3_to_12_months() throws Exception {

        Map<String, Sku> skuMap = skuService.getMapByCodes(Sets.newHashSet("airFresher001", "recorder001", "towel001"));

        for (int month = 3; month <= 12; month++) {
            int discountMileage = getDiscountMileage(month);
            mockMvc.perform(post("/internal/subscribe/calculate")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(getRequestJson(month)))
                .andExpect(jsonPath("$.statusCode").value(0))
                .andExpect(jsonPath("$..data[?(@.category != 'Merchandise')]", hasSize(getDataSize(month)))) // 排除汽車用品資料
                .andExpect(jsonPath("$.data[?(@.stage == " + getStages(month) + " && @.category == 'MonthlyFee')].infoDetail.month").value(getMonthLastStage(month)))
                .andExpect(jsonPath("$.data[?(@.category == 'MileageFee')].infoDetail.discountMileage", hasItems(discountMileage, discountMileage, discountMileage)))
                // 新增驗證加購汽車用品
                .andExpect(jsonPath("$.data[?(@.category == 'Merchandise')].skuCode", hasItems("airFresher001", "recorder001", "towel001"))) // 驗證商品代碼
                .andExpect(jsonPath("$.data[?(@.category == 'Merchandise')].amount", hasItems(440, 2800, 78))) // 驗證商品總額 (實際單價 * 數量)
                .andExpect(jsonPath("$.data[?(@.category == 'Merchandise')].infoDetail.unitPrice", hasItems(skuMap.get("airFresher001").getUnitPrice(), skuMap.get("recorder001").getUnitPrice(), skuMap.get("towel001").getUnitPrice()))) // 驗證商品原始單價
                .andExpect(jsonPath("$.data[?(@.category == 'Merchandise')].infoDetail.actualUnitPrice", hasItems(220, 2800, 39))) // 驗證商品實際單價
                .andExpect(jsonPath("$.data[?(@.category == 'Merchandise')].infoDetail.quantity", hasItems(2, 1, 2))) // 驗證商品數量
                .andExpect(jsonPath("$.data[?(@.category == 'Merchandise')].categoryName", hasItems("汽車用品", "汽車用品", "汽車用品"))) // 驗證商品 categoryName
                .andExpect(jsonPath("$.data[?(@.category == 'Merchandise')].memo", hasItems("植萃馥郁淨化噴霧", "快譯通原廠行車記錄器", "車用超細纖維擦拭布"))) // 驗證商品 memo
                .andExpect(jsonPath("$.data[?(@.category == 'Merchandise')].formulaDescription", hasItems(
                    String.format("($%sx%d)", numberFormat.format(220), 2),
                    String.format("($%sx%d)", numberFormat.format(2800), 1),
                    String.format("($%sx%d)", numberFormat.format(39), 2)))); // 驗證商品 formulaDescription 應該要以實際單價呈現
        }
    }

    @Test
    void calculatePrice_WithInvalidActualUnitPrice() throws Exception {
        mockMvc.perform(post("/internal/subscribe/calculate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(getRequestJsonWithInvalidActualUnitPrice()))
            .andExpect(status().isBadRequest())
            .andExpect(jsonPath("$.message").value(containsString("商品實際單價必須大於0")));
    }

    private int getDataSize(int month) {
        return 1 + 3 + (int) Math.ceil(month / 3.0d) * 2;
    }

    private int getStages(int month) {
        return (int) Math.ceil(month / 3.0d);
    }

    private int getMonthLastStage(int month) {
        return month % 3 == 0 ? 3 : month % 3;
    }

    private int getDiscountMileage(int month) {
        switch (month / 3) {
            case 2:
            case 3:
                return 600;
            case 4:
                return 1200;
            default:
                return 0;
        }
    }

    @Test
    void returnCarConfirm() throws Exception {
        String orderNo = "M202501101237";
        CarDropOffRequest request = new CarDropOffRequest();
        request.setReturnDate(new Date());
        request.setReturnMileage(75000);
        request.setReturnRemark("測試還車備註改儲存至訂單備註");

        mockMvc.perform(patch("/internal/subscribe/{orderNo}/return", orderNo)
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk());

        Orders order = orderService.getOrder(orderNo);
        // 驗證還車備註改儲存至訂單備註
        assertThat(order.getRemarks(), hasItem(hasProperty("content", containsString(request.getReturnRemark()))));
    }

    @Test
    void dropOffCarDiscounts() throws Exception {

        String requestJson = "[\n" +
            "    {\n" +
            "        \"priceInfoPayId\":76838,\n" +
            "        \"category\": \"EmpDiscount\",\n" +
            "        \"discount\": 2000,\n" +
            "        \"reason\": \"test monthlyFee\",\n" +
            "        \"afterDiscountAmount\": null,\n" +
            "        \"originAmount\": null,\n" +
            "        \"agree\": null\n" +
            "    },\n" +
            "    {\n" +
            "        \"priceInfoPayId\":76838,\n" +
            "        \"category\": \"EmpDiscount\",\n" +
            "        \"discount\": 3000,\n" +
            "        \"reason\": \"test monthlyFee\",\n" +
            "        \"afterDiscountAmount\": null,\n" +
            "        \"originAmount\": null,\n" +
            "        \"agree\": null\n" +
            "    },\n" +
            "    {\n" +
            "        \"priceInfoPayId\":76842,\n" +
            "        \"category\": \"EmpDiscount\",\n" +
            "        \"discount\": 3000,\n" +
            "        \"reason\": \"test monthlyFee\",\n" +
            "        \"afterDiscountAmount\": null,\n" +
            "        \"originAmount\": null,\n" +
            "        \"agree\": null\n" +
            "    }\n" +
            "]";

        mockMvc.perform(post("/internal/subscribe/{orderNo}/returnCarDiscounts", "M202408059608")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .content(requestJson))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.data[-3:].category", everyItem(is("EmpDiscount"))))
            .andExpect(jsonPath("$.data[-3:].refPriceInfoNo", everyItem(anyOf(is(76838), is(76842)))));
    }

    @Test
    void legalOperation_overdueNoReturn_shouldSuccess() throws Exception {
        testLegalOperation(
            "M202411074263",
            LegalOperationReason.OVERDUE_NO_RETURN,
            10000,
            Collections.emptyList(),
            null,
            false
        );
    }

    @Test
    void legalOperation_returnedWithDamage_shouldSuccess() throws Exception {
        testLegalOperation(
            "M202502085233",
            LegalOperationReason.RETURNED_WITH_DAMAGE,
            9903,
            Collections.singletonList(82798),
            CarDefine.Launched.accident,
            true
        );
    }

    @Test
    void legalOperation_returnedWithUnpaid_shouldSuccess() throws Exception {
        testLegalOperation(
            "M202504308183",
            LegalOperationReason.RETURNED_WITH_UNPAID,
            8960,
            Collections.singletonList(84765),
            null,
            true
        );
    }

    @Test
    void legalOperation_returnedWithUnpaidAndDamage_shouldSuccess() throws Exception {
        testLegalOperation(
            "M202411072799",
            LegalOperationReason.RETURNED_WITH_UNPAID_AND_DAMAGE,
            8000,
            Collections.emptyList(),
            CarDefine.Launched.accident,
            true
        );
    }

    @Test
    void legalOperation_returnedWithPaid_shouldSuccess() throws Exception {
        testLegalOperation(
            "M202411138402",
            RETURNED_WITH_PAID,
            10000,
            Collections.emptyList(),
            null,
            true
        );
    }

    private void testLegalOperation(
        String orderNo,
        LegalOperationReason reason,
        int forfeitedForRentalAmount,
        List<Integer> etagPriceInfoIdsToWriteOff,
        CarDefine.Launched expectedLaunched,
        boolean needReturnMember
    ) throws Exception {
        // Arrange
        Orders order = orderService.getOrdersByStatus(OrderStatus.DEPART).stream()
            .filter(o -> RenewType.AUTO_RENEW != o.getRenewType()
                && RenewType.RENEW != o.getRenewType()
                && o.getOrderNo().equals(orderNo))
            .findFirst()
            .orElseThrow(() -> new SubscribeException(ORDER_NOT_FOUND));

        MainContract mainContract = order.getContract().getMainContract();
        int paidSecurityDeposit = mainContract.getOriginalPriceInfo()
            .getSecurityDepositInfo()
            .getPaidSecurityDeposit();

        // 初始化沖銷 etag 金額
        int etagWriteOffAmount = 0;

        LegalOperationRequest request = new LegalOperationRequest();
        request.setReason(reason);
        request.setForfeitedForRentalAmount(forfeitedForRentalAmount);
        PriceInfoWrapper priceInfoWrapper = priceInfoService.getPriceInfoWrapper(orderNo);
        if (CollectionUtils.isNotEmpty(etagPriceInfoIdsToWriteOff)) {
            request.setEtagPriceInfoIdsToWriteOff(etagPriceInfoIdsToWriteOff);
            etagWriteOffAmount = priceInfoWrapper.getByCategoryType(ETag, Pay).getList().stream()
                .filter(opi -> !opi.isPaid() && etagPriceInfoIdsToWriteOff.contains(opi.getId()))
                .mapToInt(PriceInfoInterface::getActualPrice)
                .sum();
        }

        if (needReturnMember) {
            request.setReturnDate(new Date());
            request.setReturnMileage(order.getDepartMileage() + 2000);
            request.setReturnMemberId(MEMBER_ID);
        }

        // Act
        mockMvc.perform(patch("/internal/subscribe/{orderNo}/legalOperation", order.getOrderNo())
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk());

        // Assert
        // 1. 驗證訂單狀態
        order = orderService.getOrder(order.getOrderNo());
        int expectedOrderStatus = orderService.inferTargetOrderStatus(order, reason);
        assertEquals(expectedOrderStatus, order.getStatus());

        // 2. 驗證車輛狀態
        Cars car = carsService.findByPlateNo(order.getPlateNo());
        assertEquals(reason.getCarStatus().getCode(), car.getCarStatus());
        if (expectedLaunched != null) {
            assertEquals(expectedLaunched, car.getLaunched());
        }

        // 3. 驗證保證金沒收金額 (若 order 為續約單，須先取得母單來做以下操作)
        if (!order.getIsNewOrder()) {
            order = mainContract.getContracts().stream()
                .map(c -> orderService.getOrdersByContractNo(c.getContractNo()))
                .flatMap(List::stream)
                .min(Comparator.comparing(Orders::getCreateDate))
                .orElseThrow(() -> new SubscribeException(ORDER_NOT_FOUND));
        }
        List<OrderPriceInfo> orderPriceInfoList = priceInfoService.getPriceInfosByOrder(order.getOrderNo());

        // 沒收金額 = 沒收租金金額 + 沖銷 etag 金額
        int forfeitedAmount = forfeitedForRentalAmount + etagWriteOffAmount;
        if (forfeitedAmount == paidSecurityDeposit) {
            // 當 沒收金額比例 = 100% 時，應該只有一筆 Others 類別的紀錄（原保證金紀錄被轉換）
            OrderPriceInfo othersOrderPriceInfo = orderPriceInfoList.stream()
                .filter(info -> info.getCategory() == Others && info.getType() == Pay.getCode())
                .findFirst()
                .orElseThrow(() -> new SubscribeException(SECURITY_DEPOSIT_PRICE_INFO_NOT_FOUND));

            assertEquals(forfeitedForRentalAmount, othersOrderPriceInfo.getAmount());
            assertEquals(forfeitedForRentalAmount, othersOrderPriceInfo.getReceivedAmount());

            // 確認沒有 SecurityDeposit 類別的紀錄
            assertTrue(orderPriceInfoList.stream()
                .noneMatch(info -> info.getCategory() == SecurityDeposit && info.getType() == Pay.getCode()));
        } else if (forfeitedAmount > 0 && forfeitedAmount < paidSecurityDeposit) {
            // 當 沒收金額比例 < 100% 時，應該有兩筆紀錄，一筆 Others 類別，一筆 SecurityDeposit 類別
            OrderPriceInfo othersOrderPriceInfo = orderPriceInfoList.stream()
                .filter(info -> info.getCategory() == Others && info.getType() == Pay.getCode())
                .findFirst()
                .orElseThrow(() -> new SubscribeException(SECURITY_DEPOSIT_PRICE_INFO_NOT_FOUND));

            assertEquals(forfeitedForRentalAmount, othersOrderPriceInfo.getAmount());
            assertEquals(forfeitedForRentalAmount, othersOrderPriceInfo.getReceivedAmount());

            OrderPriceInfo securityDepositOrderPriceInfo = orderPriceInfoList.stream()
                .filter(info -> info.getCategory() == SecurityDeposit && info.getType() == Pay.getCode())
                .findFirst()
                .orElseThrow(() -> new SubscribeException(SECURITY_DEPOSIT_PRICE_INFO_NOT_FOUND));

            assertEquals(paidSecurityDeposit - forfeitedAmount, securityDepositOrderPriceInfo.getAmount());
            assertEquals(paidSecurityDeposit - forfeitedAmount, securityDepositOrderPriceInfo.getReceivedAmount());
        } else if (forfeitedAmount == 0) {
            // 當 沒收金額比例 = 0% 時，應該只有一筆 SecurityDeposit 類別的紀錄
            OrderPriceInfo securityDepositOrderPriceInfo = orderPriceInfoList.stream()
                .filter(info -> info.getCategory() == SecurityDeposit && info.getType() == Pay.getCode())
                .findFirst()
                .orElseThrow(() -> new SubscribeException(SECURITY_DEPOSIT_PRICE_INFO_NOT_FOUND));

            assertEquals(paidSecurityDeposit, securityDepositOrderPriceInfo.getAmount());
            assertEquals(paidSecurityDeposit, securityDepositOrderPriceInfo.getReceivedAmount());
        }

        // 4. 驗證訂單備註
        String expectedRemarkContent = String.format("完成法務作業 (事由：%s)，保證金%s %s",
            reason.getDescription(),
            reason == RETURNED_WITH_PAID ? "轉收入" : "沒收",
            String.format("$%,d (租金 $%,d + etag $%,d)", forfeitedAmount, forfeitedForRentalAmount, etagWriteOffAmount));
        assertTrue(order.getRemarks().stream()
            .anyMatch(remark -> expectedRemarkContent.equals(remark.getContent())));

        // 5. 驗證發票開立
        if (forfeitedForRentalAmount > 0) {
            List<Invoices> invoices = invoiceService.getInvoice(orderNo);
            assertTrue(invoices.stream()
                .anyMatch(invoice -> invoice.getAmount() == forfeitedForRentalAmount));
        }

        // 6. 驗證 etag 費用及 ETagInfo
        if (CollectionUtils.isNotEmpty(etagPriceInfoIdsToWriteOff)) {
            orderPriceInfoList.stream().filter(opi -> etagPriceInfoIdsToWriteOff.contains(opi.getId()))
                .forEach(opi -> assertTrue(opi.isPaid()));
            etagInfoRepository.getETagInfosByOrderNo(orderNo).stream().filter(etag -> etagPriceInfoIdsToWriteOff.contains(etag.getOrderPriceInfoId()))
                .forEach(etag -> assertTrue(etag.isPaymentCompleted()));
        }
    }

    @Test
    void calculateRenewPrice_shouldHaveInsuranceForEachStage() throws Exception {
        // Arrange
        String orderNo = "M202411140724";
        Orders order = orderService.getOrder(orderNo);
        assert order.getMonth() == 6; // 原訂單租期 6 個月
        OrderPriceInfoCriteriaRequest request = OrderPriceInfoCriteriaRequest.builder().category(Collections.singletonList(Insurance)).build();
        assert priceInfoService.getPriceInfosByOrder(orderNo, request).stream().findFirst().isPresent();

        String renewRequestJson = String.format("{\"acctId\": %d, \"month\": %d}", order.getContract().getMainContract().getAcctId(), 9); // 續約租期 9 個月

        // Act & Assert
        mockMvc.perform(post("/internal/subscribe/{orderNo}/calculateRenew", orderNo)
                .contentType(MediaType.APPLICATION_JSON)
                .content(renewRequestJson))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.data[?(@.category == 'Insurance')]", hasSize(3))); // 應該有 3 個 stage 的 Insurance
    }

    @Test
    void findOrders() throws Exception {
        mockMvc.perform(get("/internal/subscribe/{orderNo}", "M202411253893")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andDo(print());
    }

    @Test
    void getMainContractAndSubInfosV2() throws Exception {
        mockMvc.perform(get("/internal/subscribe/v2/mainContract/{mainContractNo}", "U2025092600903")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andDo(print());
    }

    @Test
    @DisplayName("更新出車里程數失敗 - 訂單尚未出車")
    void updateDepartMileage_Failure_OrderNotDeparted() throws Exception {
        // Arrange
        String orderNo = "M202412240930"; // 請替換成符合條件的訂單編號
        int newDepartMileage = 72500;

        UpdateDepartMileageRequest request = new UpdateDepartMileageRequest();
        request.setNewDepartMileage(newDepartMileage);

        // Act & Assert
        mockMvc.perform(patch(UPDATE_DEPART_MILEAGE_PATH, orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value(FORBIDDEN.getCode()))
            .andExpect(jsonPath("$.message").value(ORDER_STATUS_NOT_DEPART.getMsg()));
    }

    @Test
    @DisplayName("成功更新出車里程數 - 新訂單且已出車未還車")
    void updateDepartMileage_Success() throws Exception {
        // Arrange
        String orderNo = "M202501148266"; // 請替換成符合條件的訂單編號 (已執行還車資料確認 && 第一筆里程費尚未付款 && 尚未確認還車)
        int newDepartMileage = 75500;

        UpdateDepartMileageRequest request = new UpdateDepartMileageRequest();
        request.setNewDepartMileage(newDepartMileage);
        request.setMsgForCustomer(String.format("實際出車里程為 %d km。", newDepartMileage));

        // Act & Assert
        mockMvc.perform(patch(UPDATE_DEPART_MILEAGE_PATH, orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"));

        // 驗證訂單出車里程是否更新
        Orders updatedOrder = orderService.getOrder(orderNo);
        assertEquals(newDepartMileage, updatedOrder.getDepartMileage());

        // 驗證備註是否正確新增
        updatedOrder.getRemarks().stream()
            .filter(remark -> remark.getContent().matches("實際出車里程異動：調整前 \\d+ km；調整後 \\d+ km"))
            .findFirst()
            .orElseThrow(() -> new SubscribeException(ORDER_REMARK_NOT_FOUND));
        // 驗證留言給客戶是否正確新增
        assertEquals(String.format("實際出車里程為 %d km。", newDepartMileage), updatedOrder.getContract().getMainContract().getMsgForCust().get(0).getContent());

        // 驗證里程費用資訊是否正確更新
        PriceInfoWrapper priceInfoWrapper = priceInfoService.getPriceInfoWrapper(orderNo).getByCategoryType(MileageFee, Pay);
        OrderPriceInfo updatedMileageFee = priceInfoWrapper.getList().stream()
            .filter(mileageFee -> mileageFee.getStage() == 1)
            .findFirst()
            .orElseThrow(() -> new SubscribeException(ORDER_PRICE_INFO_FIRST_STAGE_MILEAGE_NOT_FOUND));

        PriceInfoDetail updatedDetail = updatedMileageFee.getInfoDetail();

        // 驗證起始里程是否更新
        assertEquals(newDepartMileage, updatedDetail.getStartMileage());

        // 驗證總里程是否正確計算
        int expectedTotalMileage = Math.max(updatedDetail.getEndMileage() - newDepartMileage - updatedDetail.getDiscountMileage(), 0);
        assertEquals(expectedTotalMileage, updatedDetail.getTotalMileage());

        // 驗證費用金額是否正確計算
        int expectedAmount = (int) Math.round(updatedDetail.getMileageFee() * expectedTotalMileage);
        assertEquals(expectedAmount, updatedMileageFee.getAmount());
    }

    /**
     * * 注意事項：
     * - 使用 @Transactional(isolation = Isolation.READ_COMMITTED)，確保測試方法在 transaction 中執行。
     * - 使用 `OrderTestContext` 類來管理測試過程中需要共享的上下文資料。
     * - 為了使測試能夠順利進行，需要在測試前先將以下程式碼修改：
     * 1. {@link com.carplus.subscribe.service.CheckoutService#checkOut(java.lang.String)} 和
     * {@link com.carplus.subscribe.service.CheckoutService#checkOut(com.carplus.subscribe.db.mysql.entity.contract.Orders)}
     * 方法上的 `@Transactional` 註解，移除 `propagation = Propagation.REQUIRES_NEW` 屬性。
     * 原因： 移除 `Propagation.REQUIRES_NEW` 是為了讓 `CheckoutService` 的 `checkOut` 方法與呼叫它的方法共享同一個 transaction 。
     * 2. {@link com.carplus.subscribe.service.InvoiceServiceV2#createInvoice} 中的 `Invoices invoice = self.create(orders, user, subscribeStationCode, payFor, request, memberId);`
     * 修改為 `Invoices invoice = create(orders, user, subscribeStationCode, payFor, request, memberId);`
     * 原因： `self.create()` 是內部方法調用，在 Spring 的 AOP 代理機制下，直接的內部方法調用不會被 transaction 切面增強，
     * 導致 `create` 方法可能沒有在 transaction 中執行，修改為直接調用 `create()` 方法 (假設 `createInvoice` 方法本身是 transaction 方法) 可以確保 `create` 方法參與到外部方法 (`createInvoice`) 的 transaction 中。
     * 3. {@link com.carplus.subscribe.service.InvoiceServiceV2#setCheckout} 方法上的 `@Transactional` 註解，移除 `propagation = Propagation.REQUIRES_NEW` 屬性。
     * 原因： 與 `CheckoutService` 類似，移除 `Propagation.REQUIRES_NEW` 是為了讓 `setCheckout` 方法與呼叫它的方法共享同一個 transaction
     */
    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    @DisplayName("更新出車里程數失敗 - 異動出車里程不應大於第一期里程費結算里程")
    void updateDepartMileage_Failure_UpdatedDepartMileageShouldNotGreaterThanFirstStageMileageFeeEndMileage() throws Exception {
        // Arrange
        OrderTestContext context = new OrderTestContext();

        fromCreateOrderToReturnCarConfirm(context);

        // 開始異動出車里程(大於結算里程)
        int newDepartMileage = context.carDropOffRequest.getReturnMileage() + 1000;
        UpdateDepartMileageRequest updateDepartMileageRequest = new UpdateDepartMileageRequest();
        updateDepartMileageRequest.setNewDepartMileage(newDepartMileage);

        // Act & Assert
        mockMvc.perform(patch(UPDATE_DEPART_MILEAGE_PATH, context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateDepartMileageRequest)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value(UPDATED_DEPART_MILEAGE_SHOULD_NOT_GREATER_THAN_DETAIL_END_MILEAGE.getCode()))
            .andExpect(jsonPath("$.message").value(UPDATED_DEPART_MILEAGE_SHOULD_NOT_GREATER_THAN_DETAIL_END_MILEAGE.getMsg()));
    }

    @Test
    @DisplayName("更新出車里程數失敗 - 第一筆里程費已付款")
    void updateDepartMileage_Failure_FirstMileageFeePaid() throws Exception {
        // Arrange
        String orderNo = "M202412240930"; // 請替換成第一筆里程費已付款的訂單編號
        int newDepartMileage = 72500;

        UpdateDepartMileageRequest request = new UpdateDepartMileageRequest();
        request.setNewDepartMileage(newDepartMileage);

        // Act & Assert
        mockMvc.perform(patch(UPDATE_DEPART_MILEAGE_PATH, orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value(FORBIDDEN.getCode()))
            .andExpect(jsonPath("$.message").value(ORDER_PRICE_INFO_FIRST_STAGE_MILEAGE_ALREADY_PAID.getMsg()));
    }

    @Test
    @DisplayName("更新出車里程數失敗 - 訂單已還車")
    void updateDepartMileage_Failure_OrderReturned() throws Exception {
        // Arrange
        String orderNo = "M202412240930"; // 請替換成符合條件的訂單編號
        int newDepartMileage = 72500;

        UpdateDepartMileageRequest request = new UpdateDepartMileageRequest();
        request.setNewDepartMileage(newDepartMileage);

        // Act & Assert
        mockMvc.perform(patch(UPDATE_DEPART_MILEAGE_PATH, orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value(FORBIDDEN.getCode()))
            .andExpect(jsonPath("$.message").value(ORDER_STATUS_NOT_DEPART.getMsg()));
    }

    @Test
    void shouldCreateContractAndExportCsv() throws Exception {
        // 先將車輛加入收藏清單
        String plateNo = "RDJ-7801";
        Integer acctId = 33456914;
        CarWishlistRequest wishlistRequest = new CarWishlistRequest();
        wishlistRequest.setPlateNo(plateNo);

        mockMvc.perform(post("/subscribe/carWishlist")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_ACCT, acctId)
                .content(objectMapper.writeValueAsString(wishlistRequest)))
            .andExpect(status().isOk());

        // 建立合約
        String createContractJson = "{\n" +
            "    \"plateNo\": \"" + plateNo + "\",\n" + // 車籍已補上車體描述
            "    \"carLevel\": 9,\n" +
            "    \"departStationCode\": \"201\",\n" +
            "    \"returnStationCode\": \"201\",\n" +
            "    \"expectStartDate\": " + getExpectStartDate() + ",\n" +
            "    \"month\": 12,\n" +
            "    \"disclaimer\": false,\n" +
            "    \"premium\": false,\n" +
            "    \"invoice\": {\n" +
            "        \"category\": 5,\n" +
            "        \"id\": \"A193477449\",\n" +
            "        \"title\": \"李志宏\",\n" +
            "        \"type\": 2\n" +
            "    },\n" +
            "    \"custSource\": 2,\n" +
            "    \"custRemark\": \"客戶備註測試\",\n" +
            "    \"acctId\": " + acctId + ",\n" +
            "    \"merchList\": [\n" +
            "        {\n" +
            "            \"skuCode\": \"wipe001\",\n" +
            "            \"quantity\": 2,\n" +
            "            \"actualUnitPrice\": 360\n" + // 加入自訂單價
            "        },\n" +
            "        {\n" +
            "            \"skuCode\": \"sprayCleaner001\",\n" +
            "            \"quantity\": 2,\n" +
            "            \"actualUnitPrice\": null\n" + // 使用商品原始單價
            "        },\n" +
            "        {\n" +
            "            \"skuCode\": \"voucher001\",\n" +
            "            \"quantity\": 1,\n" +
            "            \"actualUnitPrice\": 19000\n" + // 加入自訂單價
            "        }\n" +
            "    ]\n" +
            "}";

        // 執行建立合約
        MvcResult createResult = mockMvc.perform(post("/internal/subscribe/contract")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .content(createContractJson))
            .andExpect(jsonPath("$.statusCode").value(0))
            .andExpect(jsonPath("$.statusCode").value(0))
            .andExpect(jsonPath("$.data.mainContract.plateNo").value(plateNo))
            .andExpect(jsonPath("$.data.orders[0].remarks[*].content", hasItem("客戶備註測試")))
            .andExpect(jsonPath("$.data.orders[0].remarks[*].content", hasItem(containsString("原始收訂車輛"))))
            .andExpect(jsonPath("$.data.orders[0].remarks[*].content", hasItem(containsString("有加入用戶收藏清單"))))
            .andExpect(jsonPath("$.data.orders[0].remarks[*].content", hasItem(containsString("車體描述"))))
            .andReturn();

        // 準備查詢 CSV 的請求
        JsonNode rootNode = objectMapper.readTree(createResult.getResponse().getContentAsString());
        JsonNode orderNode = rootNode.get("data").get("orders");
        String orderNo = orderNode.get(0).get("orderNo").asText();
        OrdersCriteria queryRequest = new OrdersCriteria();
        queryRequest.setOrderNo(orderNo);

        // 執行 CSV 匯出
        MvcResult csvResult = mockMvc.perform(get("/internal/subscribe/order/csv")
                .contentType(MediaType.APPLICATION_JSON)
                .content(new ObjectMapper().writeValueAsString(queryRequest)))
            .andExpect(status().isOk())
            .andExpect(header().string("Content-Disposition",
                matchesPattern("attachment; filename=SUB_order_\\{\\d{8}\\}.csv")))
            .andReturn();

        // 驗證 CSV 內容
        byte[] content = csvResult.getResponse().getContentAsByteArray();
        String csvContent = new String(content, Charset.forName("big5"));
        BufferedReader reader = new BufferedReader(new StringReader(csvContent));

        // 讀取標題行
        String headerLine = reader.readLine();
        assertTrue(headerLine.contains("加購商品明細"));
        String[] headers = headerLine.split(",");
        int remarkColumnIndex = -1;
        for (int i = 0; i < headers.length; i++) {
            if (headers[i].trim().equals("訂單備註")) {
                remarkColumnIndex = i;
                break;
            }
        }
        if (remarkColumnIndex == -1) {
            fail("CSV header does not contain '訂單備註' column");
        }

        // 讀取資料行並驗證加購商品明細和收藏清單備註
        String dataLine;
        boolean foundMerchandise = false;
        boolean foundWishlistRemark = false;
        List<LocalDateTime> remarkTimestamps = new ArrayList<>();

        while ((dataLine = reader.readLine()) != null) {
            String[] cells = dataLine.split(",");
            if (!foundMerchandise && dataLine.contains("懶人必備專業車用濕巾($360) x 2;玻璃無痕速淨噴霧($388) x 2;G'ZOX石英鍍膜禮券($19,000) x 1")) {
                foundMerchandise = true;
            }

            if (cells.length > remarkColumnIndex) {
                String remarkCell = cells[remarkColumnIndex];
                if (StringUtils.isNotBlank(remarkCell)) {
                    // 檢查是否包含收藏清單相關備註
                    if (remarkCell.contains("原始收訂車輛") && remarkCell.contains("有加入用戶收藏清單")) {
                        foundWishlistRemark = true;
                    }

                    // 正則表達式驗證格式: yyyy-mm-dd HH:MM {備註者名稱}：{備註內容}
                    String expectedRemarkPattern = "\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2} .*：.*";
                    assertTrue(remarkCell.matches(expectedRemarkPattern), "CSV 訂單備註 should match expected format: yyyy-mm-dd HH:MM {備註者名稱}：{備註內容}");

                    // 解析時間並存入列表
                    String timestampStr = remarkCell.substring(0, 16); // 取 yyyy-MM-dd HH:mm
                    LocalDateTime timestamp = LocalDateTime.parse(timestampStr, DASH_FORMATTER);
                    remarkTimestamps.add(timestamp);
                }
            } else {
                fail("Data line does not have enough columns to contain '訂單備註'");
            }
        }

        assertTrue(foundMerchandise, "CSV should contain correct merchandise details");
        assertTrue(foundWishlistRemark, "CSV should contain wishlist remark: '有加入用戶收藏清單'");

        // 驗證備註排序是否為降序（最新的在前面）
        for (int i = 1; i < remarkTimestamps.size(); i++) {
            assertTrue(remarkTimestamps.get(i - 1).isAfter(remarkTimestamps.get(i)) ||
                    remarkTimestamps.get(i - 1).isEqual(remarkTimestamps.get(i)),
                "備註的 createTime 應該是降序排列");
        }
    }

    @Test
    void msgForCustomer_Success_CustomerCanReadIt() throws Exception {
        // Arrange
        String orderNo = "M202412301946";
        String msgForCustomer = "This is a test message for customer.";
        String updateMsgForCustomerRequestBody = String.format("{\"content\":\"%s\"}", msgForCustomer);

        // Act: 執行更新訊息的 API
        mockMvc.perform(patch("/internal/subscribe/{orderNo}/msgForCustomer", orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(updateMsgForCustomerRequestBody))
            .andExpect(status().isOk());

        // Act: 執行客戶查詢訂單資訊的 API
        MvcResult result = mockMvc.perform(get("/subscribe/mainContract?contractStatuses=CREATE,GOING&detail=true&skip=0&limit=10")
                .header(CarPlusConstant.AUTH_HEADER_ACCT, 33456914))
            .andExpect(status().isOk())
            .andReturn();

        // Assert: 驗證客戶是否能讀取到正確的訊息
        String responseJson = result.getResponse().getContentAsString();
        JsonNode rootNode = objectMapper.readTree(responseJson);
        JsonNode dataNode = rootNode.path("data");
        JsonNode listNode = dataNode.path("list");

        boolean found = false;
        for (JsonNode mainContractNode : listNode) {
            JsonNode contractsNode = mainContractNode.path("contracts");
            for (JsonNode contractNode : contractsNode) {
                JsonNode orderResponsesNode = contractNode.path("orderResponses");
                for (JsonNode orderResponseNode : orderResponsesNode) {
                    if (orderResponseNode.path("orderNo").asText().equals(orderNo)) {
                        JsonNode msgForCustomerNode = mainContractNode.path("msgForCust");
                        if (msgForCustomerNode.isArray() && !msgForCustomerNode.isEmpty()) {
                            assertEquals(msgForCustomer, msgForCustomerNode.get(0).path("content").asText());
                            found = true;
                            break;
                        }
                    }
                }
                if (found) {
                    break;
                }
            }
            if (found) {
                break;
            }
        }
        assertTrue(found, "Order with specified orderNo not found in the response.");
    }

    @Test
    void withdrawMsgForCustomer_Success_CustomerCanNotReadIt() throws Exception {
        // Arrange
        String orderNo = "M202412301946";

        // Act: 執行清除訊息的 API
        mockMvc.perform(delete("/internal/subscribe/{orderNo}/withdrawMsgForCustomer", orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk());

        // Act: 執行客戶查詢訂單資訊的 API
        MvcResult result = mockMvc.perform(get("/subscribe/mainContract?contractStatuses=CREATE,GOING&detail=true&skip=0&limit=10")
                .header(CarPlusConstant.AUTH_HEADER_ACCT, 33456914))
            .andExpect(status().isOk())
            .andReturn();

        // Assert: 驗證客戶是否無法讀取到訊息
        String responseJson = result.getResponse().getContentAsString();
        JsonNode rootNode = objectMapper.readTree(responseJson);
        JsonNode dataNode = rootNode.path("data");
        JsonNode listNode = dataNode.path("list");

        boolean found = false;
        for (JsonNode mainContractNode : listNode) {
            JsonNode contractsNode = mainContractNode.path("contracts");
            for (JsonNode contractNode : contractsNode) {
                JsonNode orderResponsesNode = contractNode.path("orderResponses");
                for (JsonNode orderResponseNode : orderResponsesNode) {
                    if (orderResponseNode.path("orderNo").asText().equals(orderNo)) {
                        JsonNode msgForCustomerNode = mainContractNode.path("msgForCust");
                        if (msgForCustomerNode.isMissingNode() || msgForCustomerNode.isNull() || (msgForCustomerNode.isArray() && msgForCustomerNode.isEmpty())) {
                            found = true;
                        } else if (msgForCustomerNode.isArray()) {
                            for (JsonNode messageNode : msgForCustomerNode) {
                                assertTrue(messageNode.path("content").isMissingNode() || messageNode.path("content").isNull(), "Customer message content should be null or missing.");
                            }
                            found = true;
                        }
                        break;
                    }
                }
                if (found) {
                    break;
                }
            }
            if (found) {
                break;
            }
        }
        assertTrue(found, "Order with specified orderNo not found in the response.");
    }

    private MvcResult getPayResult(int amount, String orderNo, boolean isSecurityDeposit, PayFor payFor, String payPath, String mainContractNo) throws Exception {
        PayAuthRequest payAuthRequest = new PayAuthRequest();
        payAuthRequest.setAmount(amount);
        payAuthRequest.setCardKey(CARD_KEY); // 從 `int-payment`.payment_cards 取得 (memberId = '33456914')
        payAuthRequest.setBusinessType("SUBV2");
        payAuthRequest.setDefaultCard("Y");
        payAuthRequest.setPayFrom(PayFrom.Internet.getCode());
        payAuthRequest.setPaymentType(PaymentType.CreditCard.getCode());
        PayAuthResultUrl payAuthResultUrl = new PayAuthResultUrl();
        payAuthResultUrl.setFrontendRedirectUrl(String.format("%s/subscription/payment_pay?orderNo=%s&isSecurityDeposit=%b", AppProperties.getOfficialHost(), orderNo, isSecurityDeposit));
        payAuthRequest.setResultUrl(payAuthResultUrl);
        payAuthRequest.setType(PayAuthRequest.PayType.card);
        payAuthRequest.setPayFor(payFor.name());

        return mockMvc.perform(post("/subscribe/" + payPath, payFor == PayFor.SecurityDeposit ? mainContractNo : orderNo)
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_ACCT, 33456914)
                .header(CarPlusConstant.AUTH_HEADER_SYSTEM_KIND, CustSource.WEB.getSystemKind())
                .header("X-CARPLUS-ROUTE", "be-int-gateway")
                .header(CarPlusConstant.AUTH_HEADER_PLATFORM, CustSource.WEB.name())
                .header("X-ROLE", "MEMBER")
                .content(objectMapper.writeValueAsString(payAuthRequest)))
            .andExpect(jsonPath("$.statusCode").value(0))
            .andReturn();
    }

    private void validateOtp(String paymentUrl) {
        // TapPay otp validate
        RestTemplate restTemplate = new RestTemplate();

        // 設置請求頭
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        // 設置請求參數
        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add("pin", "1234567");
        map.add("urlId", paymentUrl.substring(paymentUrl.lastIndexOf("/") + 1));

        // 創建請求實體
        HttpEntity<MultiValueMap<String, String>> otpValidateRequest = new HttpEntity<>(map, headers);

        // 發送請求
        ResponseEntity<String> response = restTemplate.postForEntity(
            "https://sandbox-redirect.tappaysdk.com/redirect/three-domain-secure/otp-validate",
            otpValidateRequest,
            String.class
        );

        // 驗證響應
        assertTrue(response.getStatusCode().is3xxRedirection());
        System.out.println("Response body: " + response.getBody());
    }

    private static class OrderTestContext {
        Cars idleCar;
        InternalContractCreateReq contractCreateReq;
        MvcResult createContractResult;
        String orderNo;
        Orders order;
        String mainContractNo;
        MainContract mainContract;
        int realSecurityDepositAmount;
        JsonNode payForSecurityDepositDataNode;
        String paySecurityDepositUrl;
        String originalLrentalContractNo;
        String recreatedLrentalContractNo;
        CarDepartRequest departRequest;
        CarBaseInfoSearchResponse crsCar;
        Cars car;
        CarDropOffRequest carDropOffRequest;
        String renewalOrderNo;
        String renewalOrderLrentalContractNo;
    }

    void createOrder(OrderTestContext context, int month, boolean needAutoCredit, String autoCreditBypassReason) throws Exception {
        List<Cars> idleCars = carsService.getIdleCar();
        assertFalse(idleCars.isEmpty(), "There should be idle cars");

        context.idleCar = null;
        for (Cars car : idleCars) {
            CarBaseInfoSearchResponse carBaseInfoSearchResponse = crsService.getCar(car.getPlateNo());
            if (carBaseInfoSearchResponse != null
                && carBaseInfoSearchResponse.getCarLicense().getLicenseStatus().equals("0")
                && carBaseInfoSearchResponse.getBuId().equals(BuIdEnum.subscribe.getCode())
                && Arrays.asList(CarDefine.Launched.open, CarDefine.Launched.close).contains(car.getLaunched())) {
                context.idleCar = car;
                break;
            }
        }
        if (context.idleCar == null) {
            throw new RuntimeException("No valid idle cars found");
        }

        context.contractCreateReq = new InternalContractCreateReq();
        context.contractCreateReq.setPlateNo(context.idleCar.getPlateNo());
        context.contractCreateReq.setCarLevel(context.idleCar.getSubscribeLevel());
        context.contractCreateReq.setDepartStationCode("203");
        context.contractCreateReq.setReturnStationCode("203");
        context.contractCreateReq.setExpectStartDate(Instant.ofEpochMilli(getExpectStartDate()));
        context.contractCreateReq.setMonth(month);
        context.contractCreateReq.setDisclaimer(false);
        context.contractCreateReq.setPremium(false);

        Invoice invoice = new Invoice();
        invoice.setCategory(5);
        invoice.setId("A193477449");
        invoice.setTitle("李志宏");
        invoice.setType(2);
        context.contractCreateReq.setInvoice(invoice);

        context.contractCreateReq.setCustSource(2);
        context.contractCreateReq.setOrderPlatform(OrderPlatform.CASHIER);
        context.contractCreateReq.setAcctId(33456914);

        List<MerchandiseReq> merchList = new ArrayList<>();
        MerchandiseReq merch1 = new MerchandiseReq();
        merch1.setSkuCode("wipe001");
        merch1.setQuantity(2);
        merch1.setActualUnitPrice(38);
        merchList.add(merch1);

        MerchandiseReq merch2 = new MerchandiseReq();
        merch2.setSkuCode("voucher001");
        merch2.setQuantity(1);
        merch2.setActualUnitPrice(19000);
        merchList.add(merch2);

        context.contractCreateReq.setMerchList(merchList);

        context.contractCreateReq.setNeedAutoCredit(needAutoCredit);
        context.contractCreateReq.setAutoCreditBypassReason(autoCreditBypassReason);

        context.createContractResult = mockMvc.perform(post("/internal/subscribe/contract")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .content(objectMapper.writeValueAsString(context.contractCreateReq)))
            .andExpect(jsonPath("$.statusCode").value(0))
            .andReturn();

        JsonNode dataNode = objectMapper.readTree(context.createContractResult.getResponse().getContentAsString()).get("data");
        JsonNode ordersNode = dataNode.get("orders");
        JsonNode orderNode = ordersNode.get(0);
        assert !orderNode.get("orderPlatform").isNull();
        context.orderNo = orderNode.get("orderNo").asText();
        context.mainContractNo = dataNode.get("mainContractNo").asText();
    }

    void approveCreditManually(OrderTestContext context) throws Exception {
        assertNotNull(context.orderNo, "orderNo in context should not be null"); // 確保 orderNo 已被初始化
        String manualCreditApprovalRemark = "授信通過原因測試";
        CreditApprovalRequest creditApprovalRequest = new CreditApprovalRequest(manualCreditApprovalRemark);
        context.order = orderService.approveCredit(context.orderNo, MEMBER_ID, creditApprovalRequest);
        assert CreditRemarkType.MANUAL_PASS == context.order.getCreditInfo().getManualCreditInfo().getCreditRemarkType();
        assertEquals(manualCreditApprovalRemark, context.order.getCreditInfo().getManualCreditInfo().getManualCreditRemark());
        Thread.sleep(1000);
    }

    private void setMileageDiscount(String orderNo) throws Exception {
        assertNotNull(orderNo, "orderNo should not be null"); // 確保 orderNo 已被初始化
        // 設定里程優惠 patch /internal/subscribe/{orderNo}/mileageDiscount
        MileageDiscountRequest mileageDiscountRequest = new MileageDiscountRequest();
        Map<Integer, EmpMileageDiscount> mileageDiscounts = new HashMap<>();
        mileageDiscounts.put(1, new EmpMileageDiscount(100, "testMileageDiscount"));
        mileageDiscounts.put(2, new EmpMileageDiscount(200, "testMileageDiscount"));
        mileageDiscounts.put(3, new EmpMileageDiscount(300, "testMileageDiscount"));
        mileageDiscounts.put(4, new EmpMileageDiscount(400, "testMileageDiscount"));
        mileageDiscountRequest.setMileageDiscounts(mileageDiscounts);
        mockMvc.perform(patch("/internal/subscribe/{orderNo}/mileageDiscount", orderNo)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mileageDiscountRequest)))
            .andExpect(jsonPath("$.statusCode").value("0"));
    }

    void payForSecurityDeposit(OrderTestContext context) throws Exception {
        assertNotNull(context.createContractResult, "createContractResult in context should not be null"); // 確保 createContractResult 已被初始化
        assertNotNull(context.order, "order in context should not be null"); // 確保 order 已被初始化
        assertNotNull(context.mainContractNo, "mainContractNo in context should not be null"); // 確保 mainContractNo 已被初始化

        // 建立一個 mock ApplicationEventPublisher 並注入到 OrderService
        ApplicationEventPublisher mockPublisher = Mockito.mock(ApplicationEventPublisher.class);
        ReflectionTestUtils.setField(orderService, "eventPublisher", mockPublisher);

        JsonNode dataNode = objectMapper.readTree(context.createContractResult.getResponse().getContentAsString()).get("data");
        context.realSecurityDepositAmount = dataNode.get("mainContract").get("originalPriceInfo").get("securityDepositInfo").get("realSecurityDeposit").asInt();
        MvcResult payForSecurityDepositResult = getPayResult(context.realSecurityDepositAmount, context.order.getOrderNo(), true, PayFor.SecurityDeposit, "{mainContractNo}/payForSecurityDeposit", context.mainContractNo);

        context.payForSecurityDepositDataNode = objectMapper.readTree(payForSecurityDepositResult.getResponse().getContentAsString()).get("data");
        context.paySecurityDepositUrl = context.payForSecurityDepositDataNode.get("paymentUrl").asText();

        // 模擬支付 OTP 驗證
        Thread.sleep(1000);
        validateOtp(context.paySecurityDepositUrl);
        Thread.sleep(1000);

        // 確認付款資訊
        List<PaymentInfo> paymentInfos = paymentService.getPaymentInfosByTradeId(context.payForSecurityDepositDataNode.get("recTradeId").asText());
        assertNotNull(paymentInfos, "付款資訊不應為空");
        assertFalse(paymentInfos.isEmpty(), "付款資訊列表不應為空");
        List<Integer> orderPriceInfoIdsToPay = objectMapper.readValue(paymentInfos.get(0).getAdditionalData(), AdditionalData.class).getOrderPriceInfoIds();

        // Local 再 Consume 一次 PaymentQueue
        PaymentInfo securityDepositPayment = paymentInfos.get(0);
        PaymentQueue queue = new PaymentQueue();
        BeanUtils.copyProperties(securityDepositPayment, queue);
        paymentListener.subscribeReceive(queue, 0, null);
        Thread.sleep(2000);

        // 確認保證金費用付款完成
        PriceInfoWrapper priceInfoWrapper = priceInfoService.getPriceInfoWrapper(context.orderNo).getByCategoryType(SecurityDeposit, Pay);
        OrderPriceInfo securityDepositPriceInfo = priceInfoWrapper.getPaid().getList().stream()
            .filter(orderPriceInfo -> orderPriceInfoIdsToPay.contains(orderPriceInfo.getId()) && orderPriceInfo.getReceivedAmount() > 0)
            .min(Comparator.comparing(OrderPriceInfo::getStage))
            .orElseThrow(() -> new SubscribeException(SECURITY_DEPOSIT_PRICE_INFO_NOT_FOUND));
        assertTrue(securityDepositPriceInfo.isPaid(), "保證金應為已付款");

        // 檢查訂單是否包含汽車用品項目
        if (context.contractCreateReq != null && context.contractCreateReq.getMerchList() != null && !context.contractCreateReq.getMerchList().isEmpty()) {
            // 驗證 OrderBookingEvent 是否被發佈
            ArgumentCaptor<ApplicationEvent> eventCaptor = ArgumentCaptor.forClass(ApplicationEvent.class);
            verify(mockPublisher, atLeastOnce()).publishEvent(eventCaptor.capture());

            List<OrderBookingEvent> bookingEvents = eventCaptor.getAllValues().stream()
                .filter(OrderBookingEvent.class::isInstance)
                .map(OrderBookingEvent.class::cast)
                .filter(e -> e.getOrder().getOrderNo().equals(context.orderNo))
                .collect(Collectors.toList());

            assertFalse(bookingEvents.isEmpty(), "應至少有一個 OrderBookingEvent 被發佈");

            OrderBookingEvent event = bookingEvents.get(0);
            assertEquals(context.orderNo, event.getOrder().getOrderNo(), "事件中的訂單編號應與測試訂單編號相符");
            assertEquals(context.order.getMemberId(), event.getOperator(), "事件中的 operator 應與測試中使用的 memberId 相符");
        }
    }

    void payForDepartFeeAndCheckOut(OrderTestContext context) throws Exception {
        assertNotNull(context.orderNo, "orderNo in context should not be null"); // 確保 orderNo 已被初始化
        assertNotNull(context.mainContractNo, "mainContractNo in context should not be null"); // 確保 mainContractNo 已被初始化
        assertNotNull(context.order, "order in context should not be null"); // 確保 order 已被初始化

        // 模擬門市折扣審核通過後
        OrderPriceInfo merchandiseOrderPriceInfo = priceInfoService.getUnPaidPriceInfoByOrder(context.orderNo, false).stream()
            .filter(orderPriceInfo -> orderPriceInfo.getCategory() == Merchandise)
            .findAny()
            .orElseThrow(() -> new SubscribeException(ORDER_PRICE_INFO_NOT_FOUND));
        OrderPriceInfo discountOpi = priceInfoService.generateNegativeOrderPriceInfo(merchandiseOrderPriceInfo, merchandiseOrderPriceInfo.getAmount() / 10, EmpDiscount);
        discountOpi.setUid("testUid");
        PriceInfoDetail infoDetail = new PriceInfoDetail();
        infoDetail.setIsAgree(true);
        discountOpi.setInfoDetail(infoDetail);
        discountOpi.setReceivedAmount(discountOpi.getAmount());
        priceInfoService.addOrUpdate(discountOpi);

        // 查詢繳完保證金後開放付款的應付費用
        int unpaidAmount = priceInfoService.getUnPaidPriceInfoByOrder(context.orderNo, false).stream()
            .filter(orderPriceInfo -> orderPriceInfo.getType() == Pay.getCode() && !orderPriceInfo.isPaid())
            .map(PriceInfoInterface::getActualPrice)
            .reduce(Integer::sum)
            .orElse(0);
        unpaidAmount += discountOpi.getActualPrice();

        if (unpaidAmount <= 0) {
            throw new RuntimeException("應存在未支付費用");
        }

        MvcResult payResult = getPayResult(unpaidAmount, context.orderNo, false, Depart, "{orderNo}/pay", context.mainContractNo);
        JsonNode payResultDataNode = objectMapper.readTree(payResult.getResponse().getContentAsString()).get("data");
        String paymentUrl = payResultDataNode.get("paymentUrl").asText();

        // 模擬支付 OTP 驗證
        Thread.sleep(1000);
        validateOtp(paymentUrl);
        Thread.sleep(1000);

        // 確認付款資訊
        List<PaymentInfo> paymentInfos = paymentService.getPaymentInfosByTradeId(payResultDataNode.get("recTradeId").asText());
        assertNotNull(paymentInfos, "付款資訊不應為空");
        assertFalse(paymentInfos.isEmpty(), "付款資訊列表不應為空");

        // Local 再 Consume 一次 PaymentQueue 並立帳
        PaymentQueue departPaymentQueue = new PaymentQueue();
        BeanUtils.copyProperties(paymentInfos.get(0), departPaymentQueue);
        paymentService.consumePayment(departPaymentQueue);
        paymentService.afterPay(departPaymentQueue);
        checkoutService.checkOut(context.order, null);

        // 等待處理完成，並檢查付款狀態
        Thread.sleep(2000);
        priceInfoService.getCurrentReceivedPriceInfo(context.orderNo)
            .forEach(orderPriceInfo -> assertTrue(orderPriceInfo.isPaid(), "所有應付費用應為已支付"));
    }

    void createOriginalLrentalContract(OrderTestContext context) throws Exception {
        assertNotNull(context.orderNo, "orderNo in context should not be null"); // 確保 orderNo 已被初始化
        assertNotNull(context.order, "order in context should not be null"); // 確保 order 已被初始化

        context.originalLrentalContractNo = createOriginalLrentalContract(context.order);
        context.order = orderService.getOrder(context.orderNo);
        assertEquals(context.originalLrentalContractNo, context.order.getLrentalContractNo());
        Thread.sleep(1000);
    }

    /**
     * 出車資料確認更新訂單，並設定與原長租契約不同的日期
     */
    void departUpdateOrder(OrderTestContext context, int offsetFromExpectedStart) {
        assertNotNull(context.order, "order in context should not be null"); // 確保 order 已被初始化

        MainContract mainContract = context.order.getContract().getMainContract();
        // 初始化 context.mainContract
        context.mainContract = mainContract;
        String plateNo = mainContract.getPlateNo();
        context.crsCar = crsService.getCar(plateNo);
        Cars car = Optional.ofNullable(carsService.findByPlateNo(plateNo)).orElseThrow(() -> new SubscribeException(CAR_NOT_FOUND));

        context.departRequest = new CarDepartRequest();
        context.departRequest.setDepartDate(Date.from(context.order.getExpectStartDate().minus(offsetFromExpectedStart, ChronoUnit.DAYS)));
        int departMileage = Math.max(car.getCurrentMileage(), context.crsCar.getCarBase().getKm()) + 1;
        context.departRequest.setDepartMileage(departMileage);
        context.departRequest.setPlateNo(plateNo);
        ArrayList<String> errorMessages = orderService.departUpdateOrder(context.orderNo, context.departRequest, MEMBER_ID);
        assertTrue(errorMessages.isEmpty());
    }

    private AccountSettlementResponse accountSettlementAndCheckOut(OrderTestContext context, PayFor payFor) throws JsonProcessingException {
        assertNotNull(context.orderNo, "orderNo in context should not be null"); // 確保 orderNo 已被初始化
        assertNotNull(context.mainContract, "mainContract in context should not be null"); // 確保 mainContract 已被初始化
        assertNotNull(context.order, "order in context should not be null"); // 確保 order 已被初始化

        AccountSettlementRequest accountSettlementRequest = new AccountSettlementRequest();

        PaymentInfo paymentInfo = paymentService.getPaymentsByOrder(context.orderNo).stream()
            .filter(payment -> PayAuth == payment.getPaymentCategory() && payFor == payment.getPayFor())
            .findFirst().orElseThrow(() -> new SubscribeException(PAYMENT_INFO_NOT_FOUND));
        PaymentRequest paymentRequest = new PaymentRequest();
        paymentRequest.setAccountRecords(new ArrayList<>());
        paymentRequest.setStationCode(Depart == payFor ? context.mainContract.getDepartStationCode() : context.mainContract.getReturnStationCode());
        paymentRequest.getAccountRecords().add(orderService.buildAccountRecord(paymentInfo.getAmount(), context.orderNo, paymentInfo, payFor));
        accountSettlementRequest.setPaymentRequest(paymentRequest);

        InvoiceNewRequest invoiceNewRequest = new InvoiceNewRequest();
        invoiceNewRequest.setStationCode(subscribeStationCode);
        invoiceNewRequest.setPayFor(payFor.name());
        List<Integer> orderPriceInfoIdsToPay = objectMapper.readValue(paymentInfo.getAdditionalData(), AdditionalData.class).getOrderPriceInfoIds();
        Map<Boolean, List<OrderPriceInfo>> groupedPaidPriceInfos = priceInfoService.getPriceInfosByOrder(context.orderNo).stream()
            .filter(opi -> orderPriceInfoIdsToPay.contains(opi.getId()) && SecurityDeposit != opi.getCategory() && ETag != opi.getCategory() && opi.isPaid())
            .collect(Collectors.partitioningBy(orderPriceInfo -> orderPriceInfo.isMerchandise() || orderPriceInfo.getUid() != null));

        groupedPaidPriceInfos.forEach((isMerchandiseRelated, orderPriceInfos) -> {
            InvoiceRequest invoiceRequest = new InvoiceRequest();
            int receivedAmount = orderPriceInfos.stream()
                .mapToInt(OrderPriceInfo::getActualReceivePrice)
                .sum();
            if (receivedAmount == 0) {
                return;
            }
            invoiceRequest.setAmount(receivedAmount);
            List<Integer> ids = orderPriceInfos.stream()
                .map(OrderPriceInfo::getId)
                .collect(Collectors.toList());
            invoiceRequest.setRefPriceInfoIds(ids);
            invoiceRequest.setInvoice(context.order.getInvoice());
            invoiceRequest.setMemo("測試發票備註");
            invoiceNewRequest.getInvoices().add(invoiceRequest);
        });
        accountSettlementRequest.setInvoiceNewRequest(invoiceNewRequest);

        AccountSettlementResponse accountSettlementResponse = paymentService.accountSettlement(accountSettlementRequest, context.orderNo, MEMBER_ID);
        paymentService.payAndDiscountBalance(context.orderNo);
        checkoutService.checkOut(context.order, null);

        return accountSettlementResponse;
    }

    void accountSettlementAndCheckOutForDepart(OrderTestContext context) throws JsonProcessingException {

        AccountSettlementResponse accountSettlementResponse = accountSettlementAndCheckOut(context, Depart);

        // 驗證訂閱發票檔備註
        List<Invoices> invoicesList = invoiceService.getInvoice(context.orderNo);
        assertEquals("測試發票備註", invoicesList.get(invoicesList.size() - 1).getMemo());

        String invNo = accountSettlementResponse.getInvoicesList().get(accountSettlementResponse.getInvoicesList().size() - 1).getInvNo();

        // 驗證發票主檔備註
        Result<List<InvoiceMasterSearchResponse>> invoiceMasterList = finServiceBusClient.getInvoiceMaster(Collections.singletonList(invNo));
        InvoiceMasterSearchResponse invoiceMasterSearchResponse = invoiceMasterList.getData().stream().findFirst().orElseThrow(() -> new RuntimeException("找不到發票主檔"));
        assertEquals("測試發票備註", invoiceMasterSearchResponse.getMemo());

        // 驗證發票完整資訊備註
        Result<List<InvoiceDataSearchResponse>> invoiceDataList = finServiceBusClient.getInvoiceData(Collections.singletonList(invNo));
        InvoiceDataSearchResponse invoiceDataSearchResponse = invoiceDataList.getData().stream().findFirst().orElseThrow(() -> new RuntimeException("找不到發票完整資訊"));
        assertEquals("測試發票備註", invoiceDataSearchResponse.getMemo());
    }

    void accountSettlementAndCheckOutForReturn(OrderTestContext context) throws JsonProcessingException {
        accountSettlementAndCheckOut(context, Return);
    }

    void depart(OrderTestContext context) throws Exception {
        assertNotNull(context.orderNo, "orderNo in context should not be null"); // 確保 orderNo 已被初始化

        MockHttpServletRequestBuilder requestBuilder = post("/internal/subscribe/{orderNo}/depart", context.orderNo)
            .contentType(MediaType.APPLICATION_JSON)
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
            .content(CAR_DEPART_FLOW_REQUEST);
        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value("0"));
    }

    /**
     * 驗證電訪任務是否正確建立
     */
    private void verifyCsatTaskCreation(OrderTestContext context) {
        assertNotNull(context.orderNo, "orderNo in context should not be null"); // 確保 orderNo 已被初始化
        assertNotNull(context.departRequest, "departRequest in context should not be null"); // 確保 departRequest 已被初始化

        context.car = carsService.findByPlateNo(context.departRequest.getPlateNo());

        // 檢查車輛是否為格上的車
        boolean isCarPlusCar = CarsUtil.isCarPlusCar(context.car.getVatNo());

        // 查詢該訂單的電訪任務
        List<Csat> csatList = csatRepository.findByOrders(Collections.singletonList(context.orderNo));

        if (isCarPlusCar) {
            // 如果是格上的車，應該要有電訪任務
            assertFalse(csatList.isEmpty(), "格上車輛應該建立電訪任務");
            Csat csat = csatList.get(0);
            assertEquals(context.orderNo, csat.getOrderNo(), "電訪任務的訂單編號應該正確");
            assertEquals(context.car.getPlateNo(), csat.getPlateNo(), "電訪任務的車牌號碼應該正確");
            assertEquals(CsatStatus.NOT_CALLED.getCode(), csat.getStatus(), "電訪任務狀態應為未電訪");
            assertEquals(CsatQuestStatus.NOT_SURVEYED.getCode(), csat.getQuestStatus(), "電訪問卷狀態應為未填寫");
            assertEquals(CsatOrderSource.SUBSCRIBE.getCode(), csat.getSource(), "電訪任務來源應為訂閱");

            // 驗證電訪問卷是否正確建立
            CsatQuest csatQuest = csatQuestRepository.findById(csat.getId()).orElse(null);
            assertNotNull(csatQuest, "電訪問卷應該存在");
            assertEquals(0, csatQuest.getVersion(), "電訪問卷版本應為 0");
        } else {
            // 如果不是格上的車，不應該有電訪任務
            assertTrue(csatList.isEmpty(), "非格上車輛不應建立電訪任務");
        }
    }

    /**
     * 驗證長租契約已重建
     */
    void verifyLrentalContractReCreated(OrderTestContext context) {
        assertNotNull(context.orderNo, "orderNo in context should not be null"); // 確保 orderNo 已被初始化
        assertNotNull(context.order, "order in context should not be null"); // 確保 order 已被初始化
        assertNotNull(context.originalLrentalContractNo, "originalLrentalContractNo in context should not be null"); // 確保 originalLrentalContractNo 已被初始化
        assertNotNull(context.departRequest, "departRequest in context should not be null"); // 確保 departRequest 已被初始化

        // 檢查車輛是否為格上的車，非格上車輛不需要驗證長租契約已重建
        if (!CarsUtil.isCarPlusCar(context.departRequest.getPlateNo())) {
            return;
        }

        context.order = orderService.getOrder(context.orderNo);
        assertNotNull(context.order.getLrentalContractNo());
        assertNotEquals(context.originalLrentalContractNo, context.order.getLrentalContractNo());
        context.recreatedLrentalContractNo = context.order.getLrentalContractNo();

        // 驗證新建長租契約日期是否正確更新
        ContractSearchRep newContract = lrentalServer.getContractInfo(context.order.getLrentalContractNo());
        String newStartDate = newContract.getDadt1().trim();
        String newEndDate = newContract.getDadt2().trim();
        // 驗證新契約的起始日期是否與實際出車日期一致
        assertEquals(DateUtil.transferADDateToMinguoDate(context.departRequest.getDepartDate().toInstant()), newStartDate);
        // 驗證新契約的結束日期是否與訂單結束日期一致
        assertEquals(DateUtil.transferADDateToMinguoDate(context.order.getExpectEndDate()), newEndDate);

        ContractSearchRep originalContract = lrentalServer.getContractInfo(context.originalLrentalContractNo);
        String originalStartDate = originalContract.getDadt1().trim();
        String originalEndDate = originalContract.getDadt2().trim();
        // 確認新契約日期與原契約日期不同
        assertNotEquals(originalStartDate, newStartDate);
        assertNotEquals(originalEndDate, newEndDate);
    }

    void verifyMileageSynchronizedWithCrs(OrderTestContext context) {
        assertNotNull(context.mainContract, "mainContract in context should not be null"); // 確保 mainContract 已被初始化
        String plateNo = context.mainContract.getPlateNo();
        context.car = carsService.findByPlateNo(plateNo);
        assertNotNull(context.car.getCrsCarNo(), "car.getCrsCarNo() should not be null"); // 確保 car.getCrsCarNo() 已被初始化
        assertTrue(context.car.getCrsCarNo() > 0, "car.getCrsCarNo() should be greater than 0"); // 確保 car.getCrsCarNo() > 0
        CarBaseInfoSearchResponse carBaseInfoSearchResponse = crsService.getCar(plateNo);
        assert Objects.equals(context.car.getCurrentMileage(), carBaseInfoSearchResponse.getCarBase().getKm());
    }

    void returnCarConfirm(OrderTestContext context) throws Exception {
        assertNotNull(context.orderNo, "orderNo in context should not be null"); // 確保 orderNo 已被初始化
        assertNotNull(context.car, "car in context should not be null"); // 確保 car 已被初始化

        context.carDropOffRequest = new CarDropOffRequest();
        context.carDropOffRequest.setReturnDate(DateUtils.toDate(DateUtils.toLocalDateTime(!context.order.getIsNewOrder() ? Date.from(context.order.getStartDate().plus(10, ChronoUnit.DAYS)) : new Date())));
        context.carDropOffRequest.setReturnMileage(context.car.getCurrentMileage() + 2000);
        context.carDropOffRequest.setReturnRemark("測試還車備註");
        mockMvc.perform(patch("/internal/subscribe/{orderNo}/return", context.orderNo)
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .content(objectMapper.writeValueAsString(context.carDropOffRequest)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"));

        Thread.sleep(1000);

        // get /internal/subscribe/v2/priceInfo/{orderNo}?credit=false
        MvcResult result = mockMvc.perform(get("/internal/subscribe/v2/priceInfo/{orderNo}?credit=false", context.orderNo)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        JsonNode jsonNode = objectMapper.readTree(responseContent);
        JsonNode dataNode = jsonNode.get("data");

        // 找到最後一個元素 (最大的 key 值)
        String lastKey = null;
        Iterator<String> fieldNames = dataNode.fieldNames();
        int maxKey = -1;
        while (fieldNames.hasNext()) {
            String key = fieldNames.next();
            try {
                int keyInt = Integer.parseInt(key);
                if (keyInt > maxKey) {
                    maxKey = keyInt;
                    lastKey = key;
                }
            } catch (NumberFormatException e) {
                // 忽略非數字的 key
            }
        }

        // 計算所有 MileageFee 的 saveRealMileage - (discountMileage - rentalDiscountMileage) 加總
        int totalDiff = 0;
        Iterator<String> allStageKeys = dataNode.fieldNames();
        while (allStageKeys.hasNext()) {
            String stageKey = allStageKeys.next();
            JsonNode priceInfoList = dataNode.get(stageKey).get("orderPriceInfoList");
            for (JsonNode item : priceInfoList) {
                if ("MileageFee".equals(item.get("category").asText())) {
                    JsonNode infoDetail = item.get("infoDetail");
                    if (infoDetail != null && infoDetail.has("saveRealMileage") && infoDetail.get("saveRealMileage").asInt(0) > 0) {
                        int saveRealMileage = infoDetail.get("saveRealMileage").asInt(0);
                        int discountMileage = infoDetail.get("discountMileage").asInt(0);
                        int rentalDiscountMileage = infoDetail.get("rentalDiscountMileage").asInt(0);
                        totalDiff += saveRealMileage - (discountMileage - rentalDiscountMileage);
                    }
                }
            }
        }

        if (lastKey != null) {
            JsonNode priceInfoList = dataNode.get(lastKey).get("orderPriceInfoList");
            if (totalDiff > 0) {
                // 應存在 category 為 Others 的回收里程優惠，且 reason 的 km 數字等於 totalDiff
                boolean found = false;
                for (JsonNode item : priceInfoList) {
                    if (item.get("category").asText().contains("Others")) {
                        String reason = item.get("infoDetail").get("reason").asText();
                        java.util.regex.Matcher matcher = java.util.regex.Pattern.compile("(\\d+)\\s*km").matcher(reason);
                        if (matcher.find()) {
                            int kmValue = Integer.parseInt(matcher.group(1));
                            assertEquals(totalDiff, kmValue, "回收里程優惠 km 數字應等於所有 MileageFee 的 saveRealMileage - (discountMileage - rentalDiscountMileage) 加總");
                            found = true;
                        }
                    }
                }
                assertTrue(found, "應存在 category 為 Others 的回收里程優惠項目");
            } else {
                // 不應有回收里程優惠
                for (JsonNode item : priceInfoList) {
                    assertFalse(item.get("category").asText().contains("Others") &&
                            item.get("infoDetail").has("reason") &&
                            item.get("infoDetail").get("reason").asText().contains("回收里程優惠"),
                        "當 totalDiff <= 0 時，不應有回收里程優惠");
                }
            }
        }
    }

    void payForReturnFee(OrderTestContext context) throws Exception {
        assertNotNull(context.orderNo, "orderNo in context should not be null"); // 確保 orderNo 已被初始化
        assertNotNull(context.mainContractNo, "mainContractNo in context should not be null"); // 確保 mainContractNo 已被初始化

        List<OrderPriceInfo> unpaidPriceInfosAfterReturnCarConfirm = priceInfoService.getUnPaidPriceInfoByOrder(context.orderNo, false);
        int unpaidAmountAfterReturnCarConfirm = unpaidPriceInfosAfterReturnCarConfirm.stream()
            .filter(orderPriceInfo -> orderPriceInfo.getType() == Pay.getCode() && !orderPriceInfo.isPaid())
            .map(PriceInfoInterface::getActualPrice)
            .reduce(Integer::sum)
            .orElse(0);

        if (unpaidAmountAfterReturnCarConfirm <= 0) {
            throw new RuntimeException("應存在未支付費用");
        }

        MvcResult payResult = getPayResult(unpaidAmountAfterReturnCarConfirm, context.orderNo, false, Return, "{orderNo}/pay", context.mainContractNo);
        JsonNode payResultDataNode = objectMapper.readTree(payResult.getResponse().getContentAsString()).get("data");
        String paymentUrl = payResultDataNode.get("paymentUrl").asText();

        Thread.sleep(1000);
        validateOtp(paymentUrl);
        Thread.sleep(1000);

        // 確認付款資訊
        List<PaymentInfo> paymentInfos = paymentService.getPaymentInfosByTradeId(payResultDataNode.get("recTradeId").asText());
        assertNotNull(paymentInfos, "付款資訊不應為空");
        assertFalse(paymentInfos.isEmpty(), "付款資訊列表不應為空");

        // Local 再 Consume 一次 PaymentQueue 並立帳
        PaymentQueue returnPaymentQueue = new PaymentQueue();
        BeanUtils.copyProperties(paymentInfos.get(0), returnPaymentQueue);
        paymentService.consumePayment(returnPaymentQueue);
        paymentService.afterPay(returnPaymentQueue);
        checkoutService.checkOut(context.order, null);

        // 等待處理完成，並檢查付款狀態
        Thread.sleep(2000);
        List<OrderPriceInfo> receivedPriceInfos = priceInfoService.getCurrentReceivedPriceInfo(context.orderNo);
        receivedPriceInfos.forEach(orderPriceInfo -> assertTrue(orderPriceInfo.isPaid(), "所有應付費用應為已支付"));
    }

    void setAccidentPriceInfo(OrderTestContext context) throws Exception {
        assertNotNull(context.orderNo, "orderNo in context should not be null"); // 確保 orderNo 已被初始化

        ExtraFeeRequest extraFeeRequest = new ExtraFeeRequest();
        extraFeeRequest.setCarDamaged(true);
        extraFeeRequest.setReturnNego("N");
        List<ExtraFeeRequest.ExtraFee> extraFeeList = new ArrayList<>();
        ExtraFeeRequest.ExtraFee extraFee = new ExtraFeeRequest.ExtraFee();
        extraFee.setAmount(20000);
        extraFee.setARCarLossAmt(20000);
        extraFee.setReason("車損");
        Orders order = orderService.getOrder(context.orderNo);
        int currentStage = order.getCurrentStage().getStage();
        extraFee.setStage(currentStage);
        extraFee.setCategory(CarAccident);
        extraFeeList.add(extraFee);
        extraFeeRequest.setExtraFeeList(extraFeeList);
        MvcResult setAccidentPriceInfoResult = mockMvc.perform(post("/internal/subscribe/v1/priceInfo/{orderNo}/accident", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(extraFeeRequest)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andReturn();
        JsonNode accidentPriceInfoResultDataNode = objectMapper.readTree(setAccidentPriceInfoResult.getResponse().getContentAsString()).get("data");
        assertNotNull(accidentPriceInfoResultDataNode, "費用資訊不應為空");
        assertTrue(accidentPriceInfoResultDataNode.isArray(), "費用資訊應為 array");
        accidentPriceInfoResultDataNode.forEach(node -> {
            // 確保返回的費用資訊列表中，當前階段的車損費用如果存在，也應該是處於未開放的狀態
            assert !node.get("category").asText().equals(CarAccident.name())
                || node.get("stage").asInt() != currentStage
                || node.get("receivableDate").asLong() > Instant.now().getEpochSecond();
        });
    }

    void returnCarSuccess(OrderTestContext context) throws Exception {
        assertNotNull(context.orderNo, "orderNo in context should not be null"); // 確保 orderNo 已被初始化

        CarDropOffCompleteRequest carDropOffCompleteRequest = new CarDropOffCompleteRequest();
        carDropOffCompleteRequest.setReturnMemberId(MEMBER_ID);
        mockMvc.perform(post("/internal/subscribe/{orderNo}/return", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(carDropOffCompleteRequest)))
            .andExpect(jsonPath("$.statusCode").value("0"));
        context.car = carsService.findByPlateNo(context.mainContract.getPlateNo());
        Thread.sleep(1000);
    }

    @Transactional(noRollbackFor = SubscribeException.class)
    void returnCarExpectUnpaidError(OrderTestContext context) {
        assertNotNull(context.orderNo, "orderNo in context should not be null"); // 確保 orderNo 已被初始化

        CarDropOffCompleteRequest carDropOffCompleteRequest = new CarDropOffCompleteRequest();
        carDropOffCompleteRequest.setReturnMemberId(MEMBER_ID);
        try {
            mockMvc.perform(post("/internal/subscribe/{orderNo}/return", context.orderNo)
                    .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(carDropOffCompleteRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.statusCode").value(ORDER_PRICE_INFO_UNRECEIVABLE_NOT_ALLOW_RETURN.getCode()))
                .andExpect(jsonPath("$.message").value(ORDER_PRICE_INFO_UNRECEIVABLE_NOT_ALLOW_RETURN.getMsg()));
            Thread.sleep(1000);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    void verifyWithoutPendingUnpaidPriceInfo(OrderTestContext context) throws Exception {
        assertNotNull(context.orderNo, "orderNo in context should not be null"); // 確保 orderNo 已被初始化

        MvcResult priceInfoResult = mockMvc.perform(get("/internal/subscribe/priceInfo/{orderNo}/unpaid", context.orderNo)
                .param("history", "false")
                .param("isCredit", "true"))
            .andExpect(status().isOk())
            .andReturn();
        List<OrderPriceInfoResponse> userPriceInfos = objectMapper.readValue(
            priceInfoResult.getResponse().getContentAsString(),
            new TypeReference<List<OrderPriceInfoResponse>>() {
            }
        );
        assertFalse(userPriceInfos.isEmpty(), "未付費用資訊列表不應為空");
        userPriceInfos.forEach(priceInfo -> {
            assertNotEquals(PayStatus.PENDING, priceInfo.getPayStatus(),
                String.format("費用 id %d 付款狀態不應為未開放", priceInfo.getId()));
        });
    }

    /**
     * POST /internal/subscribe/{orderNo}/renew (使用 mockMvc 會失敗)
     */
    private void renewOrder(OrderTestContext context) {
        assertNotNull(context.orderNo, "orderNo in context should not be null"); // 確保 orderNo 已被初始化
        assertNotNull(context.contractCreateReq, "contractCreateReq in context should not be null"); // 確保 contractCreateReq 已被初始化
        OrderRenewRequest orderRenewRequest = new OrderRenewRequest();
        orderRenewRequest.setMonth(SubscribeRenewMonth.SIX);
        orderRenewRequest.setAcctId(context.contractCreateReq.getAcctId());
        Orders renewalOrder = contractLogic.renewOrder(context.orderNo, orderRenewRequest, true, MEMBER_ID, true, null);
        assertNotEquals(context.orderNo, renewalOrder.getOrderNo());
        context.renewalOrderNo = renewalOrder.getOrderNo();
    }

    /**
     * 注意事項：
     * - 使用 @Transactional(isolation = Isolation.READ_COMMITTED)，確保測試方法在 transaction 中執行。
     * - 使用 `OrderTestContext` 類來管理測試過程中需要共享的上下文資料。
     * - 為了使測試能夠順利進行，需要在測試前先將以下程式碼修改：
     * 1. {@link com.carplus.subscribe.service.CheckoutService#checkOut(java.lang.String)} 和
     * {@link com.carplus.subscribe.service.CheckoutService#checkOut(com.carplus.subscribe.db.mysql.entity.contract.Orders)}
     * 方法上的 `@Transactional` 註解，移除 `propagation = Propagation.REQUIRES_NEW` 屬性。
     * 原因： 移除 `Propagation.REQUIRES_NEW` 是為了讓 `CheckoutService` 的 `checkOut` 方法與呼叫它的方法共享同一個 transaction 。
     * 2. {@link com.carplus.subscribe.service.InvoiceServiceV2#createInvoice} 中的 `Invoices invoice = self.create(orders, user, subscribeStationCode, payFor, request, memberId);`
     * 修改為 `Invoices invoice = create(orders, user, subscribeStationCode, payFor, request, memberId);`
     * 原因： `self.create()` 是內部方法調用，在 Spring 的 AOP 代理機制下，直接的內部方法調用不會被 transaction 切面增強，
     * 導致 `create` 方法可能沒有在 transaction 中執行，修改為直接調用 `create()` 方法 (假設 `createInvoice` 方法本身是 transaction 方法) 可以確保 `create` 方法參與到外部方法 (`createInvoice`) 的 transaction 中。
     * 3. {@link com.carplus.subscribe.service.InvoiceServiceV2#setCheckout} 方法上的 `@Transactional` 註解，移除 `propagation = Propagation.REQUIRES_NEW` 屬性。
     * 原因： 與 `CheckoutService` 類似，移除 `Propagation.REQUIRES_NEW` 是為了讓 `setCheckout` 方法與呼叫它的方法共享同一個 transaction
     */
    private void fromCreateOrderToReturnCarConfirm(OrderTestContext context) throws Exception {
        // 步驟 1: 建立訂單
        // 呼叫 createOrder 方法，模擬收銀台建立訂閱制車輛租賃訂單的流程。
        // 此步驟會選擇一台閒置車輛，設定合約相關資訊，並透過 API 建立訂單，
        // 同時取得訂單編號 (orderNo) 和主約編號 (mainContractNo) 等上下文資訊。
        createOrder(context, 12, true, null);

        // 步驟 2: 人工授信
        // 呼叫 approveCreditManually 方法，模擬人工審核通過會員的信用。
        // 此步驟會更新訂單的授信狀態為人工審核通過，為後續支付流程做準備。
        approveCreditManually(context);

        // 步驟 3: 支付保證金
        // 呼叫 payForSecurityDeposit 方法，模擬會員支付訂單的保證金。
        // 此步驟會調用支付 API，模擬 TapPay OTP 驗證，並驗證保證金付款是否成功。
        payForSecurityDeposit(context);

        // 步驟 4: 支付出車費用並確認已付款
        // 呼叫 payForDepartFeeAndCheckOut 方法，模擬會員支付出車費用。
        // 此步驟會調用支付 API，模擬 TapPay OTP 驗證，並驗證出車費用付款是否成功。
        payForDepartFeeAndCheckOut(context);

        // 步驟 5: 建立原始長租契約
        // 呼叫 createOriginalLrentalContract 方法，模擬系統根據訂單資料建立原始長租契約。
        // 此步驟會調用 LrentalServer API 建立契約，並將契約編號更新回訂單。
        createOriginalLrentalContract(context);

        // 步驟 6: 出車資料確認更新訂單，模擬與原長租契約不同的出車日期
        // 呼叫 departUpdateOrder 方法，模擬因故需要更新訂單的出車日期。
        // 此步驟會模擬更新訂單的出車日期和出車里程，並驗證更新是否成功。
        departUpdateOrder(context, 10);

        // 步驟 7: 帳務結算、開立發票及立帳
        // 呼叫 accountSettlementAndCheckOut 方法，模擬系統進行帳務結算、開立發票及立帳流程。
        accountSettlementAndCheckOutForDepart(context);

        // 步驟 8: 觸發實際出車流程
        // 呼叫 depart 方法，模擬觸發實際出車的流程。
        // 此步驟會調用出車 API，將訂單狀態變更為已出車。
        depart(context);

        // 步驟 9: 驗證電訪任務已建立
        verifyCsatTaskCreation(context);

        // 步驟 10: 驗證長租契約已重建，並檢查日期是否正確更新
        // 呼叫 verifyLrentalContractReCreated 方法，驗證系統是否已根據更新後的出車日期重建長租契約。
        // 此步驟會檢查新契約的日期是否與訂單的實際出車日期和結束日期一致，並與原始契約日期進行比較。
        verifyLrentalContractReCreated(context);

        // 步驟 11: 驗證里程已同步至 CRS
        // 呼叫 verifyMileageSynchronizedWithCrs 方法，驗證系統是否已將車輛里程同步至 CRS。
        // 此步驟會檢查 Cars 資料庫中的車輛里程是否與 CRS 中的里程一致。
        verifyMileageSynchronizedWithCrs(context);

        // 步驟 12: 還車資料異動
        // 呼叫 returnCarConfirm 方法，模擬收銀台還車資料異動流程。
        // 此步驟會調用還車資料異動 API，設定還車日期和還車里程等資訊，並將訂單狀態變更為已還車。
        returnCarConfirm(context);
    }

    @Test
    @DisplayName("建立訂單後，人工授信通過失敗 - 原因為空")
    void createOrder_thenApproveCreditManually_Fail_WhenReasonIsBlank() throws Exception {
        // 建立訂單
        OrderTestContext context = new OrderTestContext();
        createOrder(context, 6, true, null);
        assertNotNull(context.orderNo, "訂單編號不應為空");

        // 授信通過原因為空
        CreditApprovalRequest creditApprovalRequest = new CreditApprovalRequest("");

        mockMvc.perform(post("/internal/subscribe/{orderNo}/credit/approve", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(creditApprovalRequest)))
            .andExpect(status().isBadRequest())
            .andExpect(jsonPath("$.message").value(containsString("授信通過原因不可為空")));
    }

    /**
     * 整合測試案例：從訂單建立到還車並驗證存在未開放付款的車損費用情境
     * <p>
     * 此測試案例涵蓋了從訂單建立、人工授信、保證金支付、出車、建立長租契約、
     * 更新訂單出車資訊、支付出車費用、實際出車、驗證長租契約重建、里程同步 CRS、
     * 確認還車、支付還車費用，到最後設定車損費用並驗證在存在未開放付款車損費用的情況下，
     * 無法完成還車流程，並驗證最終未付費用列表中不應包含未開放付款的費用。
     * <p>
     * 驗證重點：
     * 1. 訂單建立和相關資料的正確性 (車輛、合約、訂單編號等)。
     * 2. 人工授信流程的模擬和結果驗證。
     * 3. 保證金和出車費用的支付流程模擬和驗證 (包含 TapPay OTP 驗證模擬)。
     * 4. 長租契約的成功建立和重建，以及契約日期的驗證。
     * 5. 出車和還車流程的模擬，包含里程同步和費用結算。
     * 6. 車損費用的設定和在未支付車損費用的情況下，還車流程的阻斷驗證。
     * 7. 最終未付費用狀態的驗證，確保沒有未開放付款的費用。
     * <p>
     * 測試步驟：
     * 1. 建立訂單 (createOrder)。
     * 2. 人工授信 (approveCreditManually)。
     * 3. 支付保證金 (payForSecurityDeposit)。
     * 4. 支付出車費用並確認已付款 (payForDepartFeeAndCheckOut)。
     * 5. 建立原始長租契約 (createOriginalLrentalContract)。
     * 6. 出車資料確認更新訂單，模擬與原長租契約不同的出車日期 (departUpdateOrder)。
     * 7. 帳務結算、開立發票及立帳和出車檢查 (accountSettlementAndCheckOut)。
     * 8. 觸發實際出車流程 (depart)。
     * 9. 驗證長租契約已重建，並檢查日期是否正確更新 (verifyLrentalContractReCreated)。
     * 10. 驗證里程已同步至 CRS (verifyMileageSynchronizedWithCrs)。
     * 11. 還車資料異動 (returnCarConfirm)。
     * 12. 支付還車費用 (payForReturnFee)。
     * 13. 設定車損費用 (setAccidentPriceInfo)。
     * 14. 驗證在存在未支付費用的情況下，預期還車會失敗 (returnCarExpectUnpaidError)。
     * 15. 驗證最終未付費用列表中不應包含未開放付款的費用 (verifyWithoutPendingUnpaidPriceInfo)。
     * <p>
     * 注意事項：
     * - 使用 @Transactional(isolation = Isolation.READ_COMMITTED)，確保測試方法在 transaction 中執行。
     * - 使用 `OrderTestContext` 類來管理測試過程中需要共享的上下文資料。
     * - 為了使測試能夠順利進行，需要在測試前先將以下程式碼修改：
     * 1. {@link com.carplus.subscribe.service.CheckoutService#checkOut(java.lang.String)} 和
     * {@link com.carplus.subscribe.service.CheckoutService#checkOut(com.carplus.subscribe.db.mysql.entity.contract.Orders)}
     * 方法上的 `@Transactional` 註解，移除 `propagation = Propagation.REQUIRES_NEW` 屬性。
     * 原因： 移除 `Propagation.REQUIRES_NEW` 是為了讓 `CheckoutService` 的 `checkOut` 方法與呼叫它的方法共享同一個 transaction 。
     * 2. {@link com.carplus.subscribe.service.InvoiceServiceV2#createInvoice} 中的 `Invoices invoice = self.create(orders, user, subscribeStationCode, payFor, request, memberId);`
     * 修改為 `Invoices invoice = create(orders, user, subscribeStationCode, payFor, request, memberId);`
     * 原因： `self.create()` 是內部方法調用，在 Spring 的 AOP 代理機制下，直接的內部方法調用不會被 transaction 切面增強，
     * 導致 `create` 方法可能沒有在 transaction 中執行，修改為直接調用 `create()` 方法 (假設 `createInvoice` 方法本身是 transaction 方法) 可以確保 `create` 方法參與到外部方法 (`createInvoice`) 的 transaction 中。
     * 3. {@link com.carplus.subscribe.service.InvoiceServiceV2#setCheckout} 方法上的 `@Transactional` 註解，移除 `propagation = Propagation.REQUIRES_NEW` 屬性。
     * 原因： 與 `CheckoutService` 類似，移除 `Propagation.REQUIRES_NEW` 是為了讓 `setCheckout` 方法與呼叫它的方法共享同一個 transaction
     */
    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    void from_createOrder_to_returnWithPendingAccidentPriceInfo() throws Exception {

        OrderTestContext context = new OrderTestContext();

        fromCreateOrderToReturnCarConfirm(context);

        // 步驟 13: 支付還車費用
        // 呼叫 payForReturnFee 方法，模擬會員支付還車產生的費用。
        // 此步驟會調用支付 API，模擬 TapPay OTP 驗證，並驗證還車費用付款是否成功。
        payForReturnFee(context);

        // 步驟 14: 設定車損費用
        // 呼叫 setAccidentPriceInfo 方法，模擬在收銀台設定車損費用。
        // 此步驟會調用設定車損費用 API，為訂單新增車損費用，模擬車輛在租賃期間發生事故產生額外費用的情境。
        setAccidentPriceInfo(context);

        // 步驟 15: 驗證在存在未支付費用的情況下，預期還車會失敗
        // 呼叫 returnCarExpectUnpaidError 方法，驗證在存在未支付的車損費用的情況下，
        // 系統是否會拒絕完成還車流程，並返回預期的錯誤碼。
        // 此步驟模擬在未支付車損費用的情況下嘗試完成還車，驗證系統的費用檢查機制。
        returnCarExpectUnpaidError(context);

        // 步驟 16: 驗證最終未付費用列表中不應包含未開放付款的費用
        // 呼叫 verifyWithoutPendingUnpaidPriceInfo 方法，驗證最終的未付費用列表中，
        // 是否不存在付款狀態為 "PENDING" (未開放) 的費用，確保所有未付費用都是可以支付的狀態。
        // 此步驟驗證系統在流程結束時的費用狀態，確保沒有遺留未處理的費用問題。
        verifyWithoutPendingUnpaidPriceInfo(context);
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    @DisplayName("測試更新已付款汽車用品費用失敗")
    void updatePaidMerchandisePriceInfoShouldFail() throws Exception {
        OrderTestContext context = new OrderTestContext();
        createOrder(context, 12, true, null);
        approveCreditManually(context);
        payForSecurityDeposit(context);
        payForDepartFeeAndCheckOut(context);

        assertNotNull(context.orderNo, "orderNo in context should not be null"); // 確保 orderNo 已被初始化

        MerchandiseInfoRequest merchandiseInfoRequest = new MerchandiseInfoRequest();
        merchandiseInfoRequest.setMerchandiseList(new ArrayList<>());

        // 獲取現有所有的汽車用品費用
        priceInfoService.getUnPaidPriceInfoByOrderResponse(context.orderNo, true, true).stream()
            .filter(opi -> opi.getCategory() == Merchandise)
            .forEach(opi -> {
                MerchandiseInfoRequest.MerchandiseInfo merchandiseInfo = new MerchandiseInfoRequest.MerchandiseInfo();
                merchandiseInfo.setPriceInfoId(opi.getId());
                merchandiseInfo.setActualUnitPrice(opi.getInfoDetail().getActualUnitPrice());
                merchandiseInfo.setQuantity(opi.getInfoDetail().getQuantity());
                merchandiseInfo.setStage(opi.getStage());
                merchandiseInfo.setSkuCode(opi.getSkuCode());
                merchandiseInfo.setDelete(false);
                merchandiseInfoRequest.getMerchandiseList().add(merchandiseInfo);
            });

        // 新增汽車用品費用
        MerchandiseInfoRequest.MerchandiseInfo merchandiseInfo = new MerchandiseInfoRequest.MerchandiseInfo();
        merchandiseInfo.setPriceInfoId(null);
        merchandiseInfo.setActualUnitPrice(2880);
        merchandiseInfo.setQuantity(1);
        merchandiseInfo.setStage(1);
        merchandiseInfo.setSkuCode("recorder001");
        merchandiseInfo.setDelete(false);
        merchandiseInfoRequest.getMerchandiseList().add(merchandiseInfo);
        MvcResult addMerchandiseResult = mockMvc.perform(post("/internal/subscribe/priceInfo/{orderNo}/merchandise", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(merchandiseInfoRequest)))
            .andExpect(jsonPath("$.statusCode").value("0"))
            // 確保 category 為 "Merchandise" 的項目數量為 3
            .andExpect(jsonPath("$.data[?(@.category == 'Merchandise')]", hasSize(3)))
            // 確保特定 skuCode 存在
            .andExpect(jsonPath("$.data[*].skuCode").value(hasItems("wipe001", "voucher001", "recorder001")))
            .andReturn();

        Thread.sleep(1000);

        // 異動所有待付款汽車用品費用實際單價，成功
        JsonNode dataNode = objectMapper.readTree(addMerchandiseResult.getResponse().getContentAsString()).get("data");
        List<Integer> unpaidMerchandisePriceInfoId = new ArrayList<>();
        for (JsonNode priceInfoNode : dataNode) {
            OrderPriceInfo orderPriceInfo = objectMapper.treeToValue(priceInfoNode, OrderPriceInfo.class);
            if (Merchandise == orderPriceInfo.getCategory() && !orderPriceInfo.isPaid()) {
                unpaidMerchandisePriceInfoId.add(orderPriceInfo.getId());
            }
        }
        merchandiseInfoRequest.getMerchandiseList().forEach(m -> {
            if (unpaidMerchandisePriceInfoId.contains(m.getPriceInfoId())) {
                m.setActualUnitPrice(m.getActualUnitPrice() + 1);
            }
        });
        mockMvc.perform(post("/internal/subscribe/priceInfo/{orderNo}/merchandise", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(merchandiseInfoRequest)))
            .andExpect(jsonPath("$.statusCode").value("0"));

        Thread.sleep(1000);

        // 刪除所有待付款汽車用品費用，成功
        merchandiseInfoRequest.getMerchandiseList().forEach(m -> {
            if (unpaidMerchandisePriceInfoId.contains(m.getPriceInfoId())) {
                m.setDelete(true);
            }
        });
        mockMvc.perform(post("/internal/subscribe/priceInfo/{orderNo}/merchandise", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(merchandiseInfoRequest)))
            .andExpect(jsonPath("$.statusCode").value("0"));

        Thread.sleep(1000);

        // 異動所有汽車用品費用實際單價(包含已付款)，報錯：該款項明細已付款，不可異動
        merchandiseInfoRequest.getMerchandiseList().forEach(m -> m.setActualUnitPrice(m.getActualUnitPrice() + 1));
        mockMvc.perform(post("/internal/subscribe/priceInfo/{orderNo}/merchandise", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(merchandiseInfoRequest)))
            .andExpect(jsonPath("$.statusCode").value(ORDER_PRICE_INFO_HAVE_PAID.getCode()))
            .andExpect(jsonPath("$.message").value(ORDER_PRICE_INFO_HAVE_PAID.getMsg()));
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    void from_createOrder_to_executeLegalOperation_ReturnWithDamage() throws Exception {

        OrderTestContext context = new OrderTestContext();

        fromCreateOrderToReturnCarConfirm(context);

        assertNotNull(context.orderNo, "orderNo in context should not be null"); // 確保 orderNo 已被初始化

        List<OrderPriceInfo> orderPriceInfoList = priceInfoService.getPriceInfosByOrder(context.orderNo);
        List<Integer> etagPriceInfoIdsToWriteOff = orderPriceInfoList.stream()
            .filter(opi -> ETag == opi.getCategory() && Pay.getCode() == opi.getType() && !opi.isPaid())
            .map(OrderPriceInfo::getId)
            .collect(Collectors.toList());

        testLegalOperation(
            context.orderNo,
            LegalOperationReason.RETURNED_WITH_DAMAGE,
            9903,
            etagPriceInfoIdsToWriteOff,
            CarDefine.Launched.accident,
            true
        );
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    @DisplayName("續約訂單出車後不會再次建立訂閱客戶約，續約訂單還車資料確認後檢查提前還車優惠回收里程是否正確")
    void renewalOrderDepartShouldNotRecreateLrentalContract() throws Exception {
        OrderTestContext context = new OrderTestContext();
        createOrder(context, 6, true, null);
        approveCreditManually(context);
        setMileageDiscount(context.orderNo);
        payForSecurityDeposit(context);
        payForDepartFeeAndCheckOut(context);
        createOriginalLrentalContract(context);
        departUpdateOrder(context, 160);
        accountSettlementAndCheckOutForDepart(context);
        depart(context);
        verifyCsatTaskCreation(context);
        verifyLrentalContractReCreated(context);
        verifyMileageSynchronizedWithCrs(context);

        // 續約
        renewOrder(context);

        returnCarConfirm(context);
        payForReturnFee(context);
        // 原訂單還車成功
        returnCarSuccess(context);

        assertNotNull(context.renewalOrderNo, "renewalOrderNo in context should not be null"); // 確保 renewalOrderNo 已被初始化
        // 使 context.orderNo 為續約訂單號
        context.orderNo = context.renewalOrderNo;
        approveCreditManually(context);
        // 續約且人工授信後，context.order 設為續約訂單，且其長租契約編號不為 null
        assertNotNull(context.order.getLrentalContractNo());
        context.renewalOrderLrentalContractNo = context.order.getLrentalContractNo();

        setMileageDiscount(context.orderNo);

        payForDepartFeeAndCheckOut(context);

        assertNotEquals(context.recreatedLrentalContractNo, context.renewalOrderLrentalContractNo, "續約訂單長租契約編號應該和原訂單長租契約編號不同");

        depart(context);

        assertEquals(context.renewalOrderLrentalContractNo, orderService.getOrder(context.orderNo).getLrentalContractNo(), "續約訂單出車後不應再重建訂閱客戶約");

        returnCarConfirm(context);
    }

    @Test
    @DisplayName("建立訂單，不需要自動授信，填寫不需要自動授信的原因")
    void createOrderWithAutoCreditBypass() throws Exception {
        OrderTestContext context = new OrderTestContext();
        String autoCreditBypassReason = "超級 VIP 客戶免送中華徵信";
        createOrder(context, 12, false, autoCreditBypassReason);
        Orders order = orderService.getOrder(context.orderNo);
        assertEquals(CreditRemarkType.AUTO_CREDIT_BYPASS, order.getCreditInfo().getAutoCreditInfo().get(0).getCreditRemarkType());
        assertEquals(autoCreditBypassReason, order.getCreditInfo().getManualCreditInfo().getManualCreditRemark());
    }

    @Test
    @DisplayName("建立訂單，需要自動授信")
    void createOrderWithAutoCredit() throws Exception {
        OrderTestContext context = new OrderTestContext();
        createOrder(context, 12, true, null);
        Orders order = orderService.getOrder(context.orderNo);
        assertNotNull(order.getCreditInfo().getAutoCreditInfo(), "自動授信資訊不應為空");
        assertNotEquals(CreditRemarkType.AUTO_CREDIT_BYPASS, order.getCreditInfo().getAutoCreditInfo().get(0).getCreditRemarkType());
        assertNull(order.getCreditInfo().getManualCreditInfo(), "人工授信資訊應為空");
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    @DisplayName("從建立訂單到執行還車成功（不含續約），驗證 CarDepartEvent, OrderReturnedEvent, CarReturnEvent 已被發佈")
    void createOrderToReturnCarSuccess() throws Exception {
        OrderTestContext context = new OrderTestContext();

        // 建立一個 spy/mock ApplicationEventPublisher 並注入到 OrderService
        ApplicationEventPublisher mockPublisher = Mockito.mock(ApplicationEventPublisher.class);
        ReflectionTestUtils.setField(orderService, "eventPublisher", mockPublisher);

        fromCreateOrderToReturnCarConfirm(context);
        payForReturnFee(context);
        returnCarSuccess(context);

        assertNotNull(context.order.getLrentalContractNo(), "訂單長租契約編號不應為 null");

        Thread.sleep(5000);

        // 驗證 CarDepartEvent, OrderReturnedEvent, CarReturnEvent 是否被發佈
        ArgumentCaptor<ApplicationEvent> eventCaptor = ArgumentCaptor.forClass(ApplicationEvent.class);
        verify(mockPublisher, atLeastOnce()).publishEvent(eventCaptor.capture());
        List<CarDepartEvent> departEvents = eventCaptor.getAllValues().stream()
            .filter(CarDepartEvent.class::isInstance)
            .map(CarDepartEvent.class::cast)
            .filter(e -> e.getOrderNo().equals(context.orderNo))
            .collect(Collectors.toList());
        assertFalse(departEvents.isEmpty(), "應至少有一個 CarDepartEvent 被發佈");
        CarDepartEvent departEvent = departEvents.get(0);
        assertEquals(context.orderNo, departEvent.getOrderNo(), "事件中的訂單編號應與測試中使用的相符");
        assertEquals(MEMBER_ID, departEvent.getMemberId(), "事件中的 memberId 應與測試中使用的相符");

        List<OrderReturnedEvent> returnedEvents = eventCaptor.getAllValues().stream()
            .filter(OrderReturnedEvent.class::isInstance)
            .map(OrderReturnedEvent.class::cast)
            .filter(e -> e.getOrder().getOrderNo().equals(context.orderNo))
            .collect(Collectors.toList());
        assertFalse(returnedEvents.isEmpty(), "應至少有一個 OrderReturnedEvent 被發佈");
        OrderReturnedEvent returnedEvent = returnedEvents.get(0);
        assertEquals(context.orderNo, returnedEvent.getOrder().getOrderNo(), "事件中的訂單編號應與測試中使用的相符");
        assertEquals(MEMBER_ID, returnedEvent.getMemberId(), "事件中的 memberId 應與測試中使用的相符");

        List<CarReturnEvent> returnEvents = eventCaptor.getAllValues().stream()
            .filter(CarReturnEvent.class::isInstance)
            .map(CarReturnEvent.class::cast)
            .filter(e -> e.getOrderNo().equals(context.orderNo))
            .collect(Collectors.toList());
        assertFalse(returnEvents.isEmpty(), "應至少有一個 CarReturnEvent 被發佈");
        CarReturnEvent returnEvent = returnEvents.get(0);
        assertEquals(context.orderNo, returnEvent.getOrderNo(), "事件中的訂單編號應與測試中使用的相符");
        assertEquals(MEMBER_ID, returnEvent.getMemberId(), "事件中的 memberId 應與測試中使用的相符");
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    @DisplayName("從建立訂單到執行還車成功（不含續約），再取消已還車，驗證 OrderReopenedEvent 已被發佈")
    void createOrderToReturnCarThenCancelReturnCarShouldPublishOrderReopenedEvent() throws Exception {
        OrderTestContext context = new OrderTestContext();

        // 建立一個 spy/mock ApplicationEventPublisher 並注入到 OrderService
        ApplicationEventPublisher mockPublisher = Mockito.mock(ApplicationEventPublisher.class);
        ReflectionTestUtils.setField(orderService, "eventPublisher", mockPublisher);

        fromCreateOrderToReturnCarConfirm(context);
        payForReturnFee(context);
        returnCarSuccess(context);

        // 取消已還車
        mockMvc.perform(delete("/internal/subscribe/{orderNo}/return", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk());

        // 驗證 OrderReopenedEvent 是否被發佈
        ArgumentCaptor<ApplicationEvent> eventCaptor = ArgumentCaptor.forClass(ApplicationEvent.class);
        verify(mockPublisher, atLeastOnce()).publishEvent(eventCaptor.capture());
        List<OrderReopenedEvent> reopenedEvents = eventCaptor.getAllValues().stream()
            .filter(OrderReopenedEvent.class::isInstance)
            .map(OrderReopenedEvent.class::cast)
            .filter(e -> e.getOrder().getOrderNo().equals(context.orderNo))
            .collect(Collectors.toList());

        assertFalse(reopenedEvents.isEmpty(), "應至少有一個 OrderReopenedEvent 被發佈");

        OrderReopenedEvent event = reopenedEvents.get(0);
        assertEquals(context.orderNo, event.getOrder().getOrderNo(), "事件中的訂單編號應與測試中使用的相符");
        assertEquals(MEMBER_ID, event.getMemberId(), "事件中的 memberId 應與測試中使用的相符");

        int status = event.getOrder().getStatus();
        assertEquals(OrderStatus.ARRIVE_NO_CLOSE.getStatus(), status, "事件中的訂單狀態應為 ARRIVE_NO_CLOSE");
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    @DisplayName("從建立訂單到執行還車成功（不含續約），再取消已還車，然後結案，驗證 OrderClosedEvent 已被發佈")
    void createOrderToReturnCarThenCancelReturnCarThenOrderCloseShouldPublishOrderClosedEvent() throws Exception {
        OrderTestContext context = new OrderTestContext();

        // 建立一個 spy/mock ApplicationEventPublisher 並注入到 OrderService
        ApplicationEventPublisher mockPublisher = Mockito.mock(ApplicationEventPublisher.class);
        ReflectionTestUtils.setField(orderService, "eventPublisher", mockPublisher);

        fromCreateOrderToReturnCarConfirm(context);
        payForReturnFee(context);
        returnCarSuccess(context);

        // 取消已還車
        mockMvc.perform(delete("/internal/subscribe/{orderNo}/return", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk());

        // 執行訂單結案
        OrderCloseAgreeRequest orderCloseAgreeRequest = new OrderCloseAgreeRequest();
        String closeRemark = "取消已還車再結案";
        orderCloseAgreeRequest.setCloseRemark(closeRemark);
        mockMvc.perform(patch("/internal/subscribe/{orderNo}/orderClose", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(orderCloseAgreeRequest)))
            .andExpect(status().isOk());

        // 驗證 OrderClosedEvent 是否被發佈
        ArgumentCaptor<ApplicationEvent> eventCaptor = ArgumentCaptor.forClass(ApplicationEvent.class);
        verify(mockPublisher, atLeastOnce()).publishEvent(eventCaptor.capture());
        List<OrderClosedEvent> closedEvents = eventCaptor.getAllValues().stream()
            .filter(OrderClosedEvent.class::isInstance)
            .map(OrderClosedEvent.class::cast)
            .filter(e -> e.getOrder().getOrderNo().equals(context.orderNo))
            .collect(Collectors.toList());

        assertFalse(closedEvents.isEmpty(), "應至少有一個 OrderClosedEvent 被發佈");

        OrderClosedEvent event = closedEvents.get(0);
        assertEquals(context.orderNo, event.getOrder().getOrderNo(), "事件中的訂單編號應與測試中使用的相符");
        assertEquals(MEMBER_ID, event.getMemberId(), "事件中的 memberId 應與測試中使用的相符");
        assertEquals(MEMBER_ID, event.getOrder().getCloseUser(), "事件中的 closeUser 應與測試中使用的相符");

        int status = event.getOrder().getStatus();
        assertTrue(
            status == OrderStatus.CLOSE.getStatus() || status == OrderStatus.ARRIVE_NO_CLOSE.getStatus(),
            "事件中的訂單狀態應為 CLOSE 或 ARRIVE_NO_CLOSE"
        );

        assertEquals(closeRemark, event.getCloseRemark(), "事件中的訂單結案備註應與測試中使用的相符");
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    @DisplayName("取消提前還車應該清除提前還車所產生的費用、提前還車後沒有付款則應清空 order 實際還車時間 & 里程和清空 ETagInfo orderPriceInfoId")
    void cancelEarlyReturnShouldClearPriceInfoGeneratedAfterReturnCarConfirmAndKeepOrderAndETagInfoStateIfAnyPriceInfoIsPaid() throws Exception {

        OrderTestContext context = new OrderTestContext();

        fromCreateOrderToReturnCarConfirm(context);

        String redisKey = ORDER_STATE + context.orderNo;

        CachedReturnEarlyOrder state = null;
        if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(redisKey))) {
            String stateJson = stringRedisTemplate.opsForValue().get(redisKey);
            if (stateJson != null) {
                try {
                    state = objectMapper.readValue(stateJson, new TypeReference<CachedReturnEarlyOrder>() {
                    });
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        assert state != null;

        // 取消提前還車
        mockMvc.perform(patch("/internal/subscribe/{orderNo}/return/cancelReturnEarly", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"));

        // 驗證取消提前還車後的費用資訊
        List<OrderPriceInfo> orderPriceInfoList = priceInfoService.getPriceInfosByOrder(context.orderNo);
        assertEquals(state.getOrderPriceInfos().size(), orderPriceInfoList.size(), "取消提前還車後應該恢復為提前還車前的費用資訊");
        for (int i = 0; i < state.getOrderPriceInfos().size(); i++) {
            OrderPriceInfo orderPriceInfo = orderPriceInfoList.get(i);
            OrderPriceInfoDTO stateOrderPriceInfo = state.getOrderPriceInfos().get(i);
            assertEquals(stateOrderPriceInfo.getId(), orderPriceInfo.getId(), "取消提前還車後應該恢復為提前還車前的費用資訊");
            assertEquals(stateOrderPriceInfo.getStage(), orderPriceInfo.getStage(), "取消提前還車後應該恢復為提前還車前的費用資訊");
            assertEquals(stateOrderPriceInfo.getCategory(), orderPriceInfo.getCategory(), "取消提前還車後應該恢復為提前還車前的費用資訊");
            assertEquals(stateOrderPriceInfo.getType(), orderPriceInfo.getType(), "取消提前還車後應該恢復為提前還車前的費用資訊");
            assertEquals(stateOrderPriceInfo.getAmount(), orderPriceInfo.getAmount(), "取消提前還車後應該恢復為提前還車前的費用資訊");
            assertEquals(stateOrderPriceInfo.getReceivedAmount(), orderPriceInfo.getReceivedAmount(), "取消提前還車後應該恢復為提前還車前的費用資訊");
            assertEquals(stateOrderPriceInfo.getInfoDetail(), orderPriceInfo.getInfoDetail(), "取消提前還車後應該恢復為提前還車前的費用資訊");
            assertEquals(stateOrderPriceInfo.getRemitAccountIds(), orderPriceInfo.getRemitAccountIds(), "取消提前還車後應該恢復為提前還車前的費用資訊");
            assertEquals(stateOrderPriceInfo.getPaymentId(), orderPriceInfo.getPaymentId(), "取消提前還車後應該恢復為提前還車前的費用資訊");
            assertEquals(stateOrderPriceInfo.getRecTradeId(), orderPriceInfo.getRecTradeId(), "取消提前還車後應該恢復為提前還車前的費用資訊");
            assertEquals(stateOrderPriceInfo.getRefundId(), orderPriceInfo.getRefundId(), "取消提前還車後應該恢復為提前還車前的費用資訊");
            assertEquals(stateOrderPriceInfo.getRefPriceInfoNo(), orderPriceInfo.getRefPriceInfoNo(), "取消提前還車後應該恢復為提前還車前的費用資訊");
            assertEquals(stateOrderPriceInfo.getUid(), orderPriceInfo.getUid(), "取消提前還車後應該恢復為提前還車前的費用資訊");
            assertTrue((orderPriceInfo.isPaid() && stateOrderPriceInfo.revertToEntity().isPaid()
                || !orderPriceInfo.isPaid() && !stateOrderPriceInfo.revertToEntity().isPaid()));
        }

        Orders order = orderService.getOrder(context.orderNo);
        assertNull(order.getEndDate(), "取消提前還車後應該清除提前還車所產生的實際還車時間");
        assertNull(order.getReturnMileage(), "取消提前還車後應該清除提前還車所產生的實際還車里程");

        ETagInfo etagInfo = etagService.getLatestNotReturnETagInfo(order, true);
        assertEquals(state.getEtagInfo().revertToEntity(), etagInfo, "ETagInfo 應該還原為提前還車前的狀態");
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    @DisplayName("取消提前還車應該清除提前還車所產生的費用、提前還車後有付款則不應清空 order 實際還車時間 & 里程和 ETagInfo orderPriceInfoId")
    void cancelEarlyReturnShouldClearPriceInfoGeneratedAfterReturnCarConfirmAndClearOrderAndETagInfoStateIfNoPriceInfoIsPaid() throws Exception {

        OrderTestContext context = new OrderTestContext();

        fromCreateOrderToReturnCarConfirm(context);

        String redisKey = ORDER_STATE + context.orderNo;

        CachedReturnEarlyOrder state = null;
        if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(redisKey))) {
            String stateJson = stringRedisTemplate.opsForValue().get(redisKey);
            if (stateJson != null) {
                try {
                    state = objectMapper.readValue(stateJson, new TypeReference<CachedReturnEarlyOrder>() {
                    });
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        assert state != null;

        // 步驟 13: 支付還車費用
        payForReturnFee(context);

        Map<Integer, OrderPriceInfo> priceInfoMapAfterPayingReturnFee = priceInfoService.getPriceInfosByOrder(context.orderNo).stream()
            .peek(orderPriceInfo -> assertTrue(orderPriceInfo.isPaid(), "支付還車費用後所有里程費應為已付款"))
            .collect(Collectors.toMap(OrderPriceInfo::getId, Function.identity()));

        // 新增額外費用
        int stage = orderService.getOrder(context.orderNo).getCurrentStage().getStage();
        ExtraFeeRequest extraFeeRequest = new ExtraFeeRequest();
        extraFeeRequest.setExtraFeeList(Collections.singletonList(ExtraFeeRequest.ExtraFee.builder()
            .amount(1000)
            .stage(stage)
            .reason("test")
            .category(Others).build()));
        MvcResult setExtraFeeResult = mockMvc.perform(post("/internal/subscribe/priceInfo/{orderNo}/extraFee", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(extraFeeRequest)))
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andReturn();
        List<OrderPriceInfo> extraFeeInfos = objectMapper.readValue(objectMapper.readTree(setExtraFeeResult.getResponse().getContentAsString()).get("data").toString(), new TypeReference<List<OrderPriceInfo>>() {
        });
        OrderPriceInfo extraFee = extraFeeInfos.stream().max(Comparator.comparing(OrderPriceInfo::getId)).get();
        assertFalse(extraFee.isPaid());

        // 取消提前還車
        mockMvc.perform(patch("/internal/subscribe/{orderNo}/return/cancelReturnEarly", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"));

        // 驗證取消提前還車後的費用資訊
        Map<Boolean, List<OrderPriceInfo>> orderPriceInfoListAfterCancelReturnEarly = priceInfoService.getPriceInfosByOrder(context.orderNo)
            .stream().collect(Collectors.groupingBy(OrderPriceInfo::isPaid));
        for (OrderPriceInfo paidOrderPriceInfoAfterCancelReturnEarly : orderPriceInfoListAfterCancelReturnEarly.get(true)) {
            OrderPriceInfo orderPriceInfoAfterPayingReturnFee = priceInfoMapAfterPayingReturnFee.get(paidOrderPriceInfoAfterCancelReturnEarly.getId());
            assertEquals(orderPriceInfoAfterPayingReturnFee, paidOrderPriceInfoAfterCancelReturnEarly, "取消提前還車後應該恢復為支付還車費用後的費用資訊");
            assertEquals(orderPriceInfoAfterPayingReturnFee.getInfoDetail(), paidOrderPriceInfoAfterCancelReturnEarly.getInfoDetail(), "取消提前還車後應該恢復為支付還車費用後的費用資訊");
        }

        // 取消提前還車後的費用資訊中，未付款的費用應該只有額外費用
        List<OrderPriceInfo> unpaidOrderPriceInfosAfterCancelReturnEarly = orderPriceInfoListAfterCancelReturnEarly.get(false);
        assertTrue(unpaidOrderPriceInfosAfterCancelReturnEarly.size() == 1
            && Objects.equals(unpaidOrderPriceInfosAfterCancelReturnEarly.get(0).getId(), extraFee.getId()), "取消提前還車後，在提前還車且支付費用後新增的額外費用應該不變");
        assertEquals(extraFee, unpaidOrderPriceInfosAfterCancelReturnEarly.get(0), "取消提前還車後，在提前還車且支付費用後新增的額外費用應該不變");

        Orders order = orderService.getOrder(context.orderNo);
        assertNotNull(order.getEndDate(), "取消提前還車後若有付款不應清除提前還車所產生的實際還車時間");
        assertNotNull(order.getReturnMileage(), "取消提前還車後若有付款不應清除提前還車所產生的實際還車里程");

        ETagInfo etagInfo = etagService.getLatestNotReturnETagInfo(order, true);
        assertNotEquals(state.getEtagInfo().revertToEntity(), etagInfo, "取消提前還車後若有付款 ETagInfo 不應還原為提前還車前的狀態");
    }
}