package com.carplus.subscribe.utils;

import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Image;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.pdf.PdfWriter;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.ByteBuffer;

public final class PdfUtil {

    public static <T> void createPdf(byte[] content, OutputStream output) throws DocumentException, IOException {
        Document document = new Document(PageSize.A4);
        PdfWriter.getInstance(document, output);
        document.open();
        Image image = Image.getInstance(new ByteArrayInputStream(content).toString());
        document.add(image);
        document.close();
    }

    public static class ByteArrayOutputStream2ByteBuffer extends ByteArrayOutputStream {

        public ByteBuffer toByteBuffer() {
            return ByteBuffer.wrap(buf, 0, count);
        }
    }
}