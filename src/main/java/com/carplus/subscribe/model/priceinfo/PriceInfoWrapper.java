package com.carplus.subscribe.model.priceinfo;

import com.carplus.subscribe.db.mysql.dao.OrderPriceInfoRepository;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.enums.PriceInfoDefinition;
import com.carplus.subscribe.model.OrderPriceInfoCriteria;
import com.carplus.subscribe.service.PriceInfoService;
import com.carplus.subscribe.utils.DateUtil;

import java.lang.reflect.Constructor;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoCategory.ETag;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoCategory.SecurityDeposit;

public class PriceInfoWrapper {

    public PriceInfoWrapper(OrderPriceInfoRepository orderPriceInfoRepository, String orderNo) {
        orderPriceInfoList = orderPriceInfoRepository.getPriceInfos(OrderPriceInfoCriteria.builder().orderNo(Collections.singletonList(orderNo)).build());
    }

    public PriceInfoWrapper(PriceInfoService priceInfoService, String orderNo) {
        orderPriceInfoList = priceInfoService.getPriceInfosByOrder(orderNo);
    }

    protected PriceInfoWrapper(List<OrderPriceInfo> orderPriceInfoList) {
        if (orderPriceInfoList == null || orderPriceInfoList.isEmpty()) {
            orderPriceInfoList = new ArrayList<>();
        }
        this.orderPriceInfoList = orderPriceInfoList;
    }


    protected final List<OrderPriceInfo> orderPriceInfoList;
    public Map<PriceInfoDefinition.PriceInfoCategory, PriceInfoWrapper> categoryListMap;
    public Map<PriceInfoDefinition.PriceInfoType, PriceInfoWrapper> typeListMap;
    public Map<Integer, OrderPriceInfo> idMap;

    public List<OrderPriceInfo> getList() {
        return orderPriceInfoList;
    }

    public PriceInfoWrapper getByCategory(PriceInfoDefinition.PriceInfoCategory category) {
        if (categoryListMap == null) {
            categoryListMap = new HashMap<>();
            orderPriceInfoList.stream().collect(Collectors.groupingBy(OrderPriceInfo::getCategory)).forEach((key, value) -> {
                    PriceInfoWrapper newWrapper = null;
                    if (category == PriceInfoDefinition.PriceInfoCategory.MileageFee) {
                        newWrapper = new MileagePriceInfoWrapper(value);
                    } else {
                        newWrapper = new PriceInfoWrapper(value);
                    }
                    categoryListMap.put(key, newWrapper);
                }
            );

        }
        return categoryListMap.getOrDefault(category, new PriceInfoWrapper(new ArrayList<>()));
    }

    public PriceInfoWrapper getByType(PriceInfoDefinition.PriceInfoType type) {
        if (typeListMap == null) {
            typeListMap = new HashMap<>();
            orderPriceInfoList.stream().collect(Collectors.groupingBy(OrderPriceInfo::getType)).forEach((key, value) -> typeListMap.put(PriceInfoDefinition.PriceInfoType.of(key), constructor(value)));
        }
        return typeListMap.getOrDefault(type, new PriceInfoWrapper(new ArrayList<>()));
    }

    public PriceInfoWrapper getByCategoryType(PriceInfoDefinition.PriceInfoCategory category, PriceInfoDefinition.PriceInfoType type) {
        if (category == null) {
            return getByType(type);
        }
        if (type == null) {
            return getByCategory(category);
        }
        return getByCategory(category).getByType(type);
    }

    public PriceInfoWrapper getUnpaid() {
        return constructor(orderPriceInfoList.stream()
            .filter(orderPriceInfo -> !orderPriceInfo.isPaid())
            .collect(Collectors.toList()));
    }

    public PriceInfoWrapper getPaid() {
        return constructor(orderPriceInfoList.stream()
            .filter(PriceInfoInterface::isPaid)
            .collect(Collectors.toList()));
    }

    public PriceInfoWrapper getCredit() {
        return constructor(orderPriceInfoList.stream()
            .filter(PriceInfoInterface::isCredit)
            .collect(Collectors.toList()));
    }

    /**
     * 應付款但未付款金額
     */
    public PriceInfoWrapper getUnpaidAvailable() {
        return constructor(orderPriceInfoList.stream()
            .filter(orderPriceInfo -> !orderPriceInfo.isPaid() && orderPriceInfo.getReceivableDate().isBefore(Instant.now()))
            .collect(Collectors.toList()));
    }

    /**
     * ReceivableDate is before now
     */
    public PriceInfoWrapper getAvailable() {
        return constructor(orderPriceInfoList.stream()
            .filter(orderPriceInfo -> orderPriceInfo.getReceivableDate().isBefore(Instant.now()))
            .collect(Collectors.toList()));
    }

    public PriceInfoWrapper getCurrentReceivable() {
        return constructor(orderPriceInfoList.stream()
            .filter(orderPriceInfo -> orderPriceInfo.getCategory() != SecurityDeposit
                && orderPriceInfo.getCategory() != ETag
                && (orderPriceInfo.getReceivableDate().compareTo(DateUtil.convertToEndOfInstant(Instant.now())) <= 0
                || orderPriceInfo.getReceivedAmount() != 0)
                && (orderPriceInfo.getRefPriceInfoNo() == null || Optional.ofNullable(orderPriceInfo.getRefPriceInfo()).map(OrderPriceInfo::getCategory).orElse(null) != SecurityDeposit)
            ).collect(Collectors.toList()));
    }


    public PriceInfoWrapper excludeCategory(PriceInfoDefinition.PriceInfoCategory category) {
        return constructor(orderPriceInfoList.stream()
            .filter(orderPriceInfo -> !orderPriceInfo.getCategory().equals(category))
            .collect(Collectors.toList()));
    }

    public PriceInfoWrapper excludeSecurityDeposit() {
        return constructor(orderPriceInfoList.stream()
            .filter(orderPriceInfo -> !orderPriceInfo.getCategory().equals(PriceInfoDefinition.PriceInfoCategory.SecurityDeposit))
            .collect(Collectors.toList()));
    }

    public PriceInfoWrapper excludeETag() {
        return constructor(orderPriceInfoList.stream()
            .filter(orderPriceInfo -> !orderPriceInfo.getCategory().equals(PriceInfoDefinition.PriceInfoCategory.ETag))
            .collect(Collectors.toList()));
    }

    public PriceInfoWrapper excludeType(PriceInfoDefinition.PriceInfoType... type) {
        List<Integer> typeCodes = Arrays.stream(type).map(PriceInfoDefinition.PriceInfoType::getCode).collect(Collectors.toList());
        return constructor(orderPriceInfoList.stream()
            .filter(orderPriceInfo -> !typeCodes.contains(orderPriceInfo.getType()))
            .collect(Collectors.toList()));
    }

    public PriceInfoWrapper getPaidOnline() {
        return constructor(orderPriceInfoList.stream()
            .filter(orderPriceInfo -> orderPriceInfo.getPaymentId() != null)
            .collect(Collectors.toList()));
    }

    public PriceInfoWrapper getPaidRemit() {
        return constructor(orderPriceInfoList.stream()
            .filter(orderPriceInfo -> orderPriceInfo.getRemitAccountIds() != null && !orderPriceInfo.getRemitAccountIds().isEmpty())
            .collect(Collectors.toList()));
    }

    public OrderPriceInfo getById(Integer id) {
        if (idMap == null) {
            idMap = orderPriceInfoList.stream().collect(Collectors.toMap(OrderPriceInfo::getId, orderPriceInfo -> orderPriceInfo));
        }
        return idMap.get(id);
    }

    public int getActualPrice() {
        return orderPriceInfoList.stream()
            .mapToInt(PriceInfoInterface::getActualPrice)
            .sum();
    }

    public int getActualReceivePrice() {
        return orderPriceInfoList.stream()
            .mapToInt(PriceInfoInterface::getActualReceivePrice)
            .sum();
    }

    private PriceInfoWrapper constructor(List<OrderPriceInfo> value) {
        try {
            Constructor<?> constructor = this.getClass().getDeclaredConstructor(List.class);

            return (PriceInfoWrapper) constructor.newInstance(value);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


}
