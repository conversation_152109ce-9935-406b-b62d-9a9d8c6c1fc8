package com.carplus.subscribe.service;

import carplus.common.utils.DateUtils;
import com.carplus.subscribe.constant.PDFConstant;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Image;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import org.apache.commons.io.FileUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.time.Instant;
import java.util.Date;

@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class EcontractServiceTest {

    @Autowired
    EContractService eContractService;

    @Test
    public void test() {

    }

    // 測試插入電子簽名檔、承租人身份證字號、承租人電子簽名日期、合約編號
    @Test
    public void testEContractSigning() throws IOException, DocumentException {
        PdfReader reader = null;
        PdfStamper stamper = null;
        ByteArrayOutputStream byteArrayOutputStream = null;
        try {
            // 讀取本地檔案
            String pdfPath = "D:\\car-plus\\contract.pdf";
            String signPath = "D:\\car-plus\\signature.png";
            reader = new PdfReader(pdfPath, "idNo".getBytes());
            PdfReader.unethicalreading = true;
            byteArrayOutputStream = new ByteArrayOutputStream();
            stamper = new PdfStamper(reader, byteArrayOutputStream);
            Image image = Image.getInstance(signPath);
            PdfContentByte under = stamper.getOverContent(reader.getNumberOfPages());
            image.scaleToFit(PDFConstant.SignatureWidth.floatValue(), PDFConstant.SignatureHeight.floatValue());
            float signatureY = PDFConstant.PageHeight.floatValue() - PDFConstant.Y.floatValue() - PDFConstant.SignatureHeight.floatValue();
            image.setAbsolutePosition(PDFConstant.X.floatValue(), signatureY);
            under.addImage(image);

            under.beginText();
            BaseFont baseFont = BaseFont.createFont("fonts/微軟正黑體/微軟正黑體.ttf", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
            under.setFontAndSize(baseFont, 12);
            float loginIdAndSignDateY = signatureY + PDFConstant.CompensationHeight.floatValue();
            under.setTextMatrix(PDFConstant.loginIdX.floatValue(), loginIdAndSignDateY);
            // 插入承租人身份證字號
            under.showText("A123456789");

            Instant eContractSignDate = Instant.now();
            under.setTextMatrix(PDFConstant.eContractSignDateX.floatValue(), loginIdAndSignDateY);
            // 插入承租人簽章日期
            under.showText(DateUtils.toDateString(Date.from(eContractSignDate), "yyyy/MM/dd"));
            under.endText();

            // 在每一頁插入合約編號
            eContractService.stampContractNoOnPdf("C2024111900001", reader, stamper, baseFont);

            // 重要：必須先關閉 stamper，再處理輸出
            stamper.close();

            // 輸出到本地
            String outputPath = "D:\\car-plus\\contract_output.pdf";
            FileUtils.writeByteArrayToFile(new File(outputPath), byteArrayOutputStream.toByteArray());

        } finally {
            // 依照正確順序關閉資源
            if (stamper != null) {
                try {
                    stamper.close();
                } catch (Exception e) {
                    // 忽略關閉時的錯誤
                }
            }
            if (byteArrayOutputStream != null) {
                try {
                    byteArrayOutputStream.close();
                } catch (Exception e) {
                    // 忽略關閉時的錯誤
                }
            }
            if (reader != null) {
                reader.close();
            }
        }
    }
}
