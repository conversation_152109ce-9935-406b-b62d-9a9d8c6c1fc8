package com.carplus.subscribe.controller.dealer;

import carplus.common.enums.HeaderDefine;
import carplus.common.enums.etag.ETagFlow;
import carplus.common.enums.etag.ETagPayFlow;
import carplus.common.utils.DateUtils;
import com.carplus.subscribe.App;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrder;
import com.carplus.subscribe.enums.*;
import com.carplus.subscribe.model.cars.ProcessingOrder;
import com.carplus.subscribe.model.cars.resp.CarResponse;
import com.carplus.subscribe.model.crs.CarBaseInfoSearchResponse;
import com.carplus.subscribe.model.etag.EtagCloseRequest;
import com.carplus.subscribe.model.etag.EtagInfoResponse;
import com.carplus.subscribe.model.order.LrentalContractRequest;
import com.carplus.subscribe.model.request.dealer.*;
import com.carplus.subscribe.model.response.dealer.DealerOrderQueryResponse;
import com.carplus.subscribe.model.response.dealer.DealerOrderResponse;
import com.carplus.subscribe.service.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static carplus.common.response.CarPlusCode.API_USE_INCORRECT;
import static com.carplus.subscribe.constant.CarPlusConstant.SEALAND_VIRTUAL_PLATE_NO;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(classes = App.class)
@AutoConfigureMockMvc
@Transactional(transactionManager = "mysqlTransactionManager", isolation = Isolation.READ_COMMITTED)
class DealerOrderInternalControllerTest {

    @Autowired
    private MockMvc mockMvc;
    @Autowired
    private DealerOrderService dealerOrderService;
    @Autowired
    private CarsService carsService;
    @Autowired
    private CrsService crsService;
    @Autowired
    private ETagService eTagService;
    @Autowired
    private DealerOrderPriceInfoService dealerOrderPriceInfoService;
    @Autowired
    private ObjectMapper objectMapper;

    private final String HEADER_MEMBER_ID = "K2765";
    private static final String PLATE_NO = "RFA-6076";
    private static final String PLATE_NO_VIRTUAL = "RAA0020";
    private static final String PLATE_NO_NEW = "RFA-3978";
    private static final String ORDER_NO = "M202404188888";
    private static final String CANCEL_ORDER_NO = "20250224007";
    private static final String EXPECT_DEPART_STATION = "831";
    private static final String EXPECT_RETURN_STATION = "201";
    private static final String ID_NO = "A195970863";

    private static final String DEALER_ORDER_REQUEST_BODY = "{\n" +
        "  \"orderNo\": \"" + ORDER_NO + "\",\n" +
        "  \"dealerName\": null,\n" +
        "  \"orderStatus\": null,\n" +
        "  \"isNewOrder\": true,\n" +
        "  \"isAudit\": null,\n" +
        "  \"isPaySecurityDeposit\": null,\n" +
        "  \"securityDepositDate\": \"2024-04-17T09:30:10.404Z\",\n" +
        "  \"parentOrderNo\": \"" + ORDER_NO + "\",\n" +
        "  \"stage\": \"1-3\",\n" +
        "  \"plateNo\": \"" + PLATE_NO + "\",\n" +
        "  \"insuranceId\": \"2024001\",\n" +
        "  \"infoDetail\": \"string\",\n" +
        "  \"isRegister\": true,\n" +
        "  \"customerInfo\": {\n" +
        "    \"userName\": \"Hans\",\n" +
        "    \"isForeigner\": 0,\n" +
        "    \"idNo\": \"" + ID_NO + "\",\n" +
        "    \"nationalCode\": \"886\",\n" +
        "    \"mainCell\": \"0912345678\",\n" +
        "    \"birthDay\": \"2000-01-01\",\n" +
        "    \"email\": \"<EMAIL>\",\n" +
        "    \"city\": 3,\n" +
        "    \"area\": 23,\n" +
        "    \"address\": \"茄苳路116號rL=4(格上員工)\",\n" +
        "    \"vatNumber\": \"12208883\",\n" +
        "    \"companyName\": \"格上汽車租賃股份有限公司\",\n" +
        "    \"companyLocation\": \"臺北市大安區敦化南路2段2號11樓\"\n" +
        "  },\n" +
        "  \"subscriptionInfo\": {\n" +
        "    \"securityDeposit\": 10000,\n" +
        "    \"monthlyFee\": 8800,\n" +
        "    \"actualMileageRate\": 2.8,\n" +
        "    \"originalMileageRate\": 3,\n" +
        "    \"expectDepartStation\": \"" + EXPECT_DEPART_STATION + "\",\n" +
        "    \"expectReturnStation\": \"201\",\n" +
        "    \"expectDepartDate\": \"2024-04-17T09:30:10.404Z\",\n" +
        "    \"expectReturnDate\": \"2024-04-27T09:30:10.404Z\",\n" +
        "    \"subscribeMonth\": 3,\n" +
        "    \"totalAmt\": 26820,\n" +
        "    \"monthlyFeeDiscount\": 0,\n" +
        "    \"mileageRateDiscount\": 0.5,\n" +
        "    \"prepaidMonths\": 3,\n" +
        "    \"prepaidMileage\": 3000,\n" +
        "    \"prepaidMileageDiscount\": 0,\n" +
        "    \"flexibleMileage\": 0,\n" +
        "    \"actualMileageUsed\": 0,\n" +
        "    \"offsetMileage1\": 0,\n" +
        "    \"offsetMileage2\": 0,\n" +
        "    \"prepaidMileageFee\": 8400\n" +
        "  }\n" +
        "}";

    private static final String DEALER_ORDER_UPDATE_REQUEST_BODY = "{\n" +
        "  \"orderNo\": \"" + ORDER_NO + "\",\n" +
        "  \"plateNo\": \"" + PLATE_NO + "\",\n" +
        "  \"customerInfo\": {\n" +
        "    \"userName\": \"Hans123\",\n" + // Field to update
        "    \"isForeigner\": 0,\n" +
        "    \"idNo\": \"" + ID_NO + "\",\n" +
        "    \"nationalCode\": \"886\",\n" +
        "    \"mainCell\": \"0912345678\",\n" +
        "    \"birthDay\": \"2000-01-01\",\n" +
        "    \"email\": \"<EMAIL>\",\n" +
        "    \"city\": 3,\n" +
        "    \"area\": 23,\n" +
        "    \"address\": \"茄苳路116號rL=4(格上員工)\",\n" +
        "    \"vatNumber\": \"12208883\",\n" +
        "    \"companyName\": \"格上汽車租賃股份有限公司\",\n" +
        "    \"companyLocation\": \"臺北市大安區敦化南路2段2號11樓\"\n" +
        "  },\n" +
        "  \"subscriptionInfo\": {\n" +
        "    \"securityDeposit\": 12000,\n" + // Field to update
        "    \"monthlyFee\": 8800,\n" +
        "    \"actualMileageRate\": 2.8,\n" +
        "    \"originalMileageRate\": 3,\n" +
        "    \"expectDepartStation\": \"" + EXPECT_DEPART_STATION + "\",\n" +
        "    \"expectReturnStation\": \"201\",\n" +
        "    \"expectDepartDate\": \"2024-04-17T09:30:10.404Z\",\n" +
        "    \"expectReturnDate\": \"2024-04-27T09:30:10.404Z\",\n" +
        "    \"subscribeMonth\": 3,\n" +
        "    \"totalAmt\": 26820,\n" +
        "    \"monthlyFeeDiscount\": 0,\n" +
        "    \"mileageRateDiscount\": 0.5,\n" +
        "    \"prepaidMonths\": 3,\n" +
        "    \"prepaidMileage\": 3000,\n" +
        "    \"prepaidMileageDiscount\": 0,\n" +
        "    \"flexibleMileage\": 0,\n" +
        "    \"actualMileageUsed\": 0,\n" +
        "    \"offsetMileage1\": 0,\n" +
        "    \"offsetMileage2\": 0,\n" +
        "    \"prepaidMileageFee\": 8400\n" +
        "  }\n" +
        "}";

    private static final String DEALER_ORDER_UPDATE_REQUEST_BODY_REMOVE_CUSTOMER_INFO_NO_CHANGE_FIELDS = "{\n" +
        "  \"orderNo\": \"" + ORDER_NO + "\",\n" +
        "  \"plateNo\": \"" + PLATE_NO + "\",\n" +
        "  \"customerInfo\": {\n" +
        "    \"userName\": \"Hans123\",\n" + // Field to update
        "    \"idNo\": \"" + ID_NO + "\"\n" +
        "  },\n" +
        "  \"subscriptionInfo\": {\n" +
        "    \"securityDeposit\": 12000,\n" + // Field to update
        "    \"monthlyFee\": 8800,\n" +
        "    \"actualMileageRate\": 2.8,\n" +
        "    \"originalMileageRate\": 3,\n" +
        "    \"expectDepartStation\": \"" + EXPECT_DEPART_STATION + "\",\n" +
        "    \"expectReturnStation\": \"201\",\n" +
        "    \"expectDepartDate\": \"2024-04-17T09:30:10.404Z\",\n" +
        "    \"expectReturnDate\": \"2024-04-27T09:30:10.404Z\",\n" +
        "    \"subscribeMonth\": 3,\n" +
        "    \"totalAmt\": 26820,\n" +
        "    \"monthlyFeeDiscount\": 0,\n" +
        "    \"mileageRateDiscount\": 0.5,\n" +
        "    \"prepaidMonths\": 3,\n" +
        "    \"prepaidMileage\": 3000,\n" +
        "    \"prepaidMileageDiscount\": 0,\n" +
        "    \"flexibleMileage\": 0,\n" +
        "    \"actualMileageUsed\": 0,\n" +
        "    \"offsetMileage1\": 0,\n" +
        "    \"offsetMileage2\": 0,\n" +
        "    \"prepaidMileageFee\": 8400\n" +
        "  }\n" +
        "}";

    private static final String DEALER_ORDER_UPDATE_REQUEST_BODY_CHANGE_VALID_ID_NO = "{\n" +
        "  \"orderNo\": \"" + ORDER_NO + "\",\n" +
        "  \"plateNo\": \"" + PLATE_NO + "\",\n" +
        "  \"customerInfo\": {\n" +
        "    \"userName\": \"Hans123\",\n" + // Field to update
        "    \"idNo\": \"F172455322\"\n" +
        "  },\n" +
        "  \"subscriptionInfo\": {\n" +
        "    \"securityDeposit\": 12000,\n" + // Field to update
        "    \"monthlyFee\": 8800,\n" +
        "    \"actualMileageRate\": 2.8,\n" +
        "    \"originalMileageRate\": 3,\n" +
        "    \"expectDepartStation\": \"" + EXPECT_DEPART_STATION + "\",\n" +
        "    \"expectReturnStation\": \"201\",\n" +
        "    \"expectDepartDate\": \"2024-04-17T09:30:10.404Z\",\n" +
        "    \"expectReturnDate\": \"2024-04-27T09:30:10.404Z\",\n" +
        "    \"subscribeMonth\": 3,\n" +
        "    \"totalAmt\": 26820,\n" +
        "    \"monthlyFeeDiscount\": 0,\n" +
        "    \"mileageRateDiscount\": 0.5,\n" +
        "    \"prepaidMonths\": 3,\n" +
        "    \"prepaidMileage\": 3000,\n" +
        "    \"prepaidMileageDiscount\": 0,\n" +
        "    \"flexibleMileage\": 0,\n" +
        "    \"actualMileageUsed\": 0,\n" +
        "    \"offsetMileage1\": 0,\n" +
        "    \"offsetMileage2\": 0,\n" +
        "    \"prepaidMileageFee\": 8400\n" +
        "  }\n" +
        "}";

    private static final String DEALER_ORDER_UPDATE_REQUEST_BODY_DEALER_USER_ID_0 = "{\n" +
        "  \"orderNo\": \"M202406070002\",\n" +
        "  \"plateNo\": \"RDZ-7108\",\n" +
        "  \"customerInfo\": {\n" +
        "    \"userName\": \"ValidIdTest\",\n" +
        "    \"isForeigner\": 0,\n" +
        "    \"idNo\": \"F172455322\",\n" +
        "    \"nationalCode\": \"886\",\n" +
        "    \"mainCell\": \"0912345678\",\n" +
        "    \"birthDay\": \"2000-01-01\",\n" +
        "    \"email\": \"<EMAIL>\",\n" +
        "    \"city\": 3,\n" +
        "    \"area\": 23,\n" +
        "    \"address\": \"茄苳路116號rL=4(格上員工)\",\n" +
        "    \"vatNumber\": \"12208883\",\n" +
        "    \"companyName\": \"格上汽車租賃股份有限公司\",\n" +
        "    \"companyLocation\": \"臺北市大安區敦化南路2段2號11樓\"\n" +
        "  },\n" +
        "  \"subscriptionInfo\": {\n" +
        "    \"securityDeposit\": 12000,\n" + // Field to update
        "    \"monthlyFee\": 8800,\n" +
        "    \"actualMileageRate\": 2.8,\n" +
        "    \"originalMileageRate\": 3,\n" +
        "    \"expectDepartStation\": \"" + EXPECT_DEPART_STATION + "\",\n" +
        "    \"expectReturnStation\": \"201\",\n" +
        "    \"expectDepartDate\": \"2024-04-17T09:30:10.404Z\",\n" +
        "    \"expectReturnDate\": \"2024-04-27T09:30:10.404Z\",\n" +
        "    \"subscribeMonth\": 3,\n" +
        "    \"totalAmt\": 26820,\n" +
        "    \"monthlyFeeDiscount\": 0,\n" +
        "    \"mileageRateDiscount\": 0.5,\n" +
        "    \"prepaidMonths\": 3,\n" +
        "    \"prepaidMileage\": 3000,\n" +
        "    \"prepaidMileageDiscount\": 0,\n" +
        "    \"flexibleMileage\": 0,\n" +
        "    \"actualMileageUsed\": 0,\n" +
        "    \"offsetMileage1\": 0,\n" +
        "    \"offsetMileage2\": 0,\n" +
        "    \"prepaidMileageFee\": 8400\n" +
        "  }\n" +
        "}";

    private static final String DEPART_DEALER_ORDER_REQUEST_BODY = "{\n" +
        "  \"orderNo\": \"" + ORDER_NO + "\",\n" +
        "  \"plateNo\": \"" + PLATE_NO + "\",\n" +
        "  \"orderStatus\": " + ContractStatus.GOING.getCode() + ",\n" +
        "  \"subscriptionInfo\": {\n" +
        "    \"departStation\": \"35\",\n" +
        "    \"departDate\": \"2024-04-17T09:30:10.404Z\",\n" +
        "    \"totalAmt\": null,\n" +
        "    \"beginAmt\": 34800\n" +
        "  }\n" +
        "}";

    private static final String DEPART_DEALER_ORDER_REQUEST_BODY_NO_DEPART_STATION = "{\n" +
        "  \"orderNo\": \"" + ORDER_NO + "\",\n" +
        "  \"plateNo\": \"" + PLATE_NO + "\",\n" +
        "  \"orderStatus\": " + ContractStatus.GOING.getCode() + ",\n" +
        "  \"subscriptionInfo\": {\n" +
        "    \"departStation\": null,\n" +
        "    \"departDate\": \"2024-04-17T09:30:10.404Z\",\n" +
        "    \"totalAmt\": 26820,\n" +
        "    \"beginAmt\": 34800\n" +
        "  }\n" +
        "}";

    private static final String CLOSE_ETAG_REQUEST_BODY = "{\n" +
        "  \"returnDate\": \"2024-04-17T09:30:10.404Z\"\n" +
        "}";

    private static final String CLOSE_DEALER_ORDER_REQUEST_BODY = "{\n" +
        "  \"orderNo\": \"" + ORDER_NO + "\",\n" +
        "  \"plateNo\": \"" + PLATE_NO + "\",\n" +
        "  \"orderStatus\": " + ContractStatus.COMPLETE.getCode() + ",\n" +
        "  \"subscriptionInfo\": {\n" +
        "    \"returnStation\": \"35\",\n" +
        "    \"returnDate\": \"2024-04-17T09:30:10.404Z\",\n" +
        "    \"totalAmt\": null,\n" +
        "    \"isReturned\": false,\n" +
        "    \"closeAmt\": -34800\n" +
        "  }\n" +
        "}";

    private static final String CLOSE_DEALER_ORDER_REQUEST_BODY_NO_RETURN_STATION = "{\n" +
        "  \"orderNo\": \"" + ORDER_NO + "\",\n" +
        "  \"plateNo\": \"" + PLATE_NO + "\",\n" +
        "  \"orderStatus\": " + ContractStatus.COMPLETE.getCode() + ",\n" +
        "  \"subscriptionInfo\": {\n" +
        "    \"returnStation\": null,\n" +
        "    \"returnDate\": \"2024-04-17T09:30:10.404Z\",\n" +
        "    \"totalAmt\": 26820,\n" +
        "    \"isReturned\": false,\n" +
        "    \"closeAmt\": 34800\n" +
        "  }\n" +
        "}";

    private static final String CLOSE_DEALER_ORDER_REQUEST_BODY_IS_RETURNED_TRUE = "{\n" +
        "  \"orderNo\": \"" + ORDER_NO + "\",\n" +
        "  \"plateNo\": \"" + PLATE_NO + "\",\n" +
        "  \"orderStatus\": " + ContractStatus.COMPLETE.getCode() + ",\n" +
        "  \"subscriptionInfo\": {\n" +
        "    \"returnStation\": \"35\",\n" +
        "    \"returnDate\": \"2024-04-17T09:30:10.404Z\",\n" +
        "    \"totalAmt\": 26820,\n" +
        "    \"isReturned\": true,\n" +
        "    \"closeAmt\": 34800\n" +
        "    }\n" +
        "}";

    private static final String cancelDate = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
        .format(Instant.now().plus(30, ChronoUnit.MINUTES).atZone(ZoneId.of("Asia/Taipei")));

    private static final String CANCEL_DEALER_ORDER_REQUEST_BODY = "{\n" +
        "  \"orderNo\": \"" + CANCEL_ORDER_NO + "\",\n" +
        "    \"orderStatus\": " + ContractStatus.CANCEL.getCode() + ",\n" +
        "    \"cancelDate\": \"" + cancelDate + "\",\n" +
        "    \"cancelRemark\": \"買到車子\",\n" +
        "    \"subscriptionInfo\": {\n" +
        "        \"totalAmt\": 26820\n" +
        "    }\n" +
        "}";

    private static final String CANCEL_DEALER_ORDER_REQUEST_BODY_NO_CANCEL_REMARK = "{\n" +
        "  \"orderNo\": \"" + ORDER_NO + "\",\n" +
        "  \"plateNo\": \"" + PLATE_NO + "\",\n" +
        "  \"orderStatus\": " + ContractStatus.CANCEL.getCode() + ",\n" +
        "  \"cancelDate\": \"" + cancelDate + "\",\n" +
        "  \"cancelRemark\": null,\n" +
        "  \"subscriptionInfo\": {\n" +
        "    \"totalAmt\": 26820\n" +
        "  }\n" +
        "}";

    @Test
    void createDealerOrder() throws Exception {

        MvcResult createDealerOrderResult = mockMvc.perform(post("/internal/subscribe/dealerOrder")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(buildDealerOrderCreateRequest(ORDER_NO, BuIdEnum.subscribe))))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.customerInfo.userName").isString())
            .andExpect(jsonPath("$.data.subscriptionInfo.securityDeposit").isNumber())
            .andExpect(jsonPath("$.data.dealerName").value(HeaderDefine.SystemKind.SEALAND))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.CREATE.getCode()))
            .andExpect(jsonPath("$.data.isAudit").value(true))
            .andExpect(jsonPath("$.data.isPaySecurityDeposit").value(true))
            .andExpect(jsonPath("$.data.dealerUserId").isNumber())
            .andExpect(jsonPath("$.data.dealerUserId", Matchers.not(0)))
            .andReturn();

        DealerOrderQueryResponse response = objectMapper.treeToValue(
            objectMapper.readTree(createDealerOrderResult.getResponse().getContentAsString()).get("data"),
            DealerOrderQueryResponse.class
        );

        Cars cars = carsService.findByPlateNo(response.getPlateNo());
        verifyCarStatusAndBookingOrderNo(cars, CarDefine.CarStatus.Subscribed, ORDER_NO);
    }

    private void verifyCarStatusAndBookingOrderNo(Cars cars, CarDefine.CarStatus carStatus, String expectedBookingOrderNo) {

        assertEquals(carStatus.getCode(), cars.getCarStatus());
        assertEquals(expectedBookingOrderNo, cars.getBookingOrderNo());
    }

    private DealerOrderCreateRequest buildDealerOrderCreateRequest(String orderNo, BuIdEnum buIdEnum) {
        DealerOrderCreateRequest request = new DealerOrderCreateRequest();
        request.setOrderNo(orderNo);

        request.setPlateNo(findAvailableCar(buIdEnum));

        request.setIsNewOrder(true);
        request.setSecurityDepositDate(Instant.now());
        request.setParentOrderNo(orderNo);
        request.setStage("1-1");
        request.setIsRegister(true);
        DealerCustomerInfoForCreate customerInfo = new DealerCustomerInfoForCreate();
        customerInfo.setUserName("Hans");
        customerInfo.setIdNo(ID_NO);
        customerInfo.setNationalCode("886");
        customerInfo.setMainCell("0912345678");
        customerInfo.setBirthDay("2000-01-01");
        customerInfo.setEmail("<EMAIL>");
        customerInfo.setCity(3);
        customerInfo.setArea(23);
        customerInfo.setAddress("經銷商訂單測試地址");
        request.setCustomerInfo(customerInfo);
        DealerSubscriptionInfo subscriptionInfo = new DealerSubscriptionInfo();
        subscriptionInfo.setSecurityDeposit(10000);
        subscriptionInfo.setMonthlyFee(8800);
        subscriptionInfo.setActualMileageRate(2.8);
        subscriptionInfo.setOriginalMileageRate(3.0);
        subscriptionInfo.setExpectDepartStation(EXPECT_DEPART_STATION);
        subscriptionInfo.setExpectReturnStation(EXPECT_RETURN_STATION);
        subscriptionInfo.setExpectDepartDate(Instant.now().plus(1, ChronoUnit.DAYS));
        subscriptionInfo.setExpectReturnDate(Instant.now().plus(90, ChronoUnit.DAYS));
        subscriptionInfo.setSubscribeMonth(3);
        subscriptionInfo.setTotalAmt(26820);
        subscriptionInfo.setMonthlyFeeDiscount(0);
        subscriptionInfo.setMileageRateDiscount(0.5);
        subscriptionInfo.setPrepaidMonths(1);
        subscriptionInfo.setPrepaidMileage(3000);
        subscriptionInfo.setPrepaidMileageDiscount(0);
        subscriptionInfo.setFlexibleMileage(0);
        subscriptionInfo.setActualMileageUsed(0);
        subscriptionInfo.setOffsetMileage1(0);
        subscriptionInfo.setOffsetMileage2(0);
        subscriptionInfo.setPrepaidMileageFee(8400);
        request.setSubscriptionInfo(subscriptionInfo);
        return request;
    }

    /**
     * 批次處理尋找符合條件的車牌號碼
     */
    private String findAvailableCar(BuIdEnum buIdEnum) {

        List<Cars> idleCars = carsService.getIdleCar();
        String foundPlateNo = null;
        int batchSize = 100;

        // Process cars in batches
        for (int i = 0; i < idleCars.size(); i += batchSize) {
            // Prepare a batch of plate numbers
            List<String> plateNos = idleCars.stream()
                .skip(i)
                .limit(batchSize)
                .map(Cars::getPlateNo)
                .collect(Collectors.toList());

            // Get car information in a batch
            Map<String, CarBaseInfoSearchResponse> carResponses = crsService.getCars(plateNos);

            // Process the current batch
            Optional<Cars> matchedCar = idleCars.subList(i, Math.min(i + batchSize, idleCars.size())).stream()
                .filter(car -> {
                    CarBaseInfoSearchResponse carBaseInfoSearchResponse = carResponses.get(car.getPlateNo());
                    return carBaseInfoSearchResponse != null
                        && buIdEnum.getCode().equals(carBaseInfoSearchResponse.getBuId())
                        && Arrays.asList(CarDefine.Launched.open, CarDefine.Launched.close).contains(car.getLaunched());
                })
                .findFirst();

            // If a match is found, use it and exit the loop
            if (matchedCar.isPresent()) {
                foundPlateNo = matchedCar.get().getPlateNo();
                break;
            }
        }

        assertNotNull(foundPlateNo, "No available secondHand car found");
        return foundPlateNo;
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    void testOperatingTwoDealerOrdersWithSameUser() throws Exception {

        String firstOrderNo = ORDER_NO;
        String secondOrderNo = "M202404188889";

        DealerOrderCreateRequest dealerOrderCreateRequest = buildDealerOrderCreateRequest(firstOrderNo, BuIdEnum.subscribe);

        // Create first dealer order
        MvcResult createFirstDealerOrderResult = mockMvc.perform(post("/internal/subscribe/dealerOrder")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(dealerOrderCreateRequest)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.customerInfo.userName").isString())
            .andExpect(jsonPath("$.data.subscriptionInfo.securityDeposit").isNumber())
            .andExpect(jsonPath("$.data.dealerName").value(HeaderDefine.SystemKind.SEALAND))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.CREATE.getCode()))
            .andExpect(jsonPath("$.data.isAudit").value(true))
            .andExpect(jsonPath("$.data.isPaySecurityDeposit").value(true))
            .andExpect(jsonPath("$.data.dealerUserId").isNumber())
            .andExpect(jsonPath("$.data.dealerUserId", Matchers.not(0)))
            .andReturn();

        DealerOrderQueryResponse firstDealerOrderQueryResponse = objectMapper.treeToValue(
            objectMapper.readTree(createFirstDealerOrderResult.getResponse().getContentAsString()).get("data"),
            DealerOrderQueryResponse.class
        );

        Cars cars = carsService.findByPlateNo(firstDealerOrderQueryResponse.getPlateNo());
        verifyCarStatusAndBookingOrderNo(cars, CarDefine.CarStatus.Subscribed, firstOrderNo);

        Thread.sleep(1000);

        // first dealer order 建立長租契約
        LrentalContractRequest firstLrentalContractRequest = new LrentalContractRequest();
        firstLrentalContractRequest.setOrderNo(firstDealerOrderQueryResponse.getOrderNo());
        firstLrentalContractRequest.setReplaceCodes(Collections.singletonList("5"));
        firstLrentalContractRequest.setMemo(String.format("格上官網／新單／中古車\\n%s：%s - %s（租期%d個月）\\n", firstDealerOrderQueryResponse.getOrderNo(),
            DateUtils.toDateString(Date.from(firstDealerOrderQueryResponse.getSubscriptionInfo().getExpectDepartDate()), "yyyy/MM/dd"),
            DateUtils.toDateString(Date.from(firstDealerOrderQueryResponse.getSubscriptionInfo().getExpectReturnDate()), "yyyy/MM/dd"),
            firstDealerOrderQueryResponse.getSubscriptionInfo().getSubscribeMonth()));
        dealerOrderService.createLrentalContract(firstLrentalContractRequest, HEADER_MEMBER_ID);

        Thread.sleep(1000);

        // first dealer order depart
        DealerOrderDepartRequest firstDepartRequest = new DealerOrderDepartRequest();
        firstDepartRequest.setOrderNo(firstOrderNo);
        firstDepartRequest.setPlateNo(firstDealerOrderQueryResponse.getPlateNo());
        DealerSubscriptionInfoForDepart firstSubscriptionInfoForDepart = new DealerSubscriptionInfoForDepart();
        firstSubscriptionInfoForDepart.setDepartStation(EXPECT_DEPART_STATION);
        firstSubscriptionInfoForDepart.setDepartDate(Instant.now().plus(1, ChronoUnit.DAYS));
        firstSubscriptionInfoForDepart.setPaidAmt(10000);
        firstSubscriptionInfoForDepart.setBeginAmt(34800);
        firstDepartRequest.setSubscriptionInfo(firstSubscriptionInfoForDepart);

        DealerOrder firstDepartedDealerOrder = dealerOrderService.departDealerOrder(firstDepartRequest, HEADER_MEMBER_ID, null);

        assertEquals(ContractStatus.GOING.getCode(), firstDepartedDealerOrder.getOrderStatus());
        assertEquals(CarDefine.CarStatus.BizOut.getCode(), carsService.getCarInfo(firstDealerOrderQueryResponse.getPlateNo()).getCarStatus());

        Thread.sleep(1000);

        // Create second dealer order
        dealerOrderCreateRequest.setOrderNo(secondOrderNo);
        MvcResult createSecondDealerOrderResult = mockMvc.perform(post("/internal/subscribe/dealerOrder")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(dealerOrderCreateRequest)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andReturn();

        DealerOrderQueryResponse secondDealerOrderQueryResponse = objectMapper.treeToValue(
            objectMapper.readTree(createSecondDealerOrderResult.getResponse().getContentAsString()).get("data"),
            DealerOrderQueryResponse.class
        );

        CarResponse carInfo = carsService.getCarInfo(secondDealerOrderQueryResponse.getPlateNo());
        // 因為已有一筆出車訂單，故驗證車輛狀態為 BizOut
        assertEquals(CarDefine.CarStatus.BizOut.getCode(), carInfo.getCarStatus());

        List<ProcessingOrder> processOrders = carInfo.getProcessOrders();
        assertEquals(2, processOrders.size());
        assertThat(processOrders.stream().map(ProcessingOrder::getOrderNo).collect(Collectors.toList())).containsExactlyInAnyOrder(firstOrderNo, secondOrderNo);

        Thread.sleep(1000);

        // second dealer order 建立長租契約
        LrentalContractRequest secondLrentalContractRequest = new LrentalContractRequest();
        secondLrentalContractRequest.setOrderNo(secondOrderNo);
        secondLrentalContractRequest.setReplaceCodes(Collections.singletonList("5"));
        secondLrentalContractRequest.setMemo(String.format("格上官網／新單／中古車\\n%s：%s - %s（租期%d個月）\\n", secondOrderNo,
            DateUtils.toDateString(Date.from(secondDealerOrderQueryResponse.getSubscriptionInfo().getExpectDepartDate()), "yyyy/MM/dd"),
            DateUtils.toDateString(Date.from(secondDealerOrderQueryResponse.getSubscriptionInfo().getExpectReturnDate()), "yyyy/MM/dd"),
            secondDealerOrderQueryResponse.getSubscriptionInfo().getSubscribeMonth()));
        dealerOrderService.createLrentalContract(secondLrentalContractRequest, HEADER_MEMBER_ID);

        Thread.sleep(1000);

        // second dealer order depart
        DealerOrderDepartRequest secondDepartRequest = new DealerOrderDepartRequest();
        secondDepartRequest.setOrderNo(secondOrderNo);
        secondDepartRequest.setPlateNo(secondDealerOrderQueryResponse.getPlateNo());
        DealerSubscriptionInfoForDepart secondSubscriptionInfoForDepart = new DealerSubscriptionInfoForDepart();
        secondSubscriptionInfoForDepart.setDepartStation(EXPECT_DEPART_STATION);
        secondSubscriptionInfoForDepart.setDepartDate(Instant.now().plus(1, ChronoUnit.DAYS));
        secondSubscriptionInfoForDepart.setPaidAmt(10000);
        secondSubscriptionInfoForDepart.setBeginAmt(34800);
        secondDepartRequest.setSubscriptionInfo(secondSubscriptionInfoForDepart);

        DealerOrder secondDepartedDealerOrder = dealerOrderService.departDealerOrder(secondDepartRequest, HEADER_MEMBER_ID, null);

        assertEquals(ContractStatus.GOING.getCode(), secondDepartedDealerOrder.getOrderStatus());
        assertEquals(CarDefine.CarStatus.BizOut.getCode(), carsService.getCarInfo(secondDealerOrderQueryResponse.getPlateNo()).getCarStatus());

        Thread.sleep(1000);

        // close first dealer order
        EtagCloseRequest etagCloseRequestForFirstOrder = new EtagCloseRequest();
        etagCloseRequestForFirstOrder.setReturnDate(Instant.now());
        dealerOrderService.etagReturn(firstOrderNo, HEADER_MEMBER_ID, etagCloseRequestForFirstOrder);

        DealerOrderCloseRequest dealerOrderCloseRequest = new DealerOrderCloseRequest();
        dealerOrderCloseRequest.setOrderNo(firstOrderNo);
        dealerOrderCloseRequest.setPlateNo(firstDealerOrderQueryResponse.getPlateNo());
        DealerSubscriptionInfoForClose subscriptionInfoForClose = new DealerSubscriptionInfoForClose();
        subscriptionInfoForClose.setCloseAmt(500);
        subscriptionInfoForClose.setReturnStation(EXPECT_RETURN_STATION);
        subscriptionInfoForClose.setReturnDate(Instant.now());
        subscriptionInfoForClose.setIsReturned(true);
        dealerOrderCloseRequest.setSubscriptionInfo(subscriptionInfoForClose);

        mockMvc.perform(patch("/internal/subscribe/dealerOrder/close")
            .contentType(MediaType.APPLICATION_JSON)
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, HEADER_MEMBER_ID)
            .content(objectMapper.writeValueAsString(dealerOrderCloseRequest)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.COMPLETE.getCode()))
            .andExpect(jsonPath("$.data.returnStation").value(EXPECT_RETURN_STATION))
            .andExpect(jsonPath("$.data.returnDate").value(notNullValue()))
            .andExpect(jsonPath("$.data.closeAmt").isNumber());

        CarResponse carInfoAfterFirstOrderClsoed = carsService.getCarInfo(secondDealerOrderQueryResponse.getPlateNo());
        // 因為第二筆訂單還是出車中，所以車輛狀態應為 BizOut
        assertEquals(CarDefine.CarStatus.BizOut.getCode(), carInfoAfterFirstOrderClsoed.getCarStatus());
        assertEquals(1, carInfoAfterFirstOrderClsoed.getProcessOrders().size());

        // close second dealer order
        EtagCloseRequest etagCloseRequestForSecondOrder = new EtagCloseRequest();
        etagCloseRequestForSecondOrder.setReturnDate(Instant.now());
        dealerOrderService.etagReturn(secondOrderNo, HEADER_MEMBER_ID, etagCloseRequestForSecondOrder);

        DealerOrderCloseRequest secondDealerOrderCloseRequest = new DealerOrderCloseRequest();
        secondDealerOrderCloseRequest.setOrderNo(secondOrderNo);
        secondDealerOrderCloseRequest.setPlateNo(secondDealerOrderQueryResponse.getPlateNo());
        DealerSubscriptionInfoForClose secondSubscriptionInfoForClose = new DealerSubscriptionInfoForClose();
        secondSubscriptionInfoForClose.setCloseAmt(500);
        secondSubscriptionInfoForClose.setReturnStation(EXPECT_RETURN_STATION);
        secondSubscriptionInfoForClose.setReturnDate(Instant.now());
        secondSubscriptionInfoForClose.setIsReturned(true);
        secondDealerOrderCloseRequest.setSubscriptionInfo(secondSubscriptionInfoForClose);

        mockMvc.perform(patch("/internal/subscribe/dealerOrder/close")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, HEADER_MEMBER_ID)
                .content(objectMapper.writeValueAsString(secondDealerOrderCloseRequest)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.COMPLETE.getCode()))
            .andExpect(jsonPath("$.data.returnStation").value(EXPECT_RETURN_STATION))
            .andExpect(jsonPath("$.data.returnDate").value(notNullValue()))
            .andExpect(jsonPath("$.data.closeAmt").isNumber());

        // 兩筆訂單皆已還車，車輛狀態應為 Free
        CarResponse carInfoAfterSecondOrderClosed = carsService.getCarInfo(secondDealerOrderQueryResponse.getPlateNo());
        assertEquals(CarDefine.CarStatus.Free.getCode(), carInfoAfterSecondOrderClosed.getCarStatus());
        assertTrue(carInfoAfterSecondOrderClosed.getProcessOrders().isEmpty());
    }

    @Test
    void createDealerOrder_VirtualCar() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = post("/internal/subscribe/dealerOrder")
            .contentType(MediaType.APPLICATION_JSON)
            .content(updateRequestField(DEALER_ORDER_REQUEST_BODY, "plateNo", PLATE_NO_VIRTUAL));

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.userName").isString())
            .andExpect(jsonPath("$.data.securityDeposit").isNumber())
            .andExpect(jsonPath("$.data.dealerName").value(HeaderDefine.SystemKind.SEALAND))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.CREATE.getCode()))
            .andExpect(jsonPath("$.data.isAudit").value(true))
            .andExpect(jsonPath("$.data.isPaySecurityDeposit").value(true));

        Cars cars = carsService.findByPlateNo(PLATE_NO_VIRTUAL);
        verifyCarStatusAndBookingOrderNo(cars, CarDefine.CarStatus.Free, ORDER_NO);
    }

    @Test
    void createDealerOrder_PlateNoExistsInCrsOnly() throws Exception {
        // 使用一個只存在於 CRS 的車牌號碼
        String crsOnlyPlateNo = "GGG-8000";

        // 確認車牌不存在於訂閱車籍
        assert carsService.findByPlateNo(crsOnlyPlateNo) == null;
        // 確認車牌存在於 CRS
        assert crsService.getCar(crsOnlyPlateNo) != null;

        MockHttpServletRequestBuilder requestBuilder = post("/internal/subscribe/dealerOrder")
            .contentType(MediaType.APPLICATION_JSON)
            .content(updateRequestField(DEALER_ORDER_REQUEST_BODY, "plateNo", crsOnlyPlateNo));

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.plateNo").value(crsOnlyPlateNo))  // 預期使用 CRS 的車牌號碼
            .andExpect(jsonPath("$.data.customerInfo.userName").isString())
            .andExpect(jsonPath("$.data.subscriptionInfo.securityDeposit").isNumber())
            .andExpect(jsonPath("$.data.dealerName").value(HeaderDefine.SystemKind.SEALAND))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.CREATE.getCode()))
            .andExpect(jsonPath("$.data.isAudit").value(true))
            .andExpect(jsonPath("$.data.isPaySecurityDeposit").value(true));

        // 驗證車輛狀態
        Cars car = carsService.findByPlateNo(crsOnlyPlateNo);
        // 確認車輛已被新增到訂閱車籍
        assert car != null;
        verifyCarStatusAndBookingOrderNo(car, CarDefine.CarStatus.Subscribed, ORDER_NO);
    }

    @Test
    void createDealerOrder_NonExistentPlateNo_ShouldUseSeaLandVirtualPlateNo() throws Exception {
        // 使用一個不存在的車牌號碼
        String nonExistentPlateNo = "XXX-9999";

        // 檢查訂閱車籍是否存在該車牌號碼
        assert carsService.findByPlateNo(nonExistentPlateNo) == null;
        // 檢查 CRS 中是否存在該車牌號碼
        assert crsService.getCar(nonExistentPlateNo) == null;

        MockHttpServletRequestBuilder requestBuilder = post("/internal/subscribe/dealerOrder")
            .contentType(MediaType.APPLICATION_JSON)
            .content(updateRequestField(DEALER_ORDER_REQUEST_BODY, "plateNo", nonExistentPlateNo));

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.plateNo").value(SEALAND_VIRTUAL_PLATE_NO)) // 預期使用經銷商虛擬車號建單
            .andExpect(jsonPath("$.data.customerInfo.userName").isString())
            .andExpect(jsonPath("$.data.subscriptionInfo.securityDeposit").isNumber())
            .andExpect(jsonPath("$.data.dealerName").value(HeaderDefine.SystemKind.SEALAND))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.CREATE.getCode()))
            .andExpect(jsonPath("$.data.isAudit").value(true))
            .andExpect(jsonPath("$.data.isPaySecurityDeposit").value(true));

        // 驗證虛擬車輛狀態
        Cars virtualCar = carsService.findByPlateNo(SEALAND_VIRTUAL_PLATE_NO);
        verifyCarStatusAndBookingOrderNo(virtualCar, CarDefine.CarStatus.Free, ORDER_NO);
    }

    @Test
    void createDealerOrder_Failure_OrderNoIsEmpty() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = post("/internal/subscribe/dealerOrder")
            .contentType(MediaType.APPLICATION_JSON)
            .content(updateRequestField(DEALER_ORDER_REQUEST_BODY, "orderNo", ""));

        mockMvc.perform(requestBuilder)
            .andExpect(status().isBadRequest())
            .andExpect(jsonPath("$.statusCode").value(API_USE_INCORRECT.getCode()))
            .andExpect(jsonPath("$.message").value("訂單編號不可為空"));
    }

    void createDealerOrderAndCreateLrentalContractForTest() throws JsonProcessingException {

        createDealerOrderAndCreateLrentalContractForTest(null, null);
    }

    void createDealerOrderAndCreateLrentalContractForTest(String changeField, Object changeValue) throws JsonProcessingException {

        DealerOrderCreateRequest dealerOrderRequest = objectMapper.readValue(changeField == null ? DEALER_ORDER_REQUEST_BODY : updateRequestField(DEALER_ORDER_REQUEST_BODY, changeField, changeValue), DealerOrderCreateRequest.class);
        DealerOrderQueryResponse dealerOrder = dealerOrderService.createDealerOrder(dealerOrderRequest, false);

        LrentalContractRequest firstLrentalContractRequest = new LrentalContractRequest();
        firstLrentalContractRequest.setOrderNo(dealerOrder.getOrderNo());
        firstLrentalContractRequest.setReplaceCodes(Collections.singletonList("5"));
        firstLrentalContractRequest.setMemo(String.format("格上官網／新單／中古車\\n%s：%s - %s（租期%d個月）\\n", dealerOrder.getOrderNo(),
            DateUtils.toDateString(Date.from(dealerOrder.getSubscriptionInfo().getExpectDepartDate()), "yyyy/MM/dd"),
            DateUtils.toDateString(Date.from(dealerOrder.getSubscriptionInfo().getExpectReturnDate()), "yyyy/MM/dd"),
            dealerOrder.getSubscriptionInfo().getSubscribeMonth()));
        dealerOrderService.createLrentalContract(firstLrentalContractRequest, HEADER_MEMBER_ID);
    }

    @Test
    void updateDealerOrder() throws Exception {

        createDealerOrderAndCreateLrentalContractForTest();

        MockHttpServletRequestBuilder requestBuilder = patch("/internal/subscribe/dealerOrder")
            .contentType(MediaType.APPLICATION_JSON)
            .content(DEALER_ORDER_UPDATE_REQUEST_BODY);

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.userName").value("Hans123"))
            .andExpect(jsonPath("$.data.securityDeposit").value(12000))
            .andExpect(jsonPath("$.data.dealerUserId").isNumber())
            .andExpect(jsonPath("$.data.dealerUserId").value(Matchers.not(0)));
    }

    @Test
    void updateDealerOrder_RemoveCustomerInfoNoChangeFields() throws Exception {

        createDealerOrderAndCreateLrentalContractForTest();

        MockHttpServletRequestBuilder requestBuilder = patch("/internal/subscribe/dealerOrder")
            .contentType(MediaType.APPLICATION_JSON)
            .content(DEALER_ORDER_UPDATE_REQUEST_BODY_REMOVE_CUSTOMER_INFO_NO_CHANGE_FIELDS);

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.userName").value("Hans123"))
            .andExpect(jsonPath("$.data.securityDeposit").value(12000))
            .andExpect(jsonPath("$.data.dealerUserId").isNumber())
            .andExpect(jsonPath("$.data.dealerUserId").value(Matchers.not(0)));
    }

    @Test
    void updateDealerOrder_Failure_ChangeValidIdNo() throws Exception {

        createDealerOrderAndCreateLrentalContractForTest();

        MockHttpServletRequestBuilder requestBuilder = patch("/internal/subscribe/dealerOrder")
            .contentType(MediaType.APPLICATION_JSON)
            .content(DEALER_ORDER_UPDATE_REQUEST_BODY_CHANGE_VALID_ID_NO);

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value(DEALER_USER_ID_NO_CAN_NOT_CHANGE.getCode()))
            .andExpect(jsonPath("$.message").value(DEALER_USER_ID_NO_CAN_NOT_CHANGE.getMsg()));
    }

    @Test
    void updateDealerOrder_ChangeValidDealerUserId() throws Exception {

        createDealerOrderAndCreateLrentalContractForTest();

        MockHttpServletRequestBuilder requestBuilder = patch("/internal/subscribe/dealerOrder")
            .contentType(MediaType.APPLICATION_JSON)
            .content(DEALER_ORDER_UPDATE_REQUEST_BODY_DEALER_USER_ID_0);

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.userName").value("ValidIdTest"))
            .andExpect(jsonPath("$.data.dealerUserId").isNumber())
            .andExpect(jsonPath("$.data.dealerUserId").value(Matchers.not(0)));
    }

    @Test
    @Rollback(false)
    void updateDealerOrder_ChangePlateNo() throws Exception {

        createDealerOrderAndCreateLrentalContractForTest();

        MockHttpServletRequestBuilder requestBuilder = patch("/internal/subscribe/dealerOrder")
            .contentType(MediaType.APPLICATION_JSON)
            .content(updateRequestField(DEALER_ORDER_UPDATE_REQUEST_BODY, "plateNo", PLATE_NO_NEW));

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.plateNo").value(PLATE_NO_NEW))
            .andExpect(jsonPath("$.data.customerInfo.userName").value("Hans123"))
            .andExpect(jsonPath("$.data.subscriptionInfo.securityDeposit").value(12000));

        verifyChangePlateNoResult(ContractStatus.CREATE);
    }

    private void verifyChangePlateNoResult(ContractStatus orderStatusToChange) {
        Cars oriCar = carsService.findByPlateNo(PLATE_NO);
        CarBaseInfoSearchResponse oriCarBase = crsService.getCar(PLATE_NO);
        // 驗證原車 crsCarNo 是否更新
        assertEquals(oriCarBase.getCarNo(), oriCar.getCrsCarNo());
        verifyCarStatusAndBookingOrderNo(oriCar, CarDefine.CarStatus.Free, null);

        Cars newCar = carsService.findByPlateNo(PLATE_NO_NEW);
        CarBaseInfoSearchResponse newCarBase = crsService.getCar(PLATE_NO_NEW);
        // 驗證新車 crsCarNo 是否更新
        assertEquals(newCarBase.getCarNo(), newCar.getCrsCarNo());
        switch (orderStatusToChange) {
            case CREATE:
                verifyCarStatusAndBookingOrderNo(newCar, CarDefine.CarStatus.Subscribed, ORDER_NO);
                break;
            case GOING:
                verifyCarStatusAndBookingOrderNo(newCar, CarDefine.CarStatus.BizOut, ORDER_NO);
                break;
            default:
                throw new IllegalArgumentException("Unsupported order status: " + orderStatusToChange);
        }
    }

    String updateRequestField(String requestBody, String fieldName, Object fieldValue) throws JsonProcessingException {

        JsonNode jsonNode = objectMapper.readTree(requestBody);

        if (null == fieldValue) {
            ((ObjectNode) jsonNode).put(fieldName, (JsonNode) null);
        } else {
            ((ObjectNode) jsonNode).put(fieldName, String.valueOf(fieldValue));
        }

        return objectMapper.writeValueAsString(jsonNode);
    }

    @Test
    public void updateDealerOrder_Failure_OrderNotFound() throws Exception {

        createDealerOrderAndCreateLrentalContractForTest();

        String nonExistentOrderNo = "nonExistentOrderNo";

        MockHttpServletRequestBuilder requestBuilder = patch("/internal/subscribe/dealerOrder")
            .contentType(MediaType.APPLICATION_JSON)
            .content(updateRequestField(DEALER_ORDER_UPDATE_REQUEST_BODY, "orderNo", nonExistentOrderNo));

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value(DEALER_ORDER_NOT_FOUND.getCode()))
            .andExpect(jsonPath("$.message").value(containsString("找不到經銷商訂單")))
            .andExpect(jsonPath("$.data").value(nullValue()));
    }

    @Test
    public void updateDealerOrder_Failure_PlateNoIsNull() throws Exception {

        createDealerOrderAndCreateLrentalContractForTest();

        MockHttpServletRequestBuilder requestBuilder = patch("/internal/subscribe/dealerOrder")
            .contentType(MediaType.APPLICATION_JSON)
            .content(updateRequestField(DEALER_ORDER_UPDATE_REQUEST_BODY, "plateNo", null));

        mockMvc.perform(requestBuilder)
            .andExpect(status().isBadRequest())
            .andExpect(jsonPath("$.statusCode").value(API_USE_INCORRECT.getCode()))
            .andExpect(jsonPath("$.message").value(containsString("車牌號碼不可為空")))
            .andExpect(jsonPath("$.data").value(nullValue()));
    }

    @Test
    public void departDealerOrder() throws Exception {

        createDealerOrderAndCreateLrentalContractForTest();

        MockHttpServletRequestBuilder requestBuilder = patch("/internal/subscribe/dealerOrder/depart")
            .contentType(MediaType.APPLICATION_JSON)
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, HEADER_MEMBER_ID)
            .content(DEPART_DEALER_ORDER_REQUEST_BODY);

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.GOING.getCode()))
            .andExpect(jsonPath("$.data.departStation").value("35"))
            .andExpect(jsonPath("$.data.departDate").value(notNullValue()))
            .andExpect(jsonPath("$.data.beginAmt").isNumber());

        Cars cars = carsService.findByPlateNo(PLATE_NO);
        verifyCarStatusAndBookingOrderNo(cars, CarDefine.CarStatus.BizOut, ORDER_NO);

        List<EtagInfoResponse> responses = eTagService.getETagIntoByOrderNo(ORDER_NO);
        responses.forEach(response -> {
            assertEquals(ORDER_NO, response.getOrderNo());
            assertEquals(ETagFlow.DEPART_SUCCESS.getCode(), response.getETagFlow());
            assertTrue(response.isUploaded());
        });
    }

    @Test
    public void departDealerOrder_VirtualCar() throws Exception {

        createDealerOrderAndCreateLrentalContractForTest("plateNo", PLATE_NO_VIRTUAL);

        MockHttpServletRequestBuilder requestBuilder = patch("/internal/subscribe/dealerOrder/depart")
            .contentType(MediaType.APPLICATION_JSON)
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, HEADER_MEMBER_ID)
            .content(updateRequestField(DEPART_DEALER_ORDER_REQUEST_BODY, "plateNo", PLATE_NO_VIRTUAL));

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.GOING.getCode()))
            .andExpect(jsonPath("$.data.departStation").value("35"))
            .andExpect(jsonPath("$.data.departDate").value(notNullValue()))
            .andExpect(jsonPath("$.data.beginAmt").isNumber());

        Cars cars = carsService.findByPlateNo(PLATE_NO_VIRTUAL);
        verifyCarStatusAndBookingOrderNo(cars, CarDefine.CarStatus.Free, ORDER_NO);
    }

    @Test
    public void departDealerOrder_ChangePlateNo() throws Exception {

        createDealerOrderAndCreateLrentalContractForTest();

        MockHttpServletRequestBuilder requestBuilder = patch("/internal/subscribe/dealerOrder/depart")
            .contentType(MediaType.APPLICATION_JSON)
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, HEADER_MEMBER_ID)
            .content(updateRequestField(DEPART_DEALER_ORDER_REQUEST_BODY, "plateNo", PLATE_NO_NEW));

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.plateNo").value(PLATE_NO_NEW))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.GOING.getCode()))
            .andExpect(jsonPath("$.data.departStation").value("35"))
            .andExpect(jsonPath("$.data.departDate").value(notNullValue()))
            .andExpect(jsonPath("$.data.beginAmt").isNumber());

        verifyChangePlateNoResult(ContractStatus.GOING);
    }

    @Test
    public void departDealerOrder_Failure_ChangeSeaLandVirtualPlateNo() throws Exception {

        createDealerOrderAndCreateLrentalContractForTest();

        // 出車時使用虛擬車號
        MockHttpServletRequestBuilder requestBuilder = patch("/internal/subscribe/dealerOrder/depart")
            .contentType(MediaType.APPLICATION_JSON)
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, HEADER_MEMBER_ID)
            .header(CarPlusConstant.AUTH_HEADER_SYSTEM_KIND, CsatOrderSource.SEALAND.name())
            .content(updateRequestField(DEPART_DEALER_ORDER_REQUEST_BODY, "plateNo", SEALAND_VIRTUAL_PLATE_NO));

        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value(DEALER_VIRTUAL_PLATE_NO_NOT_ALLOW_DEPART.getCode()));;
    }

    @Test
    public void departDealerOrder_Failure_OrderStatusIsNotGoing() throws Exception {

        createDealerOrderAndCreateLrentalContractForTest();

        MockHttpServletRequestBuilder requestBuilder = patch("/internal/subscribe/dealerOrder/depart")
            .contentType(MediaType.APPLICATION_JSON)
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, HEADER_MEMBER_ID)
            .content(updateRequestField(DEPART_DEALER_ORDER_REQUEST_BODY, "orderStatus", ContractStatus.CREATE.getCode()));

        mockMvc.perform(requestBuilder)
            .andExpect(status().isBadRequest())
            .andExpect(jsonPath("$.statusCode").value(API_USE_INCORRECT.getCode()))
            .andExpect(jsonPath("$.message").value(containsString("訂單狀態參數不為出車狀態，不可執行出車API")))
            .andExpect(jsonPath("$.data").value(nullValue()));
    }

    @Test
    public void departDealerOrder_OrderStatusIsNull() throws Exception {

        createDealerOrderAndCreateLrentalContractForTest();

        MockHttpServletRequestBuilder requestBuilder = patch("/internal/subscribe/dealerOrder/depart")
            .contentType(MediaType.APPLICATION_JSON)
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, HEADER_MEMBER_ID)
            .content(updateRequestField(DEPART_DEALER_ORDER_REQUEST_BODY, "orderStatus", null));

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.GOING.getCode()))
            .andExpect(jsonPath("$.data.departStation").value("35"))
            .andExpect(jsonPath("$.data.departDate").value(notNullValue()))
            .andExpect(jsonPath("$.data.beginAmt").isNumber());
    }

    @Test
    public void departDealerOrder_Failure_DepartStationIsNull() throws Exception {

        createDealerOrderAndCreateLrentalContractForTest();

        MockHttpServletRequestBuilder requestBuilder = patch("/internal/subscribe/dealerOrder/depart")
            .contentType(MediaType.APPLICATION_JSON)
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, HEADER_MEMBER_ID)
            .content(DEPART_DEALER_ORDER_REQUEST_BODY_NO_DEPART_STATION);

        mockMvc.perform(requestBuilder)
            .andExpect(status().isBadRequest())
            .andExpect(jsonPath("$.statusCode").value(API_USE_INCORRECT.getCode()))
            .andExpect(jsonPath("$.message").value(containsString("實際出車站點不可為空")))
            .andExpect(jsonPath("$.data").value(nullValue()));
    }

    private void departDealerOrderForTest() throws JsonProcessingException {

        DealerOrderDepartRequest dealerOrderDepartRequest = objectMapper.readValue(DEPART_DEALER_ORDER_REQUEST_BODY, DealerOrderDepartRequest.class);
        dealerOrderService.departDealerOrder(dealerOrderDepartRequest, HEADER_MEMBER_ID, null);
    }

    private void closeEtagForTest() throws JsonProcessingException {

        EtagCloseRequest etagCloseRequest = objectMapper.readValue(CLOSE_ETAG_REQUEST_BODY, EtagCloseRequest.class);
        dealerOrderService.etagReturn(ORDER_NO, HEADER_MEMBER_ID, etagCloseRequest);
    }

    @Test
    public void closeDealerOrder() throws Exception {

        createDealerOrderAndCreateLrentalContractForTest();

        departDealerOrderForTest();

        closeEtagForTest();

        MockHttpServletRequestBuilder requestBuilder = patch("/internal/subscribe/dealerOrder/close")
            .contentType(MediaType.APPLICATION_JSON)
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, HEADER_MEMBER_ID)
            .content(CLOSE_DEALER_ORDER_REQUEST_BODY);

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.COMPLETE.getCode()))
            .andExpect(jsonPath("$.data.returnStation").value("35"))
            .andExpect(jsonPath("$.data.returnDate").value(notNullValue()))
            .andExpect(jsonPath("$.data.closeAmt").isNumber())
            .andExpect(jsonPath("$.data.totalAmt").value(26820 + 34800 - 34800))
            .andExpect(jsonPath("$.data.paidAmt").value(0 + 34800 - 34800));

        List<EtagInfoResponse> responses = eTagService.getETagIntoByOrderNo(ORDER_NO);
        responses.forEach(response -> {
            assertEquals(ORDER_NO, response.getOrderNo());
            assertEquals(ETagFlow.RETURN_SUCCESS.getCode(), response.getETagFlow());
            assertEquals(ETagPayFlow.DONE.getCode(), response.getETagPayFlow());
            Assertions.assertNotNull(response.getDealerOrderPriceInfoId());
            assertTrue(response.isUploaded());
            Assertions.assertNotNull(response.getReturnDate());
        });
    }

    @Test
    public void closeDealerOrder_OrderStatusIsNull() throws Exception {

        createDealerOrderAndCreateLrentalContractForTest();

        departDealerOrderForTest();

        closeEtagForTest();

        MockHttpServletRequestBuilder requestBuilder = patch("/internal/subscribe/dealerOrder/close")
            .contentType(MediaType.APPLICATION_JSON)
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, HEADER_MEMBER_ID)
            .content(updateRequestField(CLOSE_DEALER_ORDER_REQUEST_BODY, "orderStatus", null));

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.COMPLETE.getCode()))
            .andExpect(jsonPath("$.data.returnStation").value("35"))
            .andExpect(jsonPath("$.data.returnDate").value(notNullValue()))
            .andExpect(jsonPath("$.data.closeAmt").isNumber());
    }

    @Test
    public void closeDealerOrder_Failure_ReturnStationIsNull() throws Exception {

        createDealerOrderAndCreateLrentalContractForTest();

        departDealerOrderForTest();

        closeEtagForTest();

        MockHttpServletRequestBuilder requestBuilder = patch("/internal/subscribe/dealerOrder/close")
            .contentType(MediaType.APPLICATION_JSON)
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, HEADER_MEMBER_ID)
            .content(CLOSE_DEALER_ORDER_REQUEST_BODY_NO_RETURN_STATION);

        mockMvc.perform(requestBuilder)
            .andExpect(status().isBadRequest())
            .andExpect(jsonPath("$.statusCode").value(API_USE_INCORRECT.getCode()))
            .andExpect(jsonPath("$.message").value(containsString("實際還車站點不可為空")))
            .andExpect(jsonPath("$.data").value(nullValue()));
    }

    @Test
    public void closeDealerOrder_IsReturnedTrue() throws Exception {

        createDealerOrderAndCreateLrentalContractForTest();

        departDealerOrderForTest();

        closeEtagForTest();

        MockHttpServletRequestBuilder requestBuilder = patch("/internal/subscribe/dealerOrder/close")
            .contentType(MediaType.APPLICATION_JSON)
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, HEADER_MEMBER_ID)
            .content(CLOSE_DEALER_ORDER_REQUEST_BODY_IS_RETURNED_TRUE);

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.COMPLETE.getCode()))
            .andExpect(jsonPath("$.data.returnStation").value("35"))
            .andExpect(jsonPath("$.data.returnDate").value(notNullValue()))
            .andExpect(jsonPath("$.data.closeAmt").isNumber());

        eTagService.getETagIntoByOrderNo(ORDER_NO).forEach(response -> {
            assertEquals(ORDER_NO, response.getOrderNo());
            assertEquals(ETagFlow.RETURN_SUCCESS.getCode(), response.getETagFlow());
            assertEquals(ETagPayFlow.DONE.getCode(), response.getETagPayFlow());
            Assertions.assertNotNull(response.getDealerOrderPriceInfoId());
            assertTrue(response.isUploaded());
            Assertions.assertNotNull(response.getReturnDate());
            assertTrue(response.getDealerAndETagAccount());
            assertTrue(response.isLockETagAmt());
        });
        dealerOrderPriceInfoService.getDealerOrderPriceInfoByOrderNo(ORDER_NO).forEach(response -> {
            System.out.println(response);
            assertEquals(ORDER_NO, response.getOrderNo());
            Assertions.assertNotNull(response.getTradeId());
            assertEquals(TransactionItem.ETAG.getCode(), response.getTransactionItem());
        });
        DealerOrderResponse dealerOrderResponse = dealerOrderService.queryDealerOrderDetail(ORDER_NO);
        dealerOrderResponse.getDealerOrderPriceInfos().forEach(response -> {
            assertEquals(ORDER_NO, response.getOrderNo());
        });
        assertEquals(ID_NO, dealerOrderResponse.getCustomerInfo().getIdNo());
    }

    @Test
    public void cancelDealerOrder() throws Exception {

        createDealerOrderAndCreateLrentalContractForTest();

        MockHttpServletRequestBuilder requestBuilder = patch("/internal/subscribe/dealerOrder/cancel")
            .contentType(MediaType.APPLICATION_JSON)
            .content(CANCEL_DEALER_ORDER_REQUEST_BODY);

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.CANCEL.getCode()))
            .andExpect(jsonPath("$.data.isCancel").value(true))
            .andExpect(jsonPath("$.data.cancelRemark").value("買到車子"));
    }

    @Test
    public void cancelDealerOrder_OrderStatusIsNull() throws Exception {

        createDealerOrderAndCreateLrentalContractForTest();

        MockHttpServletRequestBuilder requestBuilder = patch("/internal/subscribe/dealerOrder/cancel")
            .contentType(MediaType.APPLICATION_JSON)
            .content(updateRequestField(CANCEL_DEALER_ORDER_REQUEST_BODY, "orderStatus", null));

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.CANCEL.getCode()))
            .andExpect(jsonPath("$.data.isCancel").value(true))
            .andExpect(jsonPath("$.data.cancelDate").value(notNullValue()))
            .andExpect(jsonPath("$.data.cancelRemark").value("買到車子"));
    }

    @Test
    public void cancelDealerOrder_Failure_OrderStatusIsGoing() throws Exception {

        createDealerOrderAndCreateLrentalContractForTest();

        departDealerOrderForTest();

        MockHttpServletRequestBuilder requestBuilder = patch("/internal/subscribe/dealerOrder/cancel")
            .contentType(MediaType.APPLICATION_JSON)
            .content(CANCEL_DEALER_ORDER_REQUEST_BODY);

        mockMvc.perform(requestBuilder)
            .andExpect(status().isBadRequest())
            .andExpect(jsonPath("$.statusCode").value(API_USE_INCORRECT.getCode()))
            .andExpect(jsonPath("$.message").value(containsString("訂單不為建立中狀態，不可取消")))
            .andExpect(jsonPath("$.data").value(nullValue()));
    }

    @Test
    public void cancelDealerOrder_Success_CancelRemarkIsNull() throws Exception {

        createDealerOrderAndCreateLrentalContractForTest();

        MockHttpServletRequestBuilder requestBuilder = patch("/internal/subscribe/dealerOrder/cancel")
            .contentType(MediaType.APPLICATION_JSON)
            .content(CANCEL_DEALER_ORDER_REQUEST_BODY_NO_CANCEL_REMARK);

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.CANCEL.getCode()))
            .andExpect(jsonPath("$.data.isCancel").value(true))
            .andExpect(jsonPath("$.data.cancelDate").value(notNullValue()));
    }

    @Test
    public void queryDealerOrder() throws Exception {

        createDealerOrderAndCreateLrentalContractForTest();

        MockHttpServletRequestBuilder requestBuilder = get("/internal/subscribe/dealerOrder/query")
            .param("skip", "0")
            .param("limit", "10")
            .param("stationCode", EXPECT_DEPART_STATION)
            .param("orderNo", ORDER_NO);

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.page.list[0].orderNo").value(ORDER_NO))
            .andExpect(jsonPath("$.data.page.list[0].customerInfo.idNo").value(ID_NO))
            .andExpect(jsonPath("$.data.page.list[0].expectDepartStation").doesNotHaveJsonPath())
            .andExpect(jsonPath("$.data.page.list[0].subscriptionInfo.expectReturnStation").exists());
    }

    @Test
    public void queryDealerOrder_Failure_WrongIdNo() throws Exception {

        createDealerOrderAndCreateLrentalContractForTest();

        MockHttpServletRequestBuilder requestBuilder = get("/internal/subscribe/dealerOrder/query")
            .param("skip", "0")
            .param("limit", "10")
            .param("stationCode", EXPECT_DEPART_STATION)
            .param("orderNo", ORDER_NO)
            .param("idNo", "A123456789");

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.page.total").value(0))
            .andExpect(jsonPath("$.data.page.list").value(empty()));
    }

    @Test
    void queryDealerOrderDetail() throws Exception {

        createDealerOrderAndCreateLrentalContractForTest();

        mockMvc.perform(get("/internal/subscribe/dealerOrder/{orderNo}", ORDER_NO))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.orderNo").value(ORDER_NO))
            .andExpect(jsonPath("$.data.customerInfo.idNo").value(ID_NO))
            .andExpect(jsonPath("$.data.expectDepartStation").doesNotHaveJsonPath())
            .andExpect(jsonPath("$.data.subscriptionInfo.expectReturnStation").exists());
    }

    @Test
    public void updateUserIdAndLrentalContractNo_Success_UpdateBoth() throws Exception {
        // 準備測試資料
        DealerOrderUpdateUserAndLrentalContractNoRequest request = new DealerOrderUpdateUserAndLrentalContractNoRequest();
        request.setOrderNo("24083132552");
        request.setDealerUserId(83L);
        request.setLrentalContractNo("110999995-W");

        MockHttpServletRequestBuilder requestBuilder = patch("/internal/subscribe/dealerOrder/userAndLrentalContractNo")
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(request));

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.dealerUserId").value(83))
            .andExpect(jsonPath("$.data.lrentalContractNo").value("110999995-W"));
    }

    @Test
    public void updateUserIdAndLrentalContractNo_Success_ClearLrentalContractNo() throws Exception {
        // 準備測試資料
        DealerOrderUpdateUserAndLrentalContractNoRequest request = new DealerOrderUpdateUserAndLrentalContractNoRequest();
        request.setOrderNo("24083132552");
        request.setDealerUserId(83L);
        request.setLrentalContractNo(null);

        MockHttpServletRequestBuilder requestBuilder = patch("/internal/subscribe/dealerOrder/userAndLrentalContractNo")
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(request));

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.dealerUserId").value(83))
            .andExpect(jsonPath("$.data.lrentalContractNo").value(nullValue()));
    }

    @Test
    public void updateUserIdAndLrentalContractNo_Failure_DealerUserNotFound() throws Exception {
        // 準備測試資料
        DealerOrderUpdateUserAndLrentalContractNoRequest request = new DealerOrderUpdateUserAndLrentalContractNoRequest();
        request.setOrderNo("24083132552");
        request.setDealerUserId(999L);
        request.setLrentalContractNo("110999995-W");

        MockHttpServletRequestBuilder requestBuilder = patch("/internal/subscribe/dealerOrder/userAndLrentalContractNo")
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(request));

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value(DEALER_USER_NOT_FOUND.getCode()))
            .andExpect(jsonPath("$.message").value(containsString("找不到經銷商客戶")))
            .andExpect(jsonPath("$.data").value(nullValue()));
    }

    @Test
    public void updateUserIdAndLrentalContractNo_Failure_InvalidLrentalContract() throws Exception {
        // 準備測試資料
        DealerOrderUpdateUserAndLrentalContractNoRequest request = new DealerOrderUpdateUserAndLrentalContractNoRequest();
        request.setOrderNo("24083132552");
        request.setDealerUserId(83L);
        request.setLrentalContractNo("131299016-A");

        MockHttpServletRequestBuilder requestBuilder = patch("/internal/subscribe/dealerOrder/userAndLrentalContractNo")
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(request));

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value(LRENTAL_CONTRACT_NOT_FOUND.getCode()))
            .andExpect(jsonPath("$.message").value(containsString("找不到指定長租契約編號")))
            .andExpect(jsonPath("$.data").value(nullValue()));
    }

    @Test
    public void createDealerOrderWithSecondHandCarThenUpdateWithAnotherSecondHandCar() throws Exception {
        // 準備測試資料
        String orderNo = "************";
        DealerOrderCreateRequest dealerOrderCreateRequest = buildDealerOrderCreateRequest(orderNo, BuIdEnum.secondHand);
        mockMvc.perform(post("/internal/subscribe/dealerOrder")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(dealerOrderCreateRequest)))
            .andExpect(jsonPath("$.statusCode").value("0"));

        // 自動撥車申請
        long currentTime = System.currentTimeMillis();
        mockMvc.perform(post("/internal/subscribe/v1/car/changeBu")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, HEADER_MEMBER_ID)
                .header(CarPlusConstant.HEADER_COMPANY_CODE, "carplus")
                .content(String.format("{\"orderNo\":\"%s\",\"plateNo\":\"%s\",\"licenseExpDate\":%d,\"changeType\":\"AUTO_BATCH_CHANGE\"}",
                    orderNo, dealerOrderCreateRequest.getPlateNo(), currentTime)))
            .andExpect(jsonPath("$.statusCode").value("0"));

        // 建立長租契約
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
        LocalDateTime startDate = LocalDateTime.ofInstant(Instant.ofEpochMilli(currentTime), ZoneId.systemDefault());
        LocalDateTime endDate = startDate.plusMonths(dealerOrderCreateRequest.getSubscriptionInfo().getSubscribeMonth());

        String memo = String.format("格上官網／新單／中古車\n%s：%s - %s（租期%d個月）\n",
            orderNo,
            startDate.format(formatter),
            endDate.format(formatter),
            dealerOrderCreateRequest.getSubscriptionInfo().getSubscribeMonth());
        memo = memo.replace("\n", "\\\\n");

        mockMvc.perform(post("/internal/subscribe/dealerOrder/addLrentalContract")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, HEADER_MEMBER_ID)
                .content(String.format("{\"orderNo\":\"%s\",\"licenseExpDate\":%d,\"replaceCodes\":[],\"memo\":\"%s\"}",
                    orderNo, currentTime, memo)))
            .andExpect(jsonPath("$.statusCode").value("0"));
        
        DealerOrderUpdateRequest dealerOrderUpdateRequest = new DealerOrderUpdateRequest();
        dealerOrderUpdateRequest.setOrderNo(orderNo);
        dealerOrderUpdateRequest.setPlateNo(findAvailableCar(BuIdEnum.secondHand));
        dealerOrderUpdateRequest.setSubscriptionInfo(new DealerSubscriptionInfoForUpdate());
        mockMvc.perform(patch("/internal/subscribe/dealerOrder")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(dealerOrderUpdateRequest)))
            .andExpect(jsonPath("$.statusCode").value("0"));
    }
}