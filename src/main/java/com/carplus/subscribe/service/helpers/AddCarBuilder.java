package com.carplus.subscribe.service.helpers;

import carplus.common.response.exception.BadRequestException;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.controller.cars.CarsInternalController;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.model.authority.MemberInfo;
import com.carplus.subscribe.model.crs.*;
import com.carplus.subscribe.model.request.CarsAddRequest;
import com.carplus.subscribe.model.request.CarsAddSingleRequest;
import com.carplus.subscribe.model.request.CarsCRSAddRequest;
import com.carplus.subscribe.model.request.carregistration.CarRegistrationCSV;
import com.carplus.subscribe.model.request.carregistration.CarRegistrationCSVImportUrl;
import com.carplus.subscribe.server.AuthorityServer;
import com.carplus.subscribe.service.CarModelService;
import com.carplus.subscribe.service.CarRegistrationService;
import com.carplus.subscribe.service.CarsService;
import com.carplus.subscribe.service.CrsService;
import lombok.Data;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

import static com.carplus.subscribe.utils.CarsUtil.*;

@Component
public class AddCarBuilder {

    private final CarsService carsService;
    private final CrsService crsService;
    private final AuthorityServer authorityServer;
    private final CarModelService carModelService;
    private static CarRegistrationService staticCarRegistrationService = null;

    public AddCarBuilder(CarsService carsService, CrsService crsService, AuthorityServer authorityServer, CarModelService carModelService, CarRegistrationService carRegistrationService) {
        this.carsService = carsService;
        this.crsService = crsService;
        this.authorityServer = authorityServer;
        this.carModelService = carModelService;
        staticCarRegistrationService = carRegistrationService;
    }

    /**
     * 資料前置處理作業 <br/>
     * {@link AddCarBuilder#buildAddCars(Object, CarBuildContext)} <br/>
     */
    public AddCarBuilderDataPreSetup processingPreData(@NonNull String plateNo, @Nullable String memberId, @Nullable String carModelCode, boolean crsRequired) {
        // 取得車輛基本資訊
        CarBaseInfoToAddResponse carBaseInfoToAdd = null;
        List<Cars> cars = new ArrayList<>();
        Map<Integer, Cars> latestExistingCarsInfo = new HashMap<>();
        boolean isCrsCarNoExist = false;
        if (crsRequired) {
            carBaseInfoToAdd = crsService.getCarBaseInfoToAdd(plateNo);
            // 取得未刪除且未棄用的車輛列表
            List<Cars> crsCars = carsService.findByCrsCarNo(carBaseInfoToAdd.getCrsCarNo());
            cars = getCarsWithoutDeletedAndDeprecated(crsCars);
            // 取得最後一筆的車牌資訊
            latestExistingCarsInfo = getLatestCarInfo(cars);
            isCrsCarNoExist = isCarsNotEmpty(cars);
            setIsProjectCar(cars);
        }

        // 取得會員資訊
        MemberInfo memberInfo = null;
        if (memberId != null) {
            memberInfo = authorityServer.getMemberInfos(memberId).get(0);
        }

        List<String> orderNos = new ArrayList<>();

        String checkedCarModelCode = null;
        if (carModelCode != null) {
            checkedCarModelCode = carModelService.checkCarModelCodeAndGet(carModelCode);
        }

        return AddCarBuilderDataPreSetup.builder()
            .carBaseInfoToAdd(carBaseInfoToAdd)
            .memberInfo(memberInfo)
            .cars(cars)
            .latestExistingCarsInfo(latestExistingCarsInfo)
            .isCrsCarNoExist(isCrsCarNoExist)
            .orderNos(orderNos)
            .CheckedCarModelCode(checkedCarModelCode)
            .isNotVirtualAndIsCarPlusCar(crsRequired)
            .build();
    }

    private void setIsProjectCar(List<Cars> cars) {
        Integer crsNo = cars.stream().filter(c -> c.getCrsCarNo() != null).map(Cars::getCrsCarNo).findAny().orElse(null);
        if (crsNo != null) {
            boolean isProjectCar = Optional.ofNullable(crsService.searchProjectCar(crsNo)).map(PurchaseProjectCarSearchResponse::getIsProjectCar).orElse(false);
            cars.forEach(c -> c.setProjectCar(isProjectCar));
        }
    }

    // Strategy ------------------------------------------------------------------------------------------------------
    private interface CarBuildStrategy<T> {
        void setRequiredProperties(Cars car, T source, CarBuildContext context);

        void setOptionalProperties(Cars car, T source);

        /**
         * 驗證 (可無實作內容，驗證邏輯移至 AddCarBuilder.buildAddCars 之外)
         */
        void validateProperties(T source);
    }

    @Data
    private static class CarBuildContext {
        final CarBaseInfoToAddResponse carBaseInfoToAdd;
        final boolean isCrsCarNoExist;
        final Map<Integer, Cars> carsInfo;
        final String subscribeStationCode;
        final String checkedCarModelCode;
        final boolean isNotVirtualAndIsCarPlusCar;
    }

    /**
     * {@link CarsInternalController#addCars(CarsAddRequest, String)} <br/>
     */
    private static class CarsAddRequestStrategy implements CarBuildStrategy<CarsAddRequest> {
        /**
         * {@link #populateCarBaseInfo(Cars, CarBaseInfoToAddResponse)}
         * {@link #setAndValidateCarCurrMileage(Cars, CarsAddRequest, CarBuildContext)}
         * {@link #setCarNoByIsVirtual(Cars, CarsAddRequest)}
         */
        @Override
        public void setRequiredProperties(Cars car, CarsAddRequest request, CarBuildContext context) {

            if (context.isNotVirtualAndIsCarPlusCar) {
                populateCarBaseInfo(car, context.carBaseInfoToAdd);
                setAndValidateCarCurrMileage(car, request, context);
            } else {
                setAndValidateNonVirtualCarRequiredFields(car, request);
            }

            // Base
            car.setPlateNo(request.getPlateNo());
            car.validateCarMfgYear();
            setCarNoByIsVirtual(car, request);

            // No matter the value of isCRSCarNoExist, it is always set like this.
            car.setLaunched(CarDefine.Launched.close);
            setVatNo(car, request.getVatNo());
            car.setSealandLaunched(false);

            car.setCarStatus(CarDefine.CarStatus.Free.getCode());
            car.setRegisStationCode(context.subscribeStationCode);
            car.setOwnerStationCode(context.subscribeStationCode);

            // 暫時拔除 isCrsCarNoExist 邏輯 (CYB-14827)
            // cars Table PlateNo 已存在相同 CRS CarNo
//            if (context.isCrsCarNoExist) {
//                // 4. 新車牌比照原車牌`subscribeLevel`,`carModelCode`,`carState`,`launched=close`,`isSealandLaunched=false`
//                Cars existingCar = context.carsInfo.get(context.carBaseInfoToAdd.getCrsCarNo());
//                car.setSubscribeLevel(request.getSubscribeLevel());
//                car.setCarModelCode(existingCar.getCarModelCode());
//                car.setCarState(existingCar.getCarState());
//                car.setCarStatus(existingCar.getCarStatus());
//                car.setBuChangeMasterId(existingCar.getBuChangeMasterId());
//            } else {
            car.setSubscribeLevel(request.getSubscribeLevel());
            car.setCarModelCode(context.checkedCarModelCode);
            car.setCarState(request.getCarState());
//            }
        }

        private static void setCarNoByIsVirtual(Cars car, CarsAddRequest request) {
            if (!car.isVirtualCar()) {
                car.setCarNo(encodePlateNo(request.getPlateNo()));
            } else {
                car.setCarNo(Optional.ofNullable(request.getCarNo())
                    .orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.VIRTUAL_CAR_NEED_CAR_NUMBER)));
            }
        }

        private static void setAndValidateCarCurrMileage(Cars car, CarsAddRequest request, CarBuildContext context) {
            if (request.getCurrentMileage() != null) {
                if (request.getCurrentMileage() < 0) {
                    throw new BadRequestException("[里程數不可小於 0]");
                }
                car.setCurrentMileage(request.getCurrentMileage());
            } else {
                car.setCurrentMileage(context.carBaseInfoToAdd.getCurrentMileage());
            }
        }

        @Override
        public void setOptionalProperties(Cars car, CarsAddRequest request) {
            Optional.ofNullable(request.getFuelType()).ifPresent(car::setFuelType);
            Optional.ofNullable(request.getLocationStationCode()).ifPresent(car::setLocationStationCode);
            Optional.ofNullable(request.getEquipIds()).ifPresent(car::setEquipIds);
            Optional.ofNullable(request.getTagIds()).ifPresent(car::setTagIds);
            Optional.ofNullable(request.getStdPrice()).ifPresent(car::setStdPrice);
            Optional.ofNullable(request.getColorDesc()).ifPresent(car::setColorDesc);
            Optional.ofNullable(request.getCnDesc()).ifPresent(car::setCnDesc);
            Optional.ofNullable(request.getPrepWorkdays()).ifPresent(car::setPrepWorkdays);
        }

        @Override
        public void validateProperties(CarsAddRequest source) {

        }
    }

    /**
     * {@link CarsInternalController#addCars(CarsCRSAddRequest)} <br/>
     */
    private static class CarBaseInfoSearchResponseStrategy implements CarBuildStrategy<CarBaseInfoSearchResponse> {
        @Override
        public void setRequiredProperties(Cars car, CarBaseInfoSearchResponse crsBaseInfoResponse, CarBuildContext context) {
            // Base
            car.setPlateNo(crsBaseInfoResponse.getPlateNo());
            car.setCarNo(encodePlateNo(crsBaseInfoResponse.getPlateNo()));
            car.setCarStatus(CarDefine.CarStatus.Free.getCode());
            car.setLaunched(CarDefine.Launched.tbc);
            setVatNo(car, CarPlusConstant.CARPLUS_COMPANY_VAT_NO);

            // cars Table PlateNo 已存在相同 CRS CarNo
            if (context.isCrsCarNoExist) {
                // 4. 新車牌比照原車牌`subscribeLevel`,`carModelCode`,`carState`,`launched=close`,`isSealandLaunched=false`
                Cars existingCar = context.carsInfo.get(crsBaseInfoResponse.getCarNo());
                if (existingCar.getSubscribeLevel() == null || existingCar.getCarState() == null || existingCar.getCarModelCode() == null) {
                    throw new BadRequestException("請先設定舊車牌 " + existingCar.getPlateNo() + " 訂閱方案/車型/類別資料");
                } else {
                    car.setSubscribeLevel(existingCar.getSubscribeLevel());
                    car.setCarModelCode(existingCar.getCarModelCode());
                    car.setCarState(existingCar.getCarState());
                    car.setCarStatus(existingCar.getCarStatus());
                    car.setLaunched(CarDefine.Launched.close);
                    car.setSealandLaunched(false);
                    car.setBuChangeMasterId(existingCar.getBuChangeMasterId());
                }
            }
        }

        @Override
        public void setOptionalProperties(Cars car, CarBaseInfoSearchResponse crsBaseInfoResponse) {
            Optional.of(crsBaseInfoResponse)
                .map(CarBaseInfoSearchResponse::getCarBase)
                .map(CarBase::getPublishDate)
                .map(StringUtils::trim)
                .map(mfgYear -> mfgYear.substring(0, 4))
                .ifPresent(car::setMfgYear);

            Optional.of(crsBaseInfoResponse)
                .map(CarBaseInfoSearchResponse::getCarSpecInfoResponse)
                .map(CarSpecInfoResponse::getCylinder)
                .map(BigDecimal::valueOf)
                .ifPresent(car::setDisplacement);

            Optional.of(crsBaseInfoResponse)
                .map(CarBaseInfoSearchResponse::getCarSpecInfoResponse)
                .map(CarSpecInfoResponse::getSeats)
                .ifPresent(car::setSeat);

            Optional.of(crsBaseInfoResponse)
                .map(CarBaseInfoSearchResponse::getCarBase)
                .map(CarBase::getKm)
                .ifPresent(car::setCurrentMileage);

            Optional.of(crsBaseInfoResponse)
                .map(CarBaseInfoSearchResponse::getCarNo)
                .ifPresent(car::setCrsCarNo);

            Optional.of(crsBaseInfoResponse)
                .map(CarBaseInfoSearchResponse::getCarBase)
                .map(CarBase::getStdPrice)
                .ifPresent(car::setStdPrice);

            Optional.of(crsBaseInfoResponse)
                .map(CarBaseInfoSearchResponse::getIsProjectCar)
                .ifPresent(car::setProjectCar);
        }

        @Override
        public void validateProperties(CarBaseInfoSearchResponse crsBaseInfoResponse) {
        }
    }

    /**
     * {@link CarsInternalController#addSingleCars(CarsAddSingleRequest, String)} <br/>
     * {@link #setCommonFields(Cars, CarBuildContext, String, Integer, Boolean)}
     * {@link #populateCarBaseInfo(Cars, CarBaseInfoToAddResponse)}
     */
    private static class CarsAddSingleRequestStrategy implements CarBuildStrategy<CarsAddSingleRequest> {

        @Override
        public void setRequiredProperties(Cars car, CarsAddSingleRequest request, CarBuildContext context) {
            // Base
            setCommonFields(car, context, request.getPlateNo(), request.getCurrentMileage(), request.getIsSealandLaunched());
            if (isCarPlusCar(request.getVatNo())) {
                populateCarBaseInfo(car, context.carBaseInfoToAdd);
            } else {
                car.setEnergyType(request.getEnergyType());
                car.setDisplacement(BigDecimal.valueOf(request.getDisplacement()));
                car.setSeat(request.getSeat());
                car.setMfgYear(request.getMfgYear());
                car.setMfgMonth(request.getMfgMonth());
                car.setCrsCarNo(-1);
            }

            // 暫時拔除 isCrsCarNoExist 邏輯 (CYB-14827)
            // cars Table PlateNo 已存在相同 CRS CarNo
//            if (context.isCrsCarNoExist) {
//                // 4. 新車牌比照原車牌`subscribeLevel`,`carModelCode`,`carState`,`launched=close`,`isSealandLaunched=false`
//                Cars existingCar = context.carsInfo.get(context.carBaseInfoToAdd.getCrsCarNo());
//                car.setSubscribeLevel(existingCar.getSubscribeLevel());
//                car.setCarModelCode(existingCar.getCarModelCode());
//                car.setCarState(existingCar.getCarState());
//                car.setCarStatus(existingCar.getCarStatus());
//                car.setLaunched(CarDefine.Launched.close);
//                car.setSealandLaunched(false);
//                car.setBuChangeMasterId(existingCar.getBuChangeMasterId());
//            } else {
            setVatNo(car, request.getVatNo());
            car.setSubscribeLevel(request.getSubscribeLevel());
            car.setCarModelCode(context.checkedCarModelCode);
            car.setCarState(request.getCarState());
            car.setLaunched(request.getLaunched());
            car.setSealandLaunched(request.getIsSealandLaunched());
//            }

            if (context.isCrsCarNoExist) {
                Cars existingCar = context.carsInfo.get(context.carBaseInfoToAdd.getCrsCarNo());
                car.setCarStatus(existingCar.getCarStatus());
                car.setBuChangeMasterId(existingCar.getBuChangeMasterId());
                car.setBookingOrderNo(existingCar.getBookingOrderNo());
            }
        }

        @Override
        public void setOptionalProperties(Cars car, CarsAddSingleRequest request) {
            Optional.ofNullable(request.getCarType()).ifPresent(car::setCarType);
            Optional.ofNullable(request.getFuelType()).ifPresent(car::setFuelType);
            Optional.ofNullable(request.getGearType()).ifPresent(car::setGearType);
            Optional.ofNullable(request.getLocationStationCode()).ifPresent(car::setLocationStationCode);
            Optional.ofNullable(request.getEquipIds()).ifPresent(car::setEquipIds);
            Optional.ofNullable(request.getTagIds()).ifPresent(car::setTagIds);
            Optional.ofNullable(request.getColorDesc()).ifPresent(car::setColorDesc);
            Optional.ofNullable(request.getCnDesc()).ifPresent(car::setCnDesc);
            Optional.ofNullable(request.getPrepWorkdays()).ifPresent(car::setPrepWorkdays);
        }

        @Override
        public void validateProperties(CarsAddSingleRequest request) throws IllegalArgumentException {

        }
    }

    /**
     * {@link CarsInternalController#cars(CarRegistrationCSVImportUrl, String)} <br/>
     * {@link #setCommonFields(Cars, CarBuildContext, String, Integer, Boolean)}
     * {@link #populateCarBaseInfo(Cars, CarBaseInfoToAddResponse)}
     */
    private static class CarRegistrationCSVStrategy implements CarBuildStrategy<CarRegistrationCSV> {
        @Override
        public void setRequiredProperties(Cars car, CarRegistrationCSV carsCsv, CarBuildContext context) {
            // Base
            setCommonFields(car, context, carsCsv.getPlateNo(), carsCsv.getCurrentMileage(), carsCsv.getIsSealandLaunched());
            car.setIsDeleted(false);
            populateCarBaseInfo(car, context.carBaseInfoToAdd);

            // 暫時拔除 isCrsCarNoExist 邏輯 (CYB-14827)
            // 4. 新車牌比照原車牌`subscribeLevel`,`carModelCode`,`carState`,`launched=close`,`isSealandLaunched=false`
//            if (context.isCrsCarNoExist) {
//                Cars existingCar = context.carsInfo.get(context.carBaseInfoToAdd.getCrsCarNo());
//                car.setSubscribeLevel(existingCar.getSubscribeLevel());
//                car.setCarModelCode(existingCar.getCarModelCode());
//                car.setCarState(existingCar.getCarState());
//                car.setCarStatus(existingCar.getCarStatus());
//                car.setLaunched(CarDefine.Launched.close);
//                car.setSealandLaunched(false);
//                car.setBuChangeMasterId(existingCar.getBuChangeMasterId());
//            } else {
            setVatNo(car, carsCsv.getVatNo());
            car.setSubscribeLevel(carsCsv.getSubscribeLevel());
            car.setCarModelCode(context.checkedCarModelCode);
            car.setCarState(CarDefine.CarState.valueOf(carsCsv.getCarState()));
            car.setLaunched(carsCsv.getLaunched());
            car.setSealandLaunched(carsCsv.getIsSealandLaunched());
//            }
        }

        @Override
        public void setOptionalProperties(Cars car, CarRegistrationCSV carsCsv) {
            Optional.ofNullable(carsCsv.getCarType()).ifPresent(car::setCarType);
            Optional.ofNullable(carsCsv.getGearType()).ifPresent(car::setGearType);
            Optional.ofNullable(carsCsv.getLocationStationCode()).ifPresent(car::setLocationStationCode);
            Optional.ofNullable(carsCsv.getEquipIds()).ifPresent(car::setEquipIds);
            Optional.ofNullable(carsCsv.getTagIds()).ifPresent(car::setTagIds);
            Optional.ofNullable(carsCsv.getColorDesc()).ifPresent(car::setColorDesc);
            Optional.ofNullable(carsCsv.getCnDesc()).ifPresent(car::setCnDesc);
            Optional.ofNullable(carsCsv.getStdPrice()).ifPresent(car::setStdPrice);
            Optional.ofNullable(carsCsv.getPrepWorkdays()).ifPresent(car::setPrepWorkdays);
        }

        @Override
        public void validateProperties(CarRegistrationCSV csv) {
        }
    }

    // Factory --------------------------------------------------------------------------------------------------------

    /**
     * {@link CarsAddRequestStrategy} <br/>
     * {@link CarBaseInfoSearchResponseStrategy} <br/>
     * {@link CarsAddSingleRequestStrategy} <br/>
     * {@link CarRegistrationCSVStrategy} <br/>
     */
    @SuppressWarnings("unchecked")
    private static class CarBuildStrategyFactory {
        static <T> CarBuildStrategy<T> getStrategy(T source) {
            if (source instanceof CarsAddRequest) {
                return (CarBuildStrategy<T>) new CarsAddRequestStrategy();
            }
            if (source instanceof CarBaseInfoSearchResponse) {
                return (CarBuildStrategy<T>) new CarBaseInfoSearchResponseStrategy();
            }
            if (source instanceof CarsAddSingleRequest) {
                return (CarBuildStrategy<T>) new CarsAddSingleRequestStrategy();
            }
            if (source instanceof CarRegistrationCSV) {
                return (CarBuildStrategy<T>) new CarRegistrationCSVStrategy();
            }
            throw new IllegalArgumentException("Unsupported buildAddCars type: " + source.getClass().getName());
        }
    }

    private <T> Cars buildAddCars(T source, CarBuildContext context) {
        CarBuildStrategy<T> strategy = CarBuildStrategyFactory.getStrategy(source);
        Cars car = new Cars();
        strategy.setRequiredProperties(car, source, context);
        strategy.setOptionalProperties(car, source);
        return car;
    }

    /**
     * 新增車籍 <br/>
     * {@link CarsService#add(CarsAddRequest, String)}
     * {@link CarsAddRequestStrategy#setRequiredProperties(Cars, CarsAddRequest, CarBuildContext)}
     * {@link CarsAddRequestStrategy#setOptionalProperties(Cars, CarsAddRequest)}
     */
    public Cars processCarsAddRequest(CarsAddRequest request,
                                      CarBaseInfoToAddResponse carBaseInfoToAdd,
                                      boolean isCrsCarNoExist,
                                      Map<Integer, Cars> carsInfo,
                                      String subscribeStationCode,
                                      String checkedCarModelCode, boolean crsRequired) {
        CarBuildContext context = new CarBuildContext(
            carBaseInfoToAdd, isCrsCarNoExist, carsInfo, subscribeStationCode, checkedCarModelCode, crsRequired
        );
        return buildAddCars(request, context);
    }

    /**
     * 新增車籍 from CRS <br/>
     * {@link CarsService#addCarByCrs(CarBaseInfoSearchResponse)}
     */
    public Cars processCrsResponseBaseInfo(CarBaseInfoSearchResponse crsBaseInfoResponse,
                                           CarBaseInfoToAddResponse carBaseInfoToAdd,
                                           boolean isCrsCarNoExist,
                                           Map<Integer, Cars> carsInfo,
                                           String subscribeStationCode,
                                           String checkedCarModelCode) {
        CarBuildContext context = new CarBuildContext(
            carBaseInfoToAdd, isCrsCarNoExist, carsInfo, subscribeStationCode, checkedCarModelCode, false
        );
        return buildAddCars(crsBaseInfoResponse, context);
    }

    /**
     * 後台 - 單台新增 <br/>
     * {@link CarsService#addSingle(CarsAddSingleRequest, String)}
     */
    public Cars processCarsAddSingleRequest(CarsAddSingleRequest request,
                                            CarBaseInfoToAddResponse carBaseInfoToAdd,
                                            boolean isCrsCarNoExist,
                                            Map<Integer, Cars> carsInfo,
                                            String subscribeStationCode,
                                            String checkedCarModelCode) {
        CarBuildContext context = new CarBuildContext(
            carBaseInfoToAdd, isCrsCarNoExist, carsInfo, subscribeStationCode, checkedCarModelCode, false
        );
        return buildAddCars(request, context);
    }

    /**
     * 後台 - 批量新增 <br/>
     * {@link CarsService#importCSVCars(List, MemberInfo)}
     */
    public Cars processCarRegistrationCSV(CarRegistrationCSV carsCsv,
                                          CarBaseInfoToAddResponse carBaseInfoToAdd,
                                          boolean isCrsCarNoExist,
                                          Map<Integer, Cars> carsInfo,
                                          String subscribeStationCode,
                                          String checkedCarModelCode) {
        CarBuildContext context = new CarBuildContext(
            carBaseInfoToAdd, isCrsCarNoExist, carsInfo, subscribeStationCode, checkedCarModelCode, false
        );
        return buildAddCars(carsCsv, context);
    }


    // Helpers -------------------------------------------------------------------------------------------------------

    private static void populateCarBaseInfo(Cars car, CarBaseInfoToAddResponse carBaseInfoToAdd) {
        car.setEnergyType(carBaseInfoToAdd.getEnergyType());
        car.setDisplacement(carBaseInfoToAdd.getDisplacement());
        car.setSeat(carBaseInfoToAdd.getSeat());
        car.setMfgYear(carBaseInfoToAdd.getMfgYear());
        car.setMfgMonth(carBaseInfoToAdd.getMfgMonth());
        car.setCrsCarNo(carBaseInfoToAdd.getCrsCarNo());
        car.setProjectCar(Optional.ofNullable(carBaseInfoToAdd.getIsProjectCar()).orElse(false));
        car.setStdPrice(carBaseInfoToAdd.getStdPrice());
    }

    private static void setCommonFields(Cars car, CarBuildContext context, String plateNo, Integer currentMileage,
                                        Boolean isSealandLaunched) {
        car.setPlateNo(plateNo);
        car.setCurrentMileage(currentMileage);
        car.setCarStatus(CarDefine.CarStatus.Free.getCode());
        car.setRegisStationCode(context.subscribeStationCode);
        car.setOwnerStationCode(context.subscribeStationCode);
        car.setCarNo(encodePlateNo(plateNo));
        car.setSealandLaunched(isSealandLaunched);
    }

    private static void setVatNo(Cars cars, String vatNo) {
        boolean isVatNoExist = staticCarRegistrationService.isCarRegistrationExist(vatNo);
        if (isVatNoExist) {
            cars.setVatNo(vatNo);
        } else {
            throw new SubscribeException(SubscribeHttpExceptionCode.CAR_REGISTRATION_NOT_FOUND);
        }
    }
}
