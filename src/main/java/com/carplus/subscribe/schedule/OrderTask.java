package com.carplus.subscribe.schedule;

import carplus.common.utils.DateUtils;
import com.carplus.subscribe.db.mysql.dao.DealerOrderRepository;
import com.carplus.subscribe.db.mysql.dao.MainContractRepository;
import com.carplus.subscribe.db.mysql.dao.OrderRepository;
import com.carplus.subscribe.db.mysql.entity.Stations;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.db.mysql.entity.contract.MainContract;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrder;
import com.carplus.subscribe.enums.*;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.model.auth.AuthUser;
import com.carplus.subscribe.model.crs.CarBase;
import com.carplus.subscribe.model.crs.CarBaseInfoSearchResponse;
import com.carplus.subscribe.model.crs.PurchaseProjectCarSearchResponse;
import com.carplus.subscribe.model.lrental.ContractAddReq;
import com.carplus.subscribe.model.lrental.ContractSearchRep;
import com.carplus.subscribe.model.priceinfo.MileagePriceInfoWrapper;
import com.carplus.subscribe.model.priceinfo.PriceInfoWrapper;
import com.carplus.subscribe.model.response.dealer.DealerOrderQueryResponse;
import com.carplus.subscribe.server.AuthServer;
import com.carplus.subscribe.server.MattermostServer;
import com.carplus.subscribe.server.cars.LrentalServer;
import com.carplus.subscribe.service.*;
import com.carplus.subscribe.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;

import javax.persistence.criteria.Predicate;
import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoCategory.MileageFee;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoCategory.SecurityDeposit;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoType.Pay;

@Slf4j
@Component
public class OrderTask {
    @Autowired
    private PriceInfoService priceInfoService;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private DealerOrderRepository dealerOrderRepository;

    @Autowired
    private MainContractRepository mainContractRepository;

    @Autowired
    private ContractLogic contractLogic;

    @Autowired
    private ContractService contractService;

    @Autowired
    private NotifyService notifyService;

    @Autowired
    private NotifyToCService notifyToCService;

    @Autowired
    private StationService stationService;

    @Autowired
    private AuthServer authServer;

    @Autowired
    private OrderService orderService;

    @Autowired
    private DealerOrderService dealerOrderService;

    @Autowired
    private CrsService crsService;

    @Autowired
    private LrentalServer lrentalServer;

    @Autowired
    private CarsService carsService;

    @Autowired
    private MattermostServer mattermostServer;

    @Autowired
    private ConfigService configService;

    /**
     * 續約提醒
     */
    public void notifyRenewCallTask() {
        log.info("notifyRenewCallTask 續約提醒 開始");
        executeNotifyRenewCallTask();
        log.info("notifyRenewCallTask 續約提醒 開始");
    }

    private void executeNotifyRenewCallTask() {
        List<Orders> orderList = orderRepository.findAll((Specification<Orders>) (root, query, builder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(builder.equal(root.get(Orders.Fields.status), OrderStatus.DEPART.getStatus()));
            predicates.add(builder.isNull(root.get(Orders.Fields.nextStageOrderNo)));
            return builder.and(predicates.toArray(new Predicate[0]));
        });
        orderList = orderList.stream()
            .filter(od -> od.getRenewType() == null || od.getRenewType() == RenewType.PENDING
                || od.getRenewType() == RenewType.WILLING).collect(Collectors.toList());

        Instant now = Instant.now();
        for (Orders order : orderList) {
            MainContract mainContract = mainContractRepository.getMainContractByNo(order.getContract().getMainContractNo(), true);
            if (!contractLogic.isRenewable(mainContract)) {
                log.info("訂單：{} 不可續約", order.getOrderNo());
                continue;
            }
            // 當天到合約結束天數
            Duration duration = Duration.between(now, order.getExpectEndDate());
            long days = duration.toDays();
            if (days == 5 || days == 10) {
                AuthUser user = authServer.getUserWithRetry(mainContract.getAcctId());
                notifyService.notifyRenewCall(order, user);
                notifyToCService.notifyRenewOrder(order, user);
            }
        }
    }


    public void cancelOrderWithoutSecurityDeposit() {
        int count = 0;
        log.info("cancelOrderWithoutSecurityDeposit 取消保證金未付款訂單 開始");
        List<Orders> orderList = orderRepository.findAll((Specification<Orders>) (root, query, builder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(builder.equal(root.get(Orders.Fields.status), OrderStatus.CREDITED.getStatus()));
            return builder.and(predicates.toArray(new Predicate[0]));
        });
        for (Orders orders : orderList) {
            try {
                PriceInfoWrapper priceInfoWrapper = priceInfoService.getPriceInfoWrapper(orders.getOrderNo());
                List<OrderPriceInfo> securityDepositList = priceInfoWrapper.getByCategoryType(SecurityDeposit, Pay).getList();
                if (!securityDepositList.isEmpty()) {
                    OrderPriceInfo securityDeposit = securityDepositList.stream().min(Comparator.comparing(OrderPriceInfo::getLastPayDate)).get();
                    if (securityDeposit.getLastPayDate().isBefore(Instant.now())) {
                        orders.setCancelDate(Instant.now());
                        orders.setCancelMemo(SubscribeHttpExceptionCode.SECURITY_DEPOSIT_PAY_TIMEOUT.getMsg());
                        orderService.cancelOrder(orders);
                        contractService.cancelContract(orders.getContract());
                        contractService.cancelMainContract(orders.getContract().getMainContract());
                        List<OrderPriceInfo> orderPriceInfoList = priceInfoWrapper.getList();
                        orderPriceInfoList.forEach(orderPriceInfo -> priceInfoService.refundAll(orderPriceInfo.getId(), PriceInfoDefinition.PriceInfoCategory.CancelBooking));
                        count++;
                    }
                }
            } catch (Exception e) {
                log.error("訂單取消失敗 orderNo:{}", orders, e);
            }
        }
        log.info("cancelOrderWithoutSecurityDeposit 取消保證金未付款訂單 結束,共取消[{}]筆訂單", count);
    }


    public void notifyReturnNotClose() {
        log.info("已還車未結案通知 開始");
        List<Orders> orderList = orderRepository.findAll((Specification<Orders>) (root, query, builder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(builder.equal(root.get(Orders.Fields.status), OrderStatus.ARRIVE_NO_CLOSE.getStatus()));
            return builder.and(predicates.toArray(new Predicate[0]));
        });
        Map<String, Stations> stationsMap = stationService.findAll().stream().collect(Collectors.toMap(Stations::getStationCode, s -> s));

        Map<GeoDefine.GeoRegion, List<Orders>> geoOrdersMap = new LinkedHashMap<>();
        for (Orders orders : orderList) {

            MainContract mainContract = orders.getContract().getMainContract();
            GeoDefine.GeoRegion geoRegion = GeoDefine.GeoRegion.valueOf(stationsMap.get(mainContract.getReturnStationCode()).getGeoRegion());
            List<Orders> ordersList = geoOrdersMap.computeIfAbsent(geoRegion, k -> new ArrayList<>());
            ordersList.add(orders);
        }
        if (!geoOrdersMap.isEmpty()) {
            notifyService.notifyReturnNotCloseSummary(geoOrdersMap);
        }
    }

    public void notifyDepartNotReturn() {
        log.info("已出車未還車通知 開始");
        List<Orders> orderList = orderRepository.findAll((Specification<Orders>) (root, query, builder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(builder.equal(root.get(Orders.Fields.status), OrderStatus.DEPART.getStatus()));
            predicates.add(builder.lessThan(root.get(Orders.Fields.expectEndDate), builder.literal(Instant.now())));
            return builder.and(predicates.toArray(new Predicate[0]));
        });
        Map<String, Stations> stationsMap = stationService.findAll().stream().collect(Collectors.toMap(Stations::getStationCode, s -> s));

        Map<GeoDefine.GeoRegion, List<Orders>> geoOrdersMap = new LinkedHashMap<>();

        for (Orders orders : orderList) {
            MainContract mainContract = orders.getContract().getMainContract();
            GeoDefine.GeoRegion geoRegion = GeoDefine.GeoRegion.valueOf(stationsMap.get(mainContract.getReturnStationCode()).getGeoRegion());
            List<Orders> ordersList = geoOrdersMap.computeIfAbsent(geoRegion, k -> new ArrayList<>());
            ordersList.add(orders);
        }
        if (!geoOrdersMap.isEmpty()) {
            notifyService.notifyDepartNotReturnSummary(geoOrdersMap);
        }
    }

    public void notifyMonthlyFeeUnpaid() {
        log.info("訂單逾期未結算通知 開始");
        List<Orders> orderList = orderRepository.findAll((Specification<Orders>) (root, query, builder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(builder.equal(root.get(Orders.Fields.status), OrderStatus.DEPART.getStatus()));
            return builder.and(predicates.toArray(new Predicate[0]));
        });
        Map<String, Stations> stationsMap = stationService.findAll().stream().collect(Collectors.toMap(Stations::getStationCode, s -> s));
        Map<GeoDefine.GeoRegion, List<Orders>> geoOrdersMap = new LinkedHashMap<>();
        for (Orders orders : orderList) {
            MainContract mainContract = orders.getContract().getMainContract();
            GeoDefine.GeoRegion geoRegion = GeoDefine.GeoRegion.valueOf(stationsMap.get(mainContract.getReturnStationCode()).getGeoRegion());
            try {
                List<OrderPriceInfo> monthlyFeeUnpaidList = priceInfoService.getMonthlyFeeUnpaidList(orders.getOrderNo());
                if (!monthlyFeeUnpaidList.isEmpty()) {
                    List<Orders> ordersList = geoOrdersMap.computeIfAbsent(geoRegion, k -> new ArrayList<>());
                    ordersList.add(orders);
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        if (!geoOrdersMap.isEmpty()) {
            notifyService.notifyMonthlyFeeUnpaidSummary(geoOrdersMap);
        }
    }

    /**
     * 還車提醒
     */
    public void executeNotifyReturnTask() {
        List<Orders> orderList = orderRepository.findAll((Specification<Orders>) (root, query, builder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(builder.equal(root.get(Orders.Fields.status), OrderStatus.DEPART.getStatus()));
            predicates.add(builder.lessThan(root.get(Orders.Fields.month), 3));
            return builder.and(predicates.toArray(new Predicate[0]));
        });
        // 短租期且還車前10天
        orderList = orderList.stream()
            .filter(od -> DateUtil.calculateDiffDate(Instant.now(), od.getExpectEndDate(), ChronoUnit.DAYS) == 10).collect(Collectors.toList());

        for (Orders order : orderList) {
            MainContract mainContract = order.getContract().getMainContract();

            AuthUser user = authServer.getUserWithRetry(mainContract.getAcctId());
            notifyToCService.notifyReturnOrder(order, user);

        }
    }

    /**
     * 收訂尚未建約提醒
     */
    public void notifyOrderWithoutContract() {
        List<String> orderNoForRemindContractAndInsurance = orderService.getOrderNoForRemindContractAndInsurance();
        notifyService.notifyOrderWithoutContract(orderNoForRemindContractAndInsurance);
    }

    /**
     * 通知 SEALAND 訂單投保提醒
     */
    public void notifyDealerOrderInsurance() {
        List<String> dealerOrderNoForRemindInsurance = dealerOrderService.getDealerOrderNoForRemindInsurance();
        notifyService.notifyDealerOrderInsurance(dealerOrderNoForRemindInsurance);
    }

    /**
     * 通知 SEALAND 訂單出車異常
     */
    public void notifyDealerOrderDepartAbnormal() {
        List<String> dealerOrderNoForDepartAbnormal = dealerOrderService.getDealerOrderNoForDepartAbnormal();
        notifyService.notifyDealerOrderDepartAbnormal(dealerOrderNoForDepartAbnormal);
    }

    /**
     * 長租契約自動展期到訂閱約
     */
    public void autoExpendLrentalContract() {
        Instant instant = DateUtil.convertToStartOfInstant(Instant.now()).minus(1, ChronoUnit.DAYS);
        List<Orders> orderList = orderRepository.findAll((Specification<Orders>) (root, query, builder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(builder.equal(root.get(Orders.Fields.status), OrderStatus.DEPART.getStatus()));
            predicates.add(builder.isNotNull(root.get(Orders.Fields.lrentalContractNo)));
            predicates.add(builder.between(root.get(Orders.Fields.expectEndDate), instant, instant.plus(1, ChronoUnit.DAYS)));
            predicates.add(builder.notEqual(root.get(Orders.Fields.renewType), RenewType.RENEW));
            return builder.and(predicates.toArray(new Predicate[0]));
        });
        Instant contractEndDate = DateUtil.convertToStartOfInstant(Instant.now().atZone(DateUtils.ZONE_TPE).plusYears(1).toInstant());
        List<Orders> saveList = new ArrayList<>();
        for (Orders order : orderList) {

            MainContract mainContract = order.getContract().getMainContract();

            CarBaseInfoSearchResponse carBaseInfoSearchResponse = crsService.getCar(mainContract.getPlateNo());
            if (Optional.ofNullable(carBaseInfoSearchResponse).map(CarBaseInfoSearchResponse::getCarBase).map(CarBase::getBuId).filter(buid -> Objects.equals(buid, BuIdEnum.subscribe.getCode())).isPresent()) {
                try {

                    PurchaseProjectCarSearchResponse purchaseProjectCarSearchResponse = crsService.searchProjectCar(carBaseInfoSearchResponse.getCarNo());
                    Cars cars = carsService.findByPlateNo(mainContract.getPlateNo());
                    MileagePriceInfoWrapper priceInfoWrapper = (MileagePriceInfoWrapper) priceInfoService.getPriceInfoWrapper(order.getOrderNo()).getByCategoryType(MileageFee, Pay);
                    double mileageFee = priceInfoWrapper.getMileage();
                    ContractSearchRep contractSearchRep = lrentalServer.getContractInfo(order.getLrentalContractNo());
                    ContractAddReq req = new ContractAddReq();
                    req.setContractType(ContractEnum.ContractType.add_customer_subscribe);
                    req.setPlateNo(mainContract.getPlateNo());
                    req.setContractStartDate(DateUtil.transferADDateToMinguoDate(DateUtil.convertToStartOfInstant(Instant.now())));
                    req.setContractEndDate(DateUtil.transferADDateToMinguoDate(contractEndDate));
                    req.setUserId(configService.getSubscribeConfig().getSubscribeDefaultMemberId());
                    req.setBuID(BuIdEnum.subscribe.getCode());
                    req.setOrderNo(order.getOrderNo());
                    req.setRentalTaxID(mainContract.getIdNo());
                    req.setMonthlyVatRent(mainContract.getOriginalPriceInfo().getUseMonthlyFee());
                    req.setMonthlyRent((int) Math.round(mainContract.getOriginalPriceInfo().getUseMonthlyFee() / 1.05));
                    req.setPromiseAmount(mainContract.getOriginalPriceInfo().getSecurityDepositInfo().getSecurityDeposit());
                    req.setMileageRate(mileageFee);
                    req.setFixAmount((int) (mileageFee * 3000));
                    req.setReplaceCode(Optional.ofNullable(contractSearchRep).map(ContractSearchRep::getDachang).orElse("0"));
                    req.setContractMemo(String.format("因訂單：%s已過預期還車時間，故自動產生客戶約", order.getOrderNo()));
                    req.setCarSource(CarDefine.CarState.NEW.equals(cars.getCarState()) ? "0" : "1");
                    if (purchaseProjectCarSearchResponse != null) {
                        req.validateProjectCar(purchaseProjectCarSearchResponse, contractEndDate);
                    }
                    String daNo = lrentalServer.addContract(req);
                    order.setLrentalContractNo(daNo);
                    saveList.add(order);
                } catch (Exception e) {
                    Map<String, Object> map = new LinkedHashMap<>();
                    map.put("orderNo", order.getOrderNo());
                    map.put("errMsg", e.getMessage());
                    mattermostServer.notify("訂單自動建立長租契約展期失敗", map, e);
                }
            }
        }
        orderRepository.saveAll(saveList);
    }


    /**
     * 長租契約自動展期到訂閱約
     */
    public void autoExpendLrentalDealerContract() {
        Instant instant = DateUtil.convertToStartOfInstant(Instant.now()).minus(1, ChronoUnit.DAYS);
        List<DealerOrder> orderList = dealerOrderRepository.findAll((Specification<DealerOrder>) (root, query, builder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(builder.equal(root.get(DealerOrder.Fields.orderStatus), ContractStatus.GOING.getCode()));
            predicates.add(builder.isNotNull(root.get(DealerOrder.Fields.lrentalContractNo)));
            predicates.add(builder.between(root.get(DealerOrder.Fields.expectReturnDate), instant, instant.plus(1, ChronoUnit.DAYS)));
            predicates.add(builder.equal(root.get(DealerOrder.Fields.isReturned), false));
            return builder.and(predicates.toArray(new Predicate[0]));
        });
        List<DealerOrderQueryResponse> dealerOrderQueryResponses = dealerOrderService.createDealerOrderQueryResponsesWithUserInfo(orderList);
        Instant contractEndDate = DateUtil.convertToStartOfInstant(Instant.now().atZone(DateUtils.ZONE_TPE).plusYears(1).toInstant());
        List<DealerOrder> saveList = new ArrayList<>();
        for (DealerOrderQueryResponse dealerOrderQueryResponse : dealerOrderQueryResponses) {

            CarBaseInfoSearchResponse carBaseInfoSearchResponse = crsService.getCar(dealerOrderQueryResponse.getPlateNo());
            if (Optional.ofNullable(carBaseInfoSearchResponse).map(CarBaseInfoSearchResponse::getCarBase).map(CarBase::getBuId).filter(buid -> Objects.equals(buid, BuIdEnum.subscribe.getCode())).isPresent()) {
                try {
                    PurchaseProjectCarSearchResponse purchaseProjectCarSearchResponse = crsService.searchProjectCar(carBaseInfoSearchResponse.getCarNo());
                    Cars cars = carsService.findByPlateNo(dealerOrderQueryResponse.getPlateNo());
                    MileagePriceInfoWrapper priceInfoWrapper = (MileagePriceInfoWrapper) priceInfoService.getPriceInfoWrapper(dealerOrderQueryResponse.getOrderNo()).getByCategoryType(MileageFee, Pay);
                    double mileageFee = priceInfoWrapper.getMileage();
                    ContractSearchRep contractSearchRep = lrentalServer.getContractInfo(dealerOrderQueryResponse.getLrentalContractNo());
                    ContractAddReq req = new ContractAddReq();
                    req.setContractType(ContractEnum.ContractType.add_customer_subscribe);
                    req.setPlateNo(dealerOrderQueryResponse.getPlateNo());
                    req.setContractStartDate(DateUtil.transferADDateToMinguoDate(DateUtil.convertToStartOfInstant(Instant.now())));
                    req.setContractEndDate(DateUtil.transferADDateToMinguoDate(contractEndDate));
                    req.setUserId(configService.getSubscribeConfig().getSubscribeDefaultMemberId());
                    req.setBuID(BuIdEnum.subscribe.getCode());
                    req.setOrderNo(dealerOrderQueryResponse.getOrderNo());
                    req.setRentalTaxID(dealerOrderQueryResponse.getCustomerInfo().getIdNo());
                    req.setMonthlyVatRent(dealerOrderQueryResponse.getSubscriptionInfo().getMonthlyFee());
                    req.setMonthlyRent((int) Math.round(dealerOrderQueryResponse.getSubscriptionInfo().getMonthlyFee() / 1.05));
                    req.setPromiseAmount(dealerOrderQueryResponse.getSubscriptionInfo().getSecurityDeposit());
                    req.setMileageRate(mileageFee);
                    req.setFixAmount((int) (mileageFee * 3000));
                    req.setReplaceCode(Optional.ofNullable(contractSearchRep).map(ContractSearchRep::getDachang).orElse("0"));
                    req.setContractMemo(String.format("因訂單：%s已過預期還車時間，故自動產生客戶約", dealerOrderQueryResponse.getOrderNo()));
                    req.setCarSource(CarDefine.CarState.NEW.equals(cars.getCarState()) ? "0" : "1");
                    if (purchaseProjectCarSearchResponse != null) {
                        req.validateProjectCar(purchaseProjectCarSearchResponse, contractEndDate);
                    }
                    String daNo = lrentalServer.addContract(req);
                    dealerOrderQueryResponse.setLrentalContractNo(daNo);
                    saveList.add(dealerOrderQueryResponse.toEntity());
                } catch (Exception e) {
                    Map<String, Object> map = new LinkedHashMap<>();
                    map.put("orderNo", dealerOrderQueryResponse.getOrderNo());
                    map.put("errMsg", e.getMessage());
                    mattermostServer.notify("訂單自動建立經銷商長租契約展期失敗", map, e);
                }
            }
        }
        dealerOrderRepository.saveAll(saveList);
    }
}
