package com.carplus.subscribe.model.request.dealer;

import carplus.common.enums.HeaderDefine;
import com.carplus.subscribe.enums.ContractStatus;
import com.carplus.subscribe.model.request.car.CarValidationResult;
import com.carplus.subscribe.model.response.dealer.DealerOrderExcel;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.Instant;
import java.util.Arrays;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
public class DealerOrderCreateRequest extends BaseDealerOrderRequest {

    @NotBlank(message = "車牌號碼不可為空")
    @Schema(description = "車牌號碼", example = "ABC-1234")
    private String plateNo;

    @Schema(description = "經銷商名稱", example = "SEALAND")
    @JsonSetter(nulls = Nulls.SKIP)
    private String dealerName = HeaderDefine.SystemKind.SEALAND;

    @Schema(description = "訂單狀態", example = "0")
    @JsonSetter(nulls = Nulls.SKIP)
    private Integer orderStatus = ContractStatus.CREATE.getCode();

    @Schema(description = "是否新訂單", example = "true")
    @NotNull(message = "是否新訂單不可為空")
    private Boolean isNewOrder;

    @Schema(description = "是否通過授信", example = "true")
    @JsonSetter(nulls = Nulls.SKIP)
    private Boolean isAudit = true;

    @Schema(description = "是否支付保證金", example = "true")
    @JsonSetter(nulls = Nulls.SKIP)
    private Boolean isPaySecurityDeposit = true;

    @Schema(description = "保證金支付時間", example = "2024-06-01T00:00:00Z")
    @NotNull(message = "保證金支付時間不可為空")
    private Instant securityDepositDate;

    @Schema(description = "母約編號", example = "24063012345")
    @NotBlank(message = "母約編號不可為空")
    private String parentOrderNo;

    @Schema(description = "期數", example = "1-1")
    @NotBlank(message = "期數不可為空")
    private String stage;

    @Schema(description = "保險ID")
    private String insuranceId;

    @Schema(description = "款項明細 (待確認)")
    private String infoDetail;

    @Schema(description = "是否建立格上會員")
    private Boolean isRegister;

    @Schema(description = "是否投保免責險")
    private boolean disclaimer;

    @Schema(description = "訂車人資訊")
    @Valid
    private DealerCustomerInfoForCreate customerInfo;

    @Schema(description = "訂閱方案資訊")
    @Valid
    private DealerSubscriptionInfo subscriptionInfo;

    public DealerOrderCreateRequest(DealerOrderExcel dealerOrderExcel, Map<String, String> stationsNameCodeMap, CarValidationResult carValidationResult) {
        this.setOrderNo(dealerOrderExcel.getOrderNo());
        this.setPlateNo(carValidationResult.getPlateNo());
        this.setDealerName(dealerOrderExcel.getDealerName());
        this.setIsNewOrder(Arrays.asList("1-1", "1").contains(dealerOrderExcel.getStage()));
        this.setSecurityDepositDate(dealerOrderExcel.getSecurityDepositDate().toInstant());
        this.setParentOrderNo(dealerOrderExcel.getParentOrderNo());
        this.setPreviousOrderNo(dealerOrderExcel.getPreviousOrderNo());
        this.setStage(dealerOrderExcel.getStage());
        this.setCustomerInfo(buildDealerCustomerInfo(dealerOrderExcel));
        this.setSubscriptionInfo(buildDealerSubscriptionInfo(dealerOrderExcel, stationsNameCodeMap));
    }

    private DealerCustomerInfoForCreate buildDealerCustomerInfo(DealerOrderExcel dealerOrderExcel) {
        DealerCustomerInfoForCreate dealerCustomerInfoForCreate = new DealerCustomerInfoForCreate();
        dealerCustomerInfoForCreate.setUserName(dealerOrderExcel.getUserName());
        dealerCustomerInfoForCreate.setIdNo(dealerOrderExcel.getIdNo());
        dealerCustomerInfoForCreate.setNationalCode(dealerOrderExcel.getNationalCode());
        dealerCustomerInfoForCreate.setMainCell(dealerOrderExcel.getMainCell());
        dealerCustomerInfoForCreate.setBirthDay(dealerOrderExcel.getBirthDay());
        dealerCustomerInfoForCreate.setEmail(dealerOrderExcel.getEmail());
        dealerCustomerInfoForCreate.setCity(dealerOrderExcel.getCity());
        dealerCustomerInfoForCreate.setArea(dealerOrderExcel.getArea());
        dealerCustomerInfoForCreate.setAddress(dealerOrderExcel.getAddress());
        dealerCustomerInfoForCreate.setIsForeigner(0);
        dealerCustomerInfoForCreate.setVatNumber(dealerOrderExcel.getVatNumber());
        dealerCustomerInfoForCreate.setCompanyName(dealerOrderExcel.getCompanyName());
        return dealerCustomerInfoForCreate;
    }

    private DealerSubscriptionInfo buildDealerSubscriptionInfo(DealerOrderExcel dealerOrderExcel, Map<String, String> stationsNameCodeMap) {
        DealerSubscriptionInfo dealerSubscriptionInfo = new DealerSubscriptionInfo();
        dealerSubscriptionInfo.setSecurityDeposit(dealerOrderExcel.getSecurityDeposit());
        dealerSubscriptionInfo.setMonthlyFee(dealerOrderExcel.getMonthlyFee());
        dealerSubscriptionInfo.setActualMileageRate(dealerOrderExcel.getActualMileageRate());
        dealerSubscriptionInfo.setOriginalMileageRate(dealerOrderExcel.getOriginalMileageRate());
        dealerSubscriptionInfo.setExpectDepartStation(stationsNameCodeMap.get(dealerOrderExcel.getExpectDepartStationName()));
        dealerSubscriptionInfo.setExpectReturnStation(stationsNameCodeMap.get(dealerOrderExcel.getExpectReturnStationName()));
        dealerSubscriptionInfo.setExpectDepartDate(dealerOrderExcel.getExpectDepartDate().toInstant());
        dealerSubscriptionInfo.setExpectReturnDate(dealerOrderExcel.getExpectReturnDate().toInstant());
        dealerSubscriptionInfo.setSubscribeMonth(dealerOrderExcel.getSubscribeMonth());
        dealerSubscriptionInfo.setTotalAmt(0);
        dealerSubscriptionInfo.setMileageRateDiscount(1d);
        dealerSubscriptionInfo.setPrepaidMonths(dealerOrderExcel.getPrepaidMonths());
        return dealerSubscriptionInfo;
    }
}
