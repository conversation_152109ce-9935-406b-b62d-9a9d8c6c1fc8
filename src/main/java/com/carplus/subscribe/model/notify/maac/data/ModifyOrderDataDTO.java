package com.carplus.subscribe.model.notify.maac.data;

import com.carplus.subscribe.model.notify.maac.BaseDataDTO;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class ModifyOrderDataDTO extends BaseDataDTO {

    @JsonProperty("line_push")
    private LinePushDTO linePush;
    @JsonProperty("pnp")
    private PnpDTO pnp;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LinePushDTO {
        @JsonProperty("stage")
        private String stage;
        @JsonProperty("customer_name")
        private String customerName;
        @JsonProperty("order_id")
        private String orderId;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PnpDTO {
        @JsonProperty("order_id")
        private String orderId;
        @JsonProperty("car_info")
        private String carInfo;
        @JsonProperty("url")
        private String url;
    }
}
