package com.carplus.subscribe.utils;

import carplus.common.utils.DateUtils;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.enums.CancellationPolicy;
import com.carplus.subscribe.enums.CustSource;
import com.carplus.subscribe.enums.OrderStatus;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.model.SecurityDepositInfo;
import com.carplus.subscribe.model.priceinfo.resp.CancelOrderCalculateResponse;
import org.springframework.lang.Nullable;

import java.time.Instant;
import java.util.Date;
import java.util.Map;
import java.util.Optional;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.ORDER_PRICE_INFO_SECURITY_DEPOSIT_NON_PAYMENT;
import static java.time.temporal.ChronoUnit.DAYS;

/**
 * 計算金額 utils
 */
public final class PriceUtils {

    /**
     * 取得折扣後的整數金額
     *
     * @param price    原金額
     * @param disCount 折扣值 ex: 5代表 價格打95折
     */
    public static int priceOff(int price, double disCount) {
        return (int) Math.round(price * ((100d - disCount) / 100)); // 金額 *（扣除折扣後的百分比）並四捨五入
    }

    /**
     * 取得折扣後的整數金額
     *
     * @param number 要格式化的整數
     * @return 格式化後的字符串，每三位數字用逗號分隔
     */
    public static String formatWithThousandsSeparator(int number) {
        return String.format("%,d", number);
    }

    /**
     * 是否為線上訂單
     */
    public static boolean isOnline(@Nullable CustSource custSource) {
        return CustSource.GOSMART == custSource || CustSource.WEB == custSource;
    }

    public static CancelOrderCalculateResponse calculateCancelOrder(Orders order, boolean isNewCar) {
        Map<String, CancellationPolicy> map = calculateCancelOrderLevelsAmt(isNewCar);
        map.forEach((key, value) -> value.calculateCancelPolicy(order.getContract().getMainContract().getOriginalPriceInfo().getSecurityDepositInfo().getSecurityDeposit()));
        CancellationPolicy level = null;
        if (order.getStatus() < OrderStatus.BOOKING.getStatus() || !order.getIsNewOrder()) {
            level = CancellationPolicy.of(CancellationPolicy.SubscribeNewCar.ofDays(0));
        } else {
            level = getCancelOrderLevel(
                Optional.ofNullable(order.getContract().getMainContract().getOriginalPriceInfo().getSecurityDepositInfo())
                    .map(SecurityDepositInfo::getSecurityDepositDate).map(Date::toInstant).orElseThrow(() -> new SubscribeException(
                        ORDER_PRICE_INFO_SECURITY_DEPOSIT_NON_PAYMENT)), isNewCar);
        }
        level.calculateCancelPolicy(order.getContract().getMainContract().getOriginalPriceInfo().getSecurityDepositInfo().getSecurityDeposit());
        CancelOrderCalculateResponse response = new CancelOrderCalculateResponse();
        response.setLevel(level);
        response.setLevelRefundAmtMap(map);
        response.setSecurityDepositAmt(order.getContract().getMainContract().getOriginalPriceInfo().getSecurityDepositInfo().getSecurityDeposit());
        response.setPaidSecurityDeposit(Optional.ofNullable(order.getContract().getMainContract().getOriginalPriceInfo().getSecurityDepositInfo()).map(SecurityDepositInfo::getPaidSecurityDeposit).orElse(0));
        return response;
    }

    /**
     * 取得級距Map
     */
    private static Map<String, CancellationPolicy> calculateCancelOrderLevelsAmt(boolean isNewCar) {
        if (isNewCar) {
            return CancellationPolicy.SubscribeNewCar.policyMap;
        }
        return CancellationPolicy.SubscribeOldCar.policyMap;
    }

    /**
     * 訂單取消級距
     */
    public static CancellationPolicy getCancelOrderLevel(Instant date, boolean isNewCar) {
        long diff = DAYS.between(Instant.now().atZone(DateUtils.ZONE_TPE).toLocalDate(), date.atZone(DateUtils.ZONE_TPE).toLocalDate().atStartOfDay());
        CancellationPolicy policy;
        if (isNewCar) {
            diff *= -1;
            policy = CancellationPolicy.of(CancellationPolicy.SubscribeNewCar.ofDays(diff));
        } else {
            policy = CancellationPolicy.of(CancellationPolicy.SubscribeOldCar.ofDays(diff));
        }
        return policy;
    }
}
