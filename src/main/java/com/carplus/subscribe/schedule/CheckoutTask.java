package com.carplus.subscribe.schedule;


import carplus.common.response.exception.BadRequestException;
import com.carplus.subscribe.db.mysql.dao.AccountDetailRepository;
import com.carplus.subscribe.db.mysql.dao.AccountRepository;
import com.carplus.subscribe.db.mysql.dao.InvoicesRepository;
import com.carplus.subscribe.db.mysql.dao.OrderRepository;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.db.mysql.entity.invoice.Invoices;
import com.carplus.subscribe.db.mysql.entity.payment.Account;
import com.carplus.subscribe.server.MattermostServer;
import com.carplus.subscribe.service.CheckoutService;
import com.carplus.subscribe.service.InvoiceServiceV2;
import com.carplus.subscribe.service.NotifyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class CheckoutTask {

    @Autowired
    private InvoiceServiceV2 invoiceService;

    @Autowired
    private InvoicesRepository invoicesRepository;

    @Autowired
    private AccountDetailRepository accountDetailRepository;

    @Autowired
    private AccountRepository accountRepository;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private CheckoutService checkoutService;
    @Autowired
    private MattermostServer mattermostServer;

    @Autowired
    private NotifyService notifyService;

    public void dailyCheckOut() {
        log.info("日結開始");
        List<String> orderIds = new ArrayList<>();
        List<Invoices> invoicesList = invoicesRepository.findInvoicesNeedCheckout();
        List<Long> accountIds = accountDetailRepository.findAllUnCheckOut();
        List<Account> accountList = accountRepository.findAllById(accountIds);
        orderIds.addAll(invoicesList.stream().map(Invoices::getOrderNo).collect(Collectors.toSet()));
        orderIds.addAll(accountList.stream().map(Account::getOrderNo).collect(Collectors.toSet()));

        List<Orders> ordersList = orderRepository.findAllById(orderIds);
        Set<String> errorOrders = new HashSet<>();
        Set<String> errorNotEvenOrders = new HashSet<>();
        Set<String> errorFinanceOrders = new HashSet<>();
        for (Orders orders : ordersList) {

            try {
                checkoutService.checkOutWithNewTransaction(orders);
            } catch (Exception e) {
                if (e instanceof BadRequestException) {
                    errorNotEvenOrders.add(orders.getOrderNo());
                } else {
                    errorFinanceOrders.add(orders.getOrderNo());
                }
                errorOrders.add(orders.getOrderNo());
                log.error("[日結錯誤] - orderNo:{}", orders.getOrderNo(), e);
            }
        }

        if (!errorOrders.isEmpty()) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("今日日結訂單編號", orderIds);
            map.put("日結失敗訂單編號", errorOrders);
            map.put("帳務與發票不平編號", errorNotEvenOrders);
            map.put("財務中台立帳失敗", errorFinanceOrders);
            mattermostServer.notify("日節異常", map, null);
            if (!errorNotEvenOrders.isEmpty()) {
                notifyService.notifyCheckOutFail(new ArrayList<>(errorNotEvenOrders));
            }
        }
        log.info("日結結束");
    }
}
