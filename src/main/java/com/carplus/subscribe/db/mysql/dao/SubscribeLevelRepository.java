package com.carplus.subscribe.db.mysql.dao;

import com.carplus.subscribe.db.mysql.entity.SubscribeLevel;
import com.carplus.subscribe.model.request.subscribelevel.SubscribeLevelCriteria;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;

@Repository
public class SubscribeLevelRepository extends SimpleJpaRepository<SubscribeLevel, Integer> {

    private final EntityManager em;

    public SubscribeLevelRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(SubscribeLevel.class, em);
        this.em = em;
    }

    public void updateIsDeleted(Integer id, boolean isDeleted, String memberId) {
        em.createQuery("update subscribe_level set isDeleted = :isDeleted where id = :id")
            .setParameter(SubscribeLevel.Fields.id, id)
            .setParameter(SubscribeLevel.Fields.isDeleted, isDeleted)
            .setParameter(SubscribeLevel.Fields.updatedBy, memberId)
            .executeUpdate();
    }

    public SubscribeLevel findByLevel(Integer level) {
        SubscribeLevelCriteria criteria = new SubscribeLevelCriteria();
        criteria.setLevel(level);
        List<SubscribeLevel> results = findByPage(criteria, 0, 1);
        return results.isEmpty() ? null : results.get(0);
    }

    public SubscribeLevel findByName(String name) {
        SubscribeLevelCriteria criteria = new SubscribeLevelCriteria();
        criteria.setName(name);
        List<SubscribeLevel> results = findByPage(criteria, 0, 1);
        return results.isEmpty() ? null : results.get(0);
    }

    public SubscribeLevel findByLevelOrName(Integer level, String name) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<SubscribeLevel> cq = cb.createQuery(SubscribeLevel.class);
        Root<SubscribeLevel> root = cq.from(SubscribeLevel.class);

        List<Predicate> predicates = new ArrayList<>();
        if (level != null) {
            predicates.add(
                cb.or(
                    cb.equal(root.get(SubscribeLevel.Fields.id), level),
                    cb.equal(root.get(SubscribeLevel.Fields.level), level)
                )
            );
        }
        if (name != null && !name.isEmpty()) {
            predicates.add(cb.equal(root.get(SubscribeLevel.Fields.name), name));
        }

        // 使用 OR 條件連接
        Predicate predicate = cb.or(predicates.toArray(new Predicate[0]));
        cq.where(predicate);

        List<SubscribeLevel> results = em.createQuery(cq)
            .setMaxResults(1)
            .getResultList();
        return results.isEmpty() ? null : results.get(0);
    }

    public long count(SubscribeLevelCriteria criteria) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Long> cq = cb.createQuery(Long.class);
        Root<SubscribeLevel> root = cq.from(SubscribeLevel.class);

        Predicate predicate = buildPredicate(cb, root, criteria);
        cq.select(cb.count(root)).where(predicate);

        return em.createQuery(cq).getSingleResult();
    }

    public List<SubscribeLevel> findByPage(SubscribeLevelCriteria criteria, int offset, int limit) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<SubscribeLevel> cq = cb.createQuery(SubscribeLevel.class);
        Root<SubscribeLevel> root = cq.from(SubscribeLevel.class);

        Predicate predicate = buildPredicate(cb, root, criteria);
        cq.select(root).where(predicate).orderBy(cb.asc(root.get(SubscribeLevel.Fields.id)));

        return em.createQuery(cq)
            .setFirstResult(offset)
            .setMaxResults(limit)
            .getResultList();
    }

    private Predicate buildPredicate(CriteriaBuilder cb, Root<SubscribeLevel> root, SubscribeLevelCriteria criteria) {
        List<Predicate> predicates = new ArrayList<>();
        if (criteria.getLevel() != null) {
            predicates.add(cb.equal(root.get(SubscribeLevel.Fields.level), criteria.getLevel()));
        }
        if (criteria.getName() != null && !criteria.getName().isEmpty()) {
            predicates.add(cb.like(root.get(SubscribeLevel.Fields.name), "%" + criteria.getName() + "%"));
        }
        if (criteria.getSecurityDepositMin() != null) {
            predicates.add(cb.ge(root.get(SubscribeLevel.Fields.securityDeposit), criteria.getSecurityDepositMin()));
        }
        if (criteria.getSecurityDepositMax() != null) {
            predicates.add(cb.le(root.get(SubscribeLevel.Fields.securityDeposit), criteria.getSecurityDepositMax()));
        }
        if (criteria.getMonthlyFeeMin() != null) {
            predicates.add(cb.ge(root.get(SubscribeLevel.Fields.monthlyFee), criteria.getMonthlyFeeMin()));
        }
        if (criteria.getMonthlyFeeMax() != null) {
            predicates.add(cb.le(root.get(SubscribeLevel.Fields.monthlyFee), criteria.getMonthlyFeeMax()));
        }
        if (criteria.getMileageFeeMin() != null) {
            predicates.add(cb.ge(root.get(SubscribeLevel.Fields.mileageFee), criteria.getMileageFeeMin()));
        }
        if (criteria.getMileageFeeMax() != null) {
            predicates.add(cb.le(root.get(SubscribeLevel.Fields.mileageFee), criteria.getMileageFeeMax()));
        }
        if (criteria.getTypes() != null && !criteria.getTypes().isEmpty()) {
            predicates.add(root.get(SubscribeLevel.Fields.type).in(criteria.getTypes()));
        }
        if (criteria.getAutoCredit() != null) {
            predicates.add(cb.equal(root.get(SubscribeLevel.Fields.autoCredit), criteria.getAutoCredit()));
        }
        return cb.and(predicates.toArray(new Predicate[0]));
    }
}
