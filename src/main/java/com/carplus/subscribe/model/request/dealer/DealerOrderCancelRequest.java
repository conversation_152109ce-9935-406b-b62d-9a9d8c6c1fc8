package com.carplus.subscribe.model.request.dealer;

import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrder;
import com.carplus.subscribe.enums.ContractStatus;
import com.carplus.subscribe.model.response.dealer.DealerOrderExcel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.Instant;

@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
public class DealerOrderCancelRequest extends BaseDealerOrderRequest {

    @Schema(description = "車牌號碼", example = "ABC-1234")
    private String plateNo;

    @Schema(description = "經銷商名稱")
    private String dealerName;

    @Schema(description = "訂單狀態")
    private Integer orderStatus = ContractStatus.CANCEL.getCode();

    @Schema(description = "是否新訂單")
    private Boolean isNewOrder;

    @Schema(description = "是否通過授信")
    private Boolean isAudit;

    @Schema(description = "是否支付保證金")
    private Boolean isPaySecurityDeposit;

    @Schema(description = "母約編號")
    private String parentOrderNo;

    @Schema(description = "期數")
    private String stage;

    @Schema(description = "取消時間")
    @NotNull(message = "取消時間不可為空")
    private Instant cancelDate;

    @Schema(description = "取消備註")
    private String cancelRemark;

    @Schema(description = "訂車人資訊")
    private DealerCustomerInfoAllOptional customerInfo;

    @Schema(description = "訂閱方案資訊")
    @Valid
    private DealerSubscriptionInfoForCancel subscriptionInfo;

    public DealerOrderCancelRequest(DealerOrder dealerOrder, DealerOrderExcel dealerOrderExcel) {
        this.setOrderNo(dealerOrder.getOrderNo());
        this.setPlateNo(dealerOrder.getPlateNo());
        this.setOrderStatus(ContractStatus.CANCEL.getCode());
        this.cancelDate = dealerOrderExcel.getCancelDate().toInstant();
        this.cancelRemark = "匯入 Excel 取消訂單";
        DealerSubscriptionInfoForCancel subscriptionInfo = new DealerSubscriptionInfoForCancel();
        subscriptionInfo.setTotalAmt(dealerOrder.getTotalAmt());
        this.subscriptionInfo = subscriptionInfo;
    }
}
