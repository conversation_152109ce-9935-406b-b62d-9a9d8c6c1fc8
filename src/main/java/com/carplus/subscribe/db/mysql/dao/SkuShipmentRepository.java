package com.carplus.subscribe.db.mysql.dao;

import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.db.mysql.entity.contract.SkuShipment;
import com.carplus.subscribe.enums.PayStatus;
import com.carplus.subscribe.model.shipment.SkuShipmentCriteria;
import com.carplus.subscribe.model.shipment.SkuShipmentResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.*;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Repository
public class SkuShipmentRepository extends SimpleJpaRepository<SkuShipment, Integer> {

    private final EntityManager em;

    public SkuShipmentRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(SkuShipment.class, em);
        this.em = em;
    }

    @Override
    public Optional<SkuShipment> findById(Integer id) {
        // 使用 Criteria API 建立查詢，預先 fetch 所有需要的關聯資料
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<SkuShipment> cq = cb.createQuery(SkuShipment.class);
        Root<SkuShipment> skuShipmentRoot = cq.from(SkuShipment.class);

        // Fetch OrderPriceInfo
        Fetch<SkuShipment, OrderPriceInfo> orderPriceInfoFetch =
            skuShipmentRoot.fetch(SkuShipment.Fields.orderPriceInfo, JoinType.LEFT);

        // Fetch Orders through OrderPriceInfo
        orderPriceInfoFetch.fetch(OrderPriceInfo.Fields.order, JoinType.LEFT);

        // Fetch SkuShipmentHistory list
        skuShipmentRoot.fetch(SkuShipment.Fields.historyList, JoinType.LEFT);

        // 設定查詢條件 - 根據 id 查詢
        cq.where(cb.equal(skuShipmentRoot.get(SkuShipment.Fields.id), id));

        try {
            SkuShipment result = em.createQuery(cq).getSingleResult();
            return Optional.of(result);
        } catch (NoResultException e) {
            return Optional.empty();
        }
    }

    public List<SkuShipmentResponse> search(SkuShipmentCriteria criteria, Integer skip, Integer limit) {
        // 當沒有 payStatus 過濾條件時，可以直接在資料庫層面分頁
        if (CollectionUtils.isEmpty(criteria.getPayStatus())) {
            return aggregateResults(doQuery(criteria, skip, limit));
        }

        // 先取得原始資料
        List<SkuShipmentResponse> rawResults = doQuery(criteria);

        // 進行分組聚合
        List<SkuShipmentResponse> aggregatedResults = aggregateResults(rawResults);

        // 如果有 payStatus 過濾條件，進行過濾
        return applyPayStatusFilter(aggregatedResults, criteria.getPayStatus())
            .skip(skip != null ? skip : 0)
            .limit(limit != null ? limit : Long.MAX_VALUE)
            .collect(Collectors.toList());
    }

    private Stream<SkuShipmentResponse> applyPayStatusFilter(List<SkuShipmentResponse> results, List<PayStatus> payStatusList) {
        return results.stream()
            .peek(response -> response.setSkuList(response.getSkuList().stream()
                .filter(sku -> payStatusList.contains(sku.getPayStatus()))
                .collect(Collectors.toList())))
            // 移除沒有符合條件 SKU 的記錄
            .filter(response -> !response.getSkuList().isEmpty());
    }

    private List<SkuShipmentResponse> doQuery(SkuShipmentCriteria criteria, Integer skip, Integer limit) {
        CriteriaBuilder cb = em.getCriteriaBuilder();

        // 第一步：查詢分頁後的 orderNo 列表
        CriteriaQuery<String> orderNoQuery = cb.createQuery(String.class);
        Root<SkuShipment> paginationSkuShipmentRoot = orderNoQuery.from(SkuShipment.class);

        Join<SkuShipment, OrderPriceInfo> paginationOpiJoin = paginationSkuShipmentRoot.join(SkuShipment.Fields.orderPriceInfo, JoinType.LEFT);
        Join<OrderPriceInfo, Orders> paginationOrderJoin = paginationOpiJoin.join(OrderPriceInfo.Fields.order, JoinType.LEFT);

        List<Predicate> orderNoPredicates = buildPredicates(criteria, paginationSkuShipmentRoot, paginationOrderJoin, cb);
        if (!orderNoPredicates.isEmpty()) {
            orderNoQuery.where(orderNoPredicates.toArray(new Predicate[0]));
        }
        orderNoQuery.select(paginationOrderJoin.get(Orders.Fields.orderNo)).distinct(true);

        TypedQuery<String> orderNoTypedQuery = em.createQuery(orderNoQuery);
        if (skip != null) {
            orderNoTypedQuery.setFirstResult(skip);
        }
        if (limit != null) {
            orderNoTypedQuery.setMaxResults(limit);
        }

        List<String> orderNos = orderNoTypedQuery.getResultList();
        if (CollectionUtils.isEmpty(orderNos)) {
            return Collections.emptyList();
        }

        // 第二步：查詢實際資料
        CriteriaQuery<SkuShipmentResponse> dataQuery = cb.createQuery(SkuShipmentResponse.class);
        Root<SkuShipment> resultSkuShipmentRoot = dataQuery.from(SkuShipment.class);

        Join<SkuShipment, OrderPriceInfo> resultOpiJoin = resultSkuShipmentRoot.join(SkuShipment.Fields.orderPriceInfo, JoinType.LEFT);
        Join<OrderPriceInfo, Orders> resultOrderJoin = resultOpiJoin.join(OrderPriceInfo.Fields.order, JoinType.LEFT);

        List<Predicate> dataPredicates = buildPredicates(criteria, resultSkuShipmentRoot, resultOrderJoin, cb);
        dataPredicates.add(resultOrderJoin.get(Orders.Fields.orderNo).in(orderNos));
        dataQuery.where(dataPredicates.toArray(new Predicate[0]));
        dataQuery.orderBy(cb.desc(resultOrderJoin.get(Orders.Fields.orderNo)));

        dataQuery.select(cb.construct(
            SkuShipmentResponse.class,
            resultSkuShipmentRoot,
            resultOpiJoin,
            resultOrderJoin
        ));

        return em.createQuery(dataQuery).getResultList();
    }

    private List<SkuShipmentResponse> doQuery(SkuShipmentCriteria criteria) {
        return doQuery(criteria, 0, Integer.MAX_VALUE);
    }

    private List<Predicate> buildPredicates(SkuShipmentCriteria criteria,
                                            Root<SkuShipment> skuShipmentRoot,
                                            Join<OrderPriceInfo, Orders> orderJoin,
                                            CriteriaBuilder cb) {
        List<Predicate> predicates = new ArrayList<>();
        if (criteria.getOrderNo() != null) {
            predicates.add(cb.equal(orderJoin.get(Orders.Fields.orderNo), criteria.getOrderNo()));
        }
        if (criteria.getSkuCode() != null) {
            predicates.add(cb.equal(skuShipmentRoot.get(SkuShipment.Fields.skuCode), criteria.getSkuCode()));
        }
        if (CollectionUtils.isNotEmpty(criteria.getStatus())) {
            predicates.add(skuShipmentRoot.get(SkuShipment.Fields.status).in(criteria.getStatus()));
        }
        // payStatus 不在這裡處理，因為需要在記憶體中過濾整個訂單級別的條件
        return predicates;
    }

    private List<SkuShipmentResponse> aggregateResults(List<SkuShipmentResponse> rawResults) {
        // 按 orderNo 分組
        Map<String, List<SkuShipmentResponse>> groupedByOrder = rawResults.stream()
            .collect(Collectors.groupingBy(SkuShipmentResponse::getOrderNo));

        return groupedByOrder.entrySet().stream()
            .map(orderEntry -> {
                String orderNo = orderEntry.getKey();
                List<SkuShipmentResponse> skuShipmentList = orderEntry.getValue();

                // 使用第一筆資料設定基本的訂單資訊
                SkuShipmentResponse firstResponse = skuShipmentList.get(0);
                SkuShipmentResponse aggregatedResponse = new SkuShipmentResponse();
                aggregatedResponse.setOrderNo(orderNo);
                aggregatedResponse.setOrderStatus(firstResponse.getOrderStatus());
                aggregatedResponse.setOrderStartDate(firstResponse.getOrderStartDate());

                // 收集此 orderNo 下所有 SKU
                List<SkuShipmentResponse.SkuOrderPriceInfo> aggregatedSkuList = skuShipmentList.stream()
                    .flatMap(response -> response.getSkuList().stream())
                    .collect(Collectors.groupingBy(SkuShipmentResponse.SkuOrderPriceInfo::getOrderPriceInfoId))
                    .values().stream()
                    .map(skuList -> {
                        // 使用第一筆資料設定基本的 SKU 資訊
                        SkuShipmentResponse.SkuOrderPriceInfo firstSku = skuList.get(0);
                        SkuShipmentResponse.SkuOrderPriceInfo aggregatedSku = new SkuShipmentResponse.SkuOrderPriceInfo();
                        aggregatedSku.setOrderPriceInfoId(firstSku.getOrderPriceInfoId());
                        aggregatedSku.setAmount(firstSku.getAmount());
                        aggregatedSku.setQuantity(firstSku.getQuantity());
                        aggregatedSku.setPayStatus(firstSku.getPayStatus());
                        aggregatedSku.setPayStatusName(firstSku.getPayStatusName());
                        aggregatedSku.setSkuCode(firstSku.getSkuCode());
                        aggregatedSku.setSkuName(firstSku.getSkuName());

                        // 聚合此 SKU 的所有出貨資訊
                        List<SkuShipmentResponse.ShipmentInfo> allShipments = skuList.stream()
                            .flatMap(sku -> sku.getSkuShipmentList().stream())
                            .collect(Collectors.toList());
                        aggregatedSku.setSkuShipmentList(allShipments);

                        return aggregatedSku;
                    })
                    .collect(Collectors.toList());

                aggregatedResponse.setSkuList(aggregatedSkuList);
                return aggregatedResponse;
            })
            .collect(Collectors.toList());
    }

    /**
     * 統計符合條件的記錄總數
     */
    public long count(SkuShipmentCriteria criteria) {
        // 當沒有 payStatus 過濾條件時，直接在資料庫層面統計
        if (CollectionUtils.isEmpty(criteria.getPayStatus())) {
            return countDistinctOrdersInDatabase(criteria);
        }

        // 有 payStatus 過濾條件時，需要在記憶體中過濾後計數
        List<SkuShipmentResponse> rawResults = doQuery(criteria);
        List<SkuShipmentResponse> aggregatedResults = aggregateResults(rawResults);

        return applyPayStatusFilter(aggregatedResults, criteria.getPayStatus()).count();
    }

    /**
     * 當沒有 payStatus 過濾條件時，直接在資料庫層面統計
     */
    private long countDistinctOrdersInDatabase(SkuShipmentCriteria criteria) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);
        Root<SkuShipment> root = countQuery.from(SkuShipment.class);

        Join<SkuShipment, OrderPriceInfo> opiJoin = root.join(SkuShipment.Fields.orderPriceInfo, JoinType.LEFT);
        Join<OrderPriceInfo, Orders> orderJoin = opiJoin.join(OrderPriceInfo.Fields.order, JoinType.LEFT);

        // 套用與搜尋方法相同的篩選條件
        List<Predicate> predicates = buildPredicates(criteria, root, orderJoin, cb);
        if (!predicates.isEmpty()) {
            countQuery.where(predicates.toArray(new Predicate[0]));
        }

        // 統計不重複的訂單編號
        countQuery.select(cb.countDistinct(orderJoin.get(Orders.Fields.orderNo)));

        return em.createQuery(countQuery).getSingleResult();
    }

    /**
     * Find shipment by OrderPriceInfo ID
     */
    private List<SkuShipment> findByOrderPriceInfoId(Integer orderPriceInfoId) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<SkuShipment> cq = cb.createQuery(SkuShipment.class);
        Root<SkuShipment> root = cq.from(SkuShipment.class);
        
        cq.select(root).where(cb.equal(root.get(SkuShipment.Fields.orderPriceInfoId), orderPriceInfoId));

        return em.createQuery(cq).getResultList();
    }

    public int countByOrderPriceInfoId(Integer orderPriceInfoId) {
        return findByOrderPriceInfoId(orderPriceInfoId).size();
    }
}