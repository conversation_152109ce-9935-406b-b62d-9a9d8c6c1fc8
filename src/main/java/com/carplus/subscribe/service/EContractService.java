package com.carplus.subscribe.service;

import carplus.common.enums.HeaderDefine;
import carplus.common.model.Page;
import carplus.common.model.PageRequest;
import carplus.common.utils.BeanUtils;
import carplus.common.utils.DateUtils;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.constant.EContractConstant;
import com.carplus.subscribe.constant.PDFConstant;
import com.carplus.subscribe.db.mysql.dao.EContractMailHistoryRepository;
import com.carplus.subscribe.db.mysql.dao.EContractRepository;
import com.carplus.subscribe.db.mysql.dao.EContractTemplateRepository;
import com.carplus.subscribe.db.mysql.dao.UploadFileRepository;
import com.carplus.subscribe.db.mysql.entity.UploadFile;
import com.carplus.subscribe.db.mysql.entity.cars.CarBrand;
import com.carplus.subscribe.db.mysql.entity.cars.CarModel;
import com.carplus.subscribe.db.mysql.entity.contract.*;
import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrder;
import com.carplus.subscribe.db.mysql.entity.econtract.EContract;
import com.carplus.subscribe.db.mysql.entity.econtract.EContractMailHistory;
import com.carplus.subscribe.db.mysql.entity.econtract.EContractTemplate;
import com.carplus.subscribe.enums.*;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.model.auth.AuthUser;
import com.carplus.subscribe.model.authority.MemberInfo;
import com.carplus.subscribe.model.cars.resp.CarResponse;
import com.carplus.subscribe.model.crs.CarBaseInfoSearchResponse;
import com.carplus.subscribe.model.notify.MailAttachment;
import com.carplus.subscribe.model.order.EContractTemplateResponse;
import com.carplus.subscribe.model.presign.GcsGetDownloadUrlReq;
import com.carplus.subscribe.model.presign.GcsUrlRes;
import com.carplus.subscribe.model.presign.UploadFilePresignedRes;
import com.carplus.subscribe.model.request.econtract.*;
import com.carplus.subscribe.model.response.econtract.ECFilesResponse;
import com.carplus.subscribe.model.response.econtract.EContractTemplateCSV;
import com.carplus.subscribe.model.response.econtract.EContractTemplateQueryResponse;
import com.carplus.subscribe.model.response.econtract.UploadFileResponse;
import com.carplus.subscribe.server.AuthServer;
import com.carplus.subscribe.server.AuthorityServer;
import com.carplus.subscribe.server.GoSmartServer;
import com.carplus.subscribe.server.cars.LrentalServer;
import com.carplus.subscribe.service.strategy.econtract.EContractRefEntityUpdater;
import com.carplus.subscribe.service.strategy.econtract.impl.EContractRefEntityFetcher;
import com.carplus.subscribe.service.strategy.econtract.impl.EContractRefEntityUpdaterFactory;
import com.carplus.subscribe.utils.ContractUtils;
import com.carplus.subscribe.utils.CsvUtil;
import com.carplus.subscribe.utils.HttpRequestUtils;
import com.carplus.subscribe.utils.OrderUtils;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Image;
import com.itextpdf.text.pdf.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.lang.NonNull;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.LockModeType;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.carplus.subscribe.enums.EContractType.E_CONTRACT;
import static com.carplus.subscribe.enums.OrderStatus.*;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.FILE_NOT_EXISTS;
import static com.carplus.subscribe.utils.ContractUtils.isStatusEqual;

@Slf4j
@Service
public class EContractService {

    @Autowired
    private AuthorityServer authorityServer;

    @Autowired
    private GoSmartServer goSmartServer;

    @Autowired
    private LrentalServer lrentalServer;

    @Autowired
    private EContractRepository eContractRepository;

    @Autowired
    private EContractTemplateRepository eContractTemplateRepository;

    @Autowired
    private EContractMailHistoryRepository eContractMailHistoryRepository;

    @Autowired
    private CarsService carsService;

    @Autowired
    private CrsService crsService;

    @Autowired
    private UploadFileRepository uploadFileRepository;

    @Autowired
    private AuthServer authServer;

    @Autowired
    private GcsService gcsService;
    @Autowired
    private NotifyToCService notifyToCService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private RentalTaskService rentalTaskService;
    @Autowired
    private EContractRefEntityFetcher eContractRefEntityFetcher;
    @Autowired
    private EContractRefEntityUpdaterFactory eContractRefEntityUpdaterFactory;

    public List<EContractTemplate> findAll() {
        return eContractTemplateRepository.findAll();
    }

    @Transactional(transactionManager = "mysqlTransactionManager", readOnly = true)
    public EContractTemplate findById(Integer templateId) {
        return eContractTemplateRepository.findById(templateId).orElse(null);
    }

    /**
     * 建立合約範本
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void createTemplate(EContractTemplate econtractTemplate) {
        eContractTemplateRepository.save(econtractTemplate);
    }

    /**
     * 更新合約範本
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void updateTemplate(EContractTemplate econtractTemplate) {
        eContractTemplateRepository.save(econtractTemplate);
    }

    /**
     * 產生合約代碼
     */
    @Lock(LockModeType.READ)
    public String generateTemplateCode() {
        String lastId = eContractTemplateRepository.getLastTemplateCode();
        String id;
        if (Objects.isNull(lastId)) {
            id = "SUB" + "00001";
        } else {
            long nonFormatNum = Long.parseLong(lastId.substring(3)) + 1;
            id = "SUB" + String.format("%05d", nonFormatNum);
        }
        return id;
    }

    /**
     * 產生版本號
     */
    @Lock(LockModeType.READ)
    public String generateVersionId(String templateCode) {
        String lastId = eContractTemplateRepository.getLastVersionId(templateCode);
        String date = DateUtils.toDateString(new Date(), "yyyyMMdd", DateUtils.ZONE_TPE);
        String id;
        if (Objects.isNull(lastId) || lastId.substring(8, 16).compareTo(date) < 0) {
            id = templateCode + date + "01";
        } else {
            id = templateCode + (Long.parseLong(lastId.substring(8)) + 1);
        }
        return id;
    }

    /**
     * 資料驗證
     */
    public void templateCreateRequestValidate(TemplateValidateReq req) {

        // 檢查範本是否存在
        if (eContractTemplateRepository.isTemplateIsExists(req)) {
            throw new SubscribeException(SubscribeHttpExceptionCode.ECONTRACT_TEMPLATE_EXISTS);
        }
    }

    /**
     * 透過車牌拿取可用的範本
     */
    public Integer getTemplateContractIdByCarNo(String carNo, EContractSource econtractSource, int month, boolean isDisclaimerFee) {
        CarResponse cars = carsService.getCarInfoByCarNo(carNo);
        // TODO 有虛擬車議題，待PO整理邏輯與開票
        CarBaseInfoSearchResponse carBaseInfoSearchResponse = crsService.getCar(cars.getPlateNo());
        if (carBaseInfoSearchResponse == null) {
            return null;
        }
        TemplateValidateReq validateReq = new TemplateValidateReq();
        validateReq.setConditionOrderSource(econtractSource.getCode());
        validateReq.setConditionOrderMonth(month);
        // 非長租一律歸屬訂閱
        if (BuIdEnum.lRental.getCode().equals(carBaseInfoSearchResponse.getBuId())) {
            validateReq.setConditionCarBu(BuIdEnum.lRental.getCode().toString());
        } else {
            validateReq.setConditionCarBu(BuIdEnum.subscribe.getCode().toString());
        }
        validateReq.setConditionCarBrand(cars.getCarModel().getBrandCode());
        validateReq.setConditionCarState(cars.getCarState().name());
        validateReq.setConditionCarModel(cars.getCarModel().getCarModelCode());
        validateReq.setConditionCarPriceStart(cars.getStdPrice());
        validateReq.setSeaLandCar(lrentalServer.getSeaLandContractInfoByPlatNo(cars.getPlateNo()) != null);
        validateReq.setDisclaimerFee(isDisclaimerFee);
        // 使用車籍的牌價而不是CRS
        validateReq.setConditionCarPriceEnd(cars.getStdPrice());
        List<EContractTemplate> templateList = eContractTemplateRepository.getTemplateList(validateReq);
        if (templateList.isEmpty()) {
            validateReq.setConditionCarModel(null);
            templateList = eContractTemplateRepository.getTemplateList(validateReq);
        }
        if (templateList.isEmpty()) {
            validateReq.setConditionCarBrand(null);
            templateList = eContractTemplateRepository.getTemplateList(validateReq);
        }
        templateList.sort(Comparator.comparing(EContractTemplate::getEnableDate).reversed());
        return templateList.stream().filter(temp -> temp.getEnableDate().isBefore(Instant.now())).findFirst().map(EContractTemplate::getTemplateId).orElse(null);
    }


    /**
     * 電子合約範本查詢
     *
     * @param pageRequest  頁面相關參數
     * @param queryRequest 查詢相關參數
     */
    public Page<EContractTemplateQueryResponse> searchByPage(PageRequest pageRequest, EContractTemplateCriteria queryRequest) {
        int limit = pageRequest.getLimit();
        int offset = pageRequest.getSkip();
        long total = eContractTemplateRepository.count(queryRequest);
        if (0 == total) {
            return Page.of(0, Collections.emptyList(), offset, limit);
        }
        List<EContractTemplateQueryResponse> list = searchPage(queryRequest, limit, offset);
        return Page.of(total, list, offset, limit);
    }

    /**
     * 電子合約範本版本歷程查詢
     */
    public Page<EContractTemplateResponse> getByTemplateCode(PageRequest pageRequest, EContractTemplateGetAllVersion req) {
        int limit = pageRequest.getLimit();
        int offset = pageRequest.getSkip();
        long total = eContractTemplateRepository.findByTemplateCodeCount(req.getTemplateCode(), limit, offset);
        if (0 == total) {
            return Page.of(0, Collections.emptyList(), offset, limit);
        }
        Map<String, MemberInfo> memberInfoMap = new HashMap<>();
        List<EContractTemplateResponse> list = eContractTemplateRepository.findByTemplateCode(req.getTemplateCode(), limit, offset).stream().map(
            econtractTemplate -> {
                EContractTemplateResponse response = BeanUtils.copyProperties(econtractTemplate, new EContractTemplateResponse());
                if (StringUtils.isNotBlank(econtractTemplate.getUpdateUser())) {
                    response.setUpdateMemberName(Optional.ofNullable(getMember(econtractTemplate.getUpdateUser(), memberInfoMap)).map(MemberInfo::getMemberName).orElseGet(econtractTemplate::getUpdateUser));
                }
                return response;
            }
        ).collect(Collectors.toList());

        return Page.of(total, list, offset, limit);
    }

    /**
     * 查詢資料
     *
     * @param queryRequest 查詢相關參數
     * @param limit        限制回傳筆數
     * @param offset       略過前N筆資料
     */
    public List<EContractTemplateQueryResponse> searchPage(EContractTemplateCriteria queryRequest, Integer limit, Integer offset) {

        List<EContractTemplateQueryResponse> responses = eContractTemplateRepository.findBySearch(queryRequest, limit, offset)
            .stream()
            .map(o -> {
                EContractTemplateQueryResponse response = new EContractTemplateQueryResponse(
                    (EContractTemplate) Optional.ofNullable(o[0]).get(),
                    (CarBrand) Optional.ofNullable(o[1]).orElseGet(CarBrand::new),
                    queryRequest.getIsEnabled()
                );
                return response;
            }).collect(Collectors.toList());

        responses.forEach(o -> {
            MemberInfo createMemberInfo = new MemberInfo();
            MemberInfo updateMemberInfo = new MemberInfo();
            if (StringUtils.isNotBlank(o.getCreateMemberId())) {
                createMemberInfo = authorityServer.getMemberInfos(o.getCreateMemberId()).stream().findAny().orElse(null);
            }
            if (StringUtils.isNotBlank(o.getUpdateMemberId())) {
                updateMemberInfo = authorityServer.getMemberInfos(o.getUpdateMemberId()).stream().findAny().orElse(null);
            }
            if (Objects.nonNull(createMemberInfo)) {
                o.setCreateUser(Optional.ofNullable(createMemberInfo.getMemberName()).orElse(o.getCreateMemberId()));
            }
            if (Objects.nonNull(updateMemberInfo)) {
                o.setUpdateUser(Optional.ofNullable(updateMemberInfo.getMemberName()).orElse(o.getUpdateMemberId()));
            }
        });

        if (queryRequest.getIsEnabled() == null) {
            Map<String, Optional<EContractTemplateQueryResponse>> latestEnabledECTs = responses.stream()
                .filter(o -> o.getEnableDate() != null && o.getEnableDate().isBefore(Instant.now()))
                .collect(Collectors.groupingBy(
                        EContractTemplateQueryResponse::getTemplateCode,
                        Collectors.maxBy(Comparator.comparing(EContractTemplateQueryResponse::getEnableDate))
                    )
                );
            latestEnabledECTs.values().stream()
                .filter(Optional::isPresent)
                .map(Optional::get)
                .forEach(o -> o.setEnabled(true));
        }

        return responses;
    }

    /**
     * 匯出電子合約範本CSV
     *
     * @param queryRequest 查詢相關參數
     */
    public CsvUtil.ByteArrayOutputStream2ByteBuffer generateEContractTemplateCsv(EContractTemplateCriteria queryRequest) {

        List<EContractTemplateCSV> responses = eContractTemplateRepository.findBySearch(queryRequest, null, null)
            .stream()
            .map(o -> {
                EContractTemplateCSV csv = new EContractTemplateCSV(
                    (EContractTemplate) Optional.ofNullable(o[0]).get(),
                    (CarBrand) Optional.ofNullable(o[1]).orElseGet(CarBrand::new),
                    (CarModel) Optional.ofNullable(o[2]).orElseGet(CarModel::new)
                );

                return csv;
            }).collect(Collectors.toList());

        responses.forEach(o -> {
            MemberInfo memberInfo = new MemberInfo();
            if (StringUtils.isNotBlank(o.getUpdateMemberId())) {
                memberInfo = authorityServer.getMemberInfos(o.getUpdateMemberId()).stream().findAny().orElse(null);
            }
            if (Objects.nonNull(memberInfo)) {
                o.setUpdateUser(Optional.ofNullable(memberInfo.getMemberName()).orElse(o.getUpdateMemberId()));
            }
        });

        CsvUtil.ByteArrayOutputStream2ByteBuffer out = new CsvUtil.ByteArrayOutputStream2ByteBuffer();
        CsvUtil.createCsv(
            responses,
            new String[] {"範本代碼", "版本號", "範本名稱-營業", "範本名稱-官網", "訂單來源",
                "訂閱租期", "車輛所屬", "車輛廠牌", "車型", "訂閱類別", "車輛牌價起始", "車輛牌價結束", "是否冰宇車", "是否有免責費用",
                "啟用時間", "修訂說明", "更新人員編號", "更新人員", "更新日期時間"},
            true,
            ',',
            out,
            Charset.forName("big5"),
            EContractTemplateCSV.class
        );
        return out;
    }

    /**
     * 匯出電子合約範本PDF
     */
    public GcsUrlRes generateEContractPdf(EContractPdfReq queryRequest, Integer acctId) {
        // 取得合約範本資料
        EContractTemplate eContractTemplate = eContractTemplateRepository.findById(queryRequest.getTemplateId()).orElse(null);
        if (eContractTemplate == null) {
            throw new SubscribeException(SubscribeHttpExceptionCode.ECONTRACT_TEMPLATE_NOT_EXISTS);
        }

        return gcsService.getDownloadPresigned(acctId, UploadFileKindEnum.CONTRACT_TEMPLATE, eContractTemplate.getUploadFileId());
    }

    @NonNull
    private Integer getAcctId(EContractReferencable econtractRefEntity) {
        return Optional.ofNullable(econtractRefEntity.getAcctId())
            .orElse(authServer.getAuthUser(econtractRefEntity).getAcctId());
    }

    public EContractReferencable fetchEContractRefEntity(String econtractRefEntityNo) {
        return eContractRefEntityFetcher.fetch(econtractRefEntityNo);
    }

    /**
     * 生成簽署的電子合約
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void eContractSigning(Integer acctId, Integer templateFileId, Integer signFileId, String econtractRefEntityNo) {
        try {
            // 查驗身份證
            AuthUser authUser = authServer.getUser(acctId);
            if (StringUtils.isEmpty(authUser.getLoginId())) {
                throw new SubscribeException(SubscribeHttpExceptionCode.PROFILE_INCOMPLETE);
            }

            EContractReferencable eContractRefEntity = fetchEContractRefEntity(econtractRefEntityNo);

            // 取得合約PDF
            EContractTemplate eContractTemplate = eContractTemplateRepository.findById(templateFileId).orElse(null);
            if (eContractTemplate == null) {
                throw new SubscribeException(SubscribeHttpExceptionCode.ECONTRACT_TEMPLATE_NOT_EXISTS);
            }
            UploadFile templateFile = uploadFileRepository.findById(eContractTemplate.getUploadFileId()).orElse(null);
            if (templateFile == null) {
                throw new SubscribeException(SubscribeHttpExceptionCode.ECONTRACT_TEMPLATE_FILE_NOT_EXISTS);
            }

            GcsGetDownloadUrlReq pdfDownloadReq = GcsGetDownloadUrlReq.builder()
                .source(HeaderDefine.SystemKind.SUB)
                .filePath(EContractConstant.GCS_CONTRACT_TEMPLATE_PATH)
                .isTemp(Boolean.FALSE)
                .fileNames(Collections.singletonList(templateFile.getFilename()))
                .build();

            GcsUrlRes fileInfoPdf = goSmartServer.getGcsDownloadUrl(pdfDownloadReq);
            if (fileInfoPdf.getSignedUrls().isEmpty()) {
                throw new SubscribeException(SubscribeHttpExceptionCode.GENERATE_DOWNLOAD_PRESIGNED_FAILED);
            }

            // 取得簽名檔
            UploadFile signFile = uploadFileRepository.findById(signFileId).orElse(null);
            if (signFile == null) {
                throw new SubscribeException(SubscribeHttpExceptionCode.SIGN_FILE_NOT_EXISTS);
            }

            GcsGetDownloadUrlReq imgDownloadReq = GcsGetDownloadUrlReq.builder()
                .source(HeaderDefine.SystemKind.SUB)
                .filePath(String.format(EContractConstant.GCS_SING_IMG_PATH, acctId))
                .isTemp(Boolean.TRUE)
                .fileNames(Collections.singletonList(signFile.getFilename()))
                .build();

            GcsUrlRes fileInfoImg = goSmartServer.getGcsDownloadUrl(imgDownloadReq);
            if (fileInfoImg.getSignedUrls().isEmpty()) {
                throw new SubscribeException(SubscribeHttpExceptionCode.ECONTRACT_SIGN_NOT_EXISTS);
            }

            // 將公版合約與簽名圖檔合併，並插入合約編號、承租人身分證字號和簽章日期，產生個人合約
            InputStream inputStream = new URL(fileInfoPdf.getSignedUrls().get(0).getSignedUrl()).openStream();
            PdfReader reader = new PdfReader(inputStream);
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            PdfStamper stamper = new PdfStamper(reader, byteArrayOutputStream);
            // 設定密碼為身份証
            stamper.setEncryption(authUser.getLoginId().toUpperCase(Locale.ROOT).getBytes(StandardCharsets.UTF_8), EContractConstant.PDF_ADMIN_SECRET.getBytes(StandardCharsets.UTF_8),
                PdfWriter.ALLOW_DEGRADED_PRINTING | PdfWriter.ALLOW_SCREENREADERS, PdfWriter.ENCRYPTION_AES_128);
            // 插入承租人簽名圖檔
            Image image = Image.getInstance(new URL(fileInfoImg.getSignedUrls().get(0).getSignedUrl()));
            PdfContentByte under = stamper.getOverContent(reader.getNumberOfPages());
            image.scaleToFit(PDFConstant.SignatureWidth.floatValue(), PDFConstant.SignatureHeight.floatValue());
            float signatureY = PDFConstant.PageHeight.floatValue() - PDFConstant.Y.floatValue() - PDFConstant.SignatureHeight.floatValue();
            image.setAbsolutePosition(PDFConstant.X.floatValue() + PDFConstant.SignatureOffsetX.floatValue(), signatureY);
            under.addImage(image);

            // 使用原生BaseFont字體無法印出中文
            BaseFont baseFont = getJhengHeiFont();
            // 插入合約編號
            stampContractNoOnPdf(econtractRefEntityNo, reader, stamper, baseFont);

            under.beginText();
            under.setFontAndSize(baseFont, 12);
            float loginIdAndSignDateY = signatureY + PDFConstant.CompensationHeight.floatValue();
            under.setTextMatrix(PDFConstant.loginIdX.floatValue(), loginIdAndSignDateY);
            // 插入承租人身份證字號
            under.showText(authUser.getLoginId());

            Instant eContractSignDate = Instant.now();
            under.setTextMatrix(PDFConstant.eContractSignDateX.floatValue(), loginIdAndSignDateY);
            // 插入承租人簽章日期
            under.showText(DateUtils.toDateString(Date.from(eContractSignDate), "yyyy/MM/dd"));
            under.endText();

            stamper.close();
            reader.close();

            // 取得上傳憑証
            UploadFilePresignedRes gcsUploadUrl = gcsService.getUploadPresigned(acctId, UploadFileKindEnum.CUSTOM_CONTRACT, false, eContractTemplate.getTemplateNameCust(), acctId.toString());

            // 上傳合約至GCS
            GoSmartServer.putFileToGCS(byteArrayOutputStream.toByteArray(), gcsUploadUrl.getSignedUrl(), "application/pdf");

            List<Integer> uploadFileId = new ArrayList<>();
            uploadFileId.add(gcsUploadUrl.getId());

            //儲存電子合約
            EContract eContract = new EContract();
            eContract.setCreateUser(acctId.toString());
            eContract.setUpdateUser(acctId.toString());
            eContract.setEContractSignDate(eContractSignDate);
            eContract.setUploadFileId(uploadFileId);
            // EC Type 來源官網
            eContract.setEContractType(E_CONTRACT.toString());
            eContract.setAcctId(acctId);
            eContract.setContractNo(econtractRefEntityNo);
            eContractRepository.save(eContract);

            // 標記檔案已使用
            UploadFile uploadFile = uploadFileRepository.findById(gcsUploadUrl.getId()).orElse(new UploadFile());
            uploadFile.setStatus(1);
            uploadFile.setDisplayFilename(E_CONTRACT.getDescription() + "_" + econtractRefEntityNo);
            uploadFileRepository.save(uploadFile);
            EContractRefEntityUpdater updater = eContractRefEntityUpdaterFactory.getUpdater(econtractRefEntityNo);
            updater.updateEContractId(eContractRefEntity, eContract.getEContractId());
        } catch (SubscribeException e) {
            throw e;
        } catch (Exception ex) {
            log.error("[eContract] Sign Failed:", ex);
            throw new SubscribeException(SubscribeHttpExceptionCode.ECONTRACT_SIGN_ERROR);
        }
    }

    /**
     * Cond-1: 如 維運App / 官網 Client未簽名，則無建立e_contract <br/>
     * 此時後台手動更新關聯 contract.eContractTempVerId，contract.eContractId 不需更新(簽名為線下紙本作業) <br/>
     * Cond-2: 如有提供簽名，則e_contract, contract.eContractTempVerId, contract.eContractId皆有建立 <br/>
     * 此時後台不提供更新範本ID
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void updateContractTemplate(String econtractRefEntityNo, Integer newTemplateId) {

        Map<String, List<EContract>> ecsByType = eContractRepository.getEContractsByEContractRefEntityNo(econtractRefEntityNo).stream()
            .collect(Collectors.groupingBy(EContract::getEContractType));

        EContractTemplate newTemplate = eContractTemplateRepository.findById(newTemplateId)
            .orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.ECONTRACT_TEMPLATE_NOT_EXISTS));

        List<EContract> esByType = ecsByType.getOrDefault(E_CONTRACT.name(), new ArrayList<>());

        EContractRefEntityUpdater updater = eContractRefEntityUpdaterFactory.getUpdater(econtractRefEntityNo);
        updater.updateEContractTemplateId(econtractRefEntityNo, newTemplate, esByType);
    }

    public List<EContract> getEContracts(String econtractRefEntityNo, List<String> types) {
        return eContractRepository.getEContractByRefEntityNoAndTypes(econtractRefEntityNo, types);
    }

    /**
     * 取得電子合約
     */
    public List<EContract> getEContracts(String econtractRefEntityNo) {
        return eContractRepository.getEContractsByEContractRefEntityNo(econtractRefEntityNo);
    }

    public List<EContract> getRentalTaskEContractByEContractRefEntityNo(String econtractRefEntityNo) {
        return eContractRepository.getRentalTaskEContractByRefEntityNo(econtractRefEntityNo);
    }

    /**
     * 寄送郵件夾帶電子合約
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void sendContractMail(Integer acctId, String econtractRefEntityNo, List<Integer> fileIds, String memberId) {

        // 取得合約
        List<EContract> eContracts = getEContracts(econtractRefEntityNo);
        if (eContracts == null || !acctId.equals(eContracts.get(0).getAcctId())) {
            throw new SubscribeException(SubscribeHttpExceptionCode.ECONTRACT_UNAUTHORIZED);
        }


        sendContractMail(acctId, fileIds, memberId, eContracts);
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void sendContractMail(Integer acctId, List<Integer> fileIds, String memberId, List<EContract> ecs) {
        List<MemberInfo> memberInfos = authorityServer.getMemberInfos(memberId);
        if (memberInfos.isEmpty()) {
            throw new SubscribeException(SubscribeHttpExceptionCode.ECONTRACT_UNAUTHORIZED);
        }

        // 查驗身份証
        AuthUser authUser = authServer.getUser(acctId);
        if (authUser.getLoginId().isEmpty()) {
            throw new SubscribeException(SubscribeHttpExceptionCode.PROFILE_INCOMPLETE);
        }

        // 處理附件
        List<MailAttachment> attachments = new ArrayList<>();
        Map<String, String> downloadPreSignedUrls = new HashMap<>();
        for (EContract ec : ecs) {
            ec.getUploadFileId().forEach(fileId -> {
                if (fileIds != null && !fileIds.isEmpty() && !fileIds.contains(fileId)) {
                    return;
                }
                UploadFile uploadFile = uploadFileRepository.findById(fileId).orElse(null);
                if (uploadFile != null) {
                    // 成功取得Presigned才寄信 -- 寄信是個佇列,有時間差,生成24小時效期的連結供寄信時下載
                    GcsUrlRes gcsUrlRes = gcsService.getDownloadPresigned(acctId, UploadFileKindEnum.CUSTOM_CONTRACT, uploadFile.getId(), Math.toIntExact(Duration.ofHours(24).toMinutes()));
                    if (gcsUrlRes.getSignedUrls().isEmpty()) {
                        throw new SubscribeException(FILE_NOT_EXISTS);
                    }

                    downloadPreSignedUrls.put(gcsUrlRes.getSignedUrls().get(0).getSignedUrl(), uploadFile.getDisplayFilename());

                    attachments.add(
                        MailAttachment.builder()
                            .mediaType(gcsUrlRes.getSignedUrls().get(0).getMediaType())
                            .filename(uploadFile.getDisplayFilename())
                            .fileUrl(gcsUrlRes.getSignedUrls().get(0).getSignedUrl())
                            .build()
                    );
                }
            });
        }
        String contractNo = ecs.get(0).getContractNo();
        Orders order = orderService.getOrdersByContractNo(contractNo).get(0);
        notifyToCService.notifyContractManualReSend(order, authUser, downloadPreSignedUrls);

        createSendECMailHistory(acctId, memberId, attachments, authUser, memberInfos, contractNo);
    }

    /**
     * 紀錄當下寄送EC Files 所有附件資訊
     */
    private void createSendECMailHistory(Integer acctId, String memberId, List<MailAttachment> attachments,
                                         AuthUser authUser, List<MemberInfo> memberInfos, String contractNo) {
        // 記錄郵件發送歷程
        EContractMailHistory eContractMailHistory = new EContractMailHistory();
        // URL有時效。就不存了
        attachments.forEach(item -> item.setFileUrl(null));
        eContractMailHistory.setAttachments(attachments);
        eContractMailHistory.setReceiver(authUser.getEmail());
        eContractMailHistory.setReceiveAcctId(acctId);
        eContractMailHistory.setTargetMember(memberId);
        eContractMailHistory.setTargetMember(memberInfos.get(0).getMemberName());
        eContractMailHistory.setContractNo(contractNo);
        eContractMailHistoryRepository.save(eContractMailHistory);
    }

    /**
     * 查詢郵件寄送歷程
     */
    public List<EContractMailHistory> getMailHistory(String econtractRefEntityNo) {
        List<EContract> eContracts = getEContracts(econtractRefEntityNo);
        if (CollectionUtils.isEmpty(eContracts)) {
            throw new SubscribeException(SubscribeHttpExceptionCode.ECONTRACT_NOT_FOUND);
        }
        return eContractMailHistoryRepository.getByContractId(econtractRefEntityNo);
    }

    /**
     * 新增附件 : OTHER Type - e_contracts.uploadFileId 只有一個 file id
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void addContractAttachment(EContractFileUpdateReq req, String econtractRefEntityNo, String memberId) {
        //查驗會員是否有效
        AuthUser authUser = authServer.getUser(req.getAcctId());
        if (StringUtils.isEmpty(authUser.getLoginId())) {
            throw new SubscribeException(SubscribeHttpExceptionCode.PROFILE_INCOMPLETE);
        }

        UploadFile uploadFile = uploadFileRepository.findById(req.getUploadFileId()).orElse(null);
        if (uploadFile == null) {
            throw new SubscribeException(FILE_NOT_EXISTS);
        }

        GcsGetDownloadUrlReq tmpFileDownloadReq = GcsGetDownloadUrlReq.builder()
            .source(HeaderDefine.SystemKind.SUB)
            .filePath(String.format(EContractConstant.GCS_CUSTOM_CONTRACT_PATH, req.getAcctId().toString()))
            .isTemp(Boolean.TRUE)
            .fileNames(Collections.singletonList(uploadFile.getFilename()))
            .build();

        GcsUrlRes gcsUrlres = goSmartServer.getGcsDownloadUrl(tmpFileDownloadReq);
        if (gcsUrlres.getSignedUrls().isEmpty()) {
            throw new SubscribeException(SubscribeHttpExceptionCode.GENERATE_DOWNLOAD_PRESIGNED_FAILED);
        }

        try {
            // 下載檔案搬到GCS正在區
            InputStream inputStream = new URL(gcsUrlres.getSignedUrls().get(0).getSignedUrl()).openStream();
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            PdfReader reader = new PdfReader(inputStream);
            PdfStamper stamper = new PdfStamper(reader, byteArrayOutputStream);
            // 設定密碼為身份証
            stamper.setEncryption(authUser.getLoginId().toUpperCase(Locale.ROOT).getBytes(StandardCharsets.UTF_8), EContractConstant.PDF_ADMIN_SECRET.getBytes(StandardCharsets.UTF_8),
                PdfWriter.ALLOW_DEGRADED_PRINTING | PdfWriter.ALLOW_SCREENREADERS, PdfWriter.ENCRYPTION_AES_128);

            // 使用原生BaseFont字體無法印出中文
            BaseFont baseFont = getJhengHeiFont();
            // 在每一頁插入合約編號
            stampContractNoOnPdf(econtractRefEntityNo, reader, stamper, baseFont);
            stamper.close();
            reader.close();
            // 取得上傳憑証
            UploadFilePresignedRes gcsUploadUrl = gcsService.getUploadPresigned(req.getAcctId(),
                UploadFileKindEnum.CUSTOM_CONTRACT, false, req.getDisplayFilename(), memberId);

            // 上傳合約至GCS
            GoSmartServer.putFileToGCS(byteArrayOutputStream.toByteArray(), gcsUploadUrl.getSignedUrl(), "application/pdf");

            // 修改檔案使用狀態
            uploadFile.setStatus(0);
            uploadFileRepository.save(uploadFile);

            UploadFile newFile = uploadFileRepository.findById(gcsUploadUrl.getId()).orElse(uploadFile);
            newFile.setStatus(1);
            newFile.setRemark(req.getRemark());
            uploadFileRepository.save(newFile);
            // 建立 EC(status 1 Upload File)
            EContract eContract = new EContract();
            eContract.setAcctId(req.getAcctId());
            eContract.setContractNo(econtractRefEntityNo);
            eContract.setEContractType(EContractType.OTHER.name());
            ArrayList<Integer> uploadFileIds = new ArrayList<>();
            uploadFileIds.add(gcsUploadUrl.getId());
            eContract.setUploadFileId(uploadFileIds);
            eContract.setCreateUser(memberId);
            eContract.setUpdateUser(memberId);
            eContract.setERentalSignDate(Instant.now());
            eContractRepository.save(eContract);

        } catch (MalformedURLException e) {
            log.error("[DOWNLOAD FILE] FAILED. ", e);
            throw new SubscribeException(FILE_NOT_EXISTS);
        } catch (Exception e) {
            log.error("[UPLOAD FILE] FAILED. ", e);
            throw new SubscribeException(SubscribeHttpExceptionCode.CANNOT_ADD_ECONTRACT_FILE);
        }
    }

    private BaseFont getJhengHeiFont() throws DocumentException, IOException {
        return BaseFont.createFont("fonts/微軟正黑體/微軟正黑體.ttf", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
    }

    public void stampContractNoOnPdf(String econtractRefEntityNo, PdfReader reader, PdfStamper stamper, BaseFont baseFont) {
        for (int pageNum = 1; pageNum <= reader.getNumberOfPages(); pageNum++) {
            PdfContentByte content = stamper.getOverContent(pageNum);
            content.beginText();
            content.setFontAndSize(baseFont, 8);
            // 計算實際座標
            float x = (float) (PDFConstant.PageWidth * PDFConstant.ContractNoRelativeX);
            float y = (float) (PDFConstant.PageHeight * PDFConstant.ContractNoRelativeY);
            // 設定文字位置(x, y)
            content.setTextMatrix(x, y);
            content.showText("合約編號：" + econtractRefEntityNo);
            content.endText();
        }
    }

    /**
     * 刪除附件
     */
    @Transactional
    public void removeContractAttachment(String econtractRefEntityNo, Integer fileId, String memberId) {
        // 用合約編號找出對應的電子合約記錄
        List<EContract> ecs = getEContracts(econtractRefEntityNo, EContractType.toNamelist());

        if (ecs.isEmpty()) {
            throw new SubscribeException(SubscribeHttpExceptionCode.ECONTRACT_NOT_FOUND);
        }

        // 註銷檔案使用
        UploadFile uploadFile = uploadFileRepository.findById(fileId).orElseThrow(() -> new SubscribeException(FILE_NOT_EXISTS));
        uploadFile.setStatus(2);
        uploadFile.setUpdateUser(memberId);
        uploadFileRepository.save(uploadFile);

        //todo: GCS 刪檔
    }

    /**
     * 取得電子合約範本下載聯結
     */
    public GcsUrlRes getEContractTemplateDownloadPresigned(Integer templateId) {
        EContractTemplate eContractTemplate = eContractTemplateRepository.findById(templateId).orElse(null);
        if (eContractTemplate == null || eContractTemplate.getUploadFileId() == null) {
            throw new SubscribeException(FILE_NOT_EXISTS);
        }
        return gcsService.getDownloadPresigned(null, UploadFileKindEnum.CONTRACT_TEMPLATE, eContractTemplate.getUploadFileId());
    }

    /**
     * 取得該合約下的有效附件
     */
    public List<UploadFile> getEContractFiles(String econtractRefEntityNo) {
        List<UploadFile> uploadFiles = new ArrayList<>();
        List<EContract> ecs = getEContracts(econtractRefEntityNo, EContractType.toNamelist());
        List<Integer> ecFiles = ContractUtils.getECFiles(ecs);
        if (!ecs.isEmpty() && !ecFiles.isEmpty()) {
            uploadFiles = uploadFileRepository.getByMultiId(ecFiles);
        }
        return uploadFiles;
    }

    public Map<Integer, UploadFile> getEContractFiles(EContract ec) {
        Map<Integer, UploadFile> uploadFileMap = new HashMap<>();
        if (ec != null && !ec.getUploadFileId().isEmpty()) {
            uploadFileMap = uploadFileRepository.getByMultiId(ec.getUploadFileId()).stream()
                .collect(Collectors.toMap(UploadFile::getId, Function.identity()));
        }
        return uploadFileMap;
    }

    private List<EContract> fetchEContractsByMainContractOrDealerOrderNo(String mainContractOrDealerOrderNo) {
        return mainContractOrDealerOrderNo.startsWith("U")
            ? eContractRefEntityFetcher.getContracts(mainContractOrDealerOrderNo)
            .stream()
            .map(Contract::getContractNo)
            .map(this::getEContracts)
            .flatMap(List::stream)
            .collect(Collectors.toList())
            : getEContracts(mainContractOrDealerOrderNo);
    }

    /**
     * 取得主約所有合約下的有效附件
     */
    public List<ECFilesResponse> getMainContractFiles(String mainContractOrDealerOrderNo) {
        return fetchEContractsByMainContractOrDealerOrderNo(mainContractOrDealerOrderNo).stream()
            .map(this::buildECFilesResponse)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    private ECFilesResponse buildECFilesResponse(EContract ec) {
        Map<String, MemberInfo> memberMap = new HashMap<>();

        return uploadFileRepository.getById(ec.getUploadFileId().get(0), 1)
            .map(file -> {
                String createName = resolveName(file.getCreateUser(), memberMap);
                String updateName = resolveName(file.getUpdateUser(), memberMap);

                return ECFilesResponse.builder()
                    .contractNo(ec.getContractNo())
                    .eContractId(ec.getEContractId())
                    .eContractType(ec.getEContractType())
                    .uploadFile(new UploadFileResponse(file, createName, updateName))
                    .eRentalSignDate(ec.getERentalSignDate())
                    .eRentalSignInfo(ec.getERentalSignInfo())
                    .departTaskId(ec.getDepartTaskId())
                    .returnTaskId(ec.getReturnTaskId())
                    .createUser(resolveName(ec.getCreateUser(), memberMap))
                    .createDate(ec.getInstantCreateDate())
                    .build();
            })
            .orElse(null);
    }

    /**
     * 判斷ID為員工/Acct，決定中文姓名，如中文姓名為空，則顯示ID
     */
    private String resolveName(String userId, Map<String, MemberInfo> memberMap) {
        if (userId == null) {
            return "";
        }

        return userId.toUpperCase().startsWith("K")
            ? Optional.ofNullable(getMember(userId, memberMap))
            .map(MemberInfo::getMemberName)
            .filter(name -> !name.isEmpty())
            .orElse(userId)
            : Optional.of(userId)
            .map(Integer::parseInt)
            .map(authServer::getUser)
            .map(AuthUser::getAcctName)
            .filter(name -> !name.isEmpty())
            .orElse(userId);
    }

    /**
     * 更新電子合約 - 上傳檔案
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public UploadFileResponse updateEContractUploadFile(String econtractRefEntityNo, EContractRequest request, String memberId) {
        UploadFile file = uploadFileRepository.getById(request.getUploadFileId(), 1)
            .orElseThrow(() -> new SubscribeException(FILE_NOT_EXISTS));

        if (request.getDisplayFilename() != null) {
            file.setDisplayFilename(request.getDisplayFilename());
        }
        if (request.getUploadFileRemark() != null) {
            file.setRemark(request.getUploadFileRemark());
        }

        file.setUpdateUser(memberId);
        uploadFileRepository.save(file);
        List<MemberInfo> memberInfos = authorityServer.getMemberInfos(memberId);
        String updateUser = memberInfos.isEmpty() ? file.getUpdateUser() : memberInfos.get(0).getMemberName();
        return new UploadFileResponse(file, updateUser);
    }

    private MemberInfo getMember(String memberId, Map<String, MemberInfo> userMap) {
        MemberInfo user = userMap.get(memberId);
        if (user == null) {
            Optional<MemberInfo> optional = authorityServer.getMemberInfos(memberId).stream().findAny();
            if (optional.isPresent()) {
                user = optional.get();
                userMap.put(memberId, user);
            }
        }
        return user;
    }

    /**
     * 合約 GOING + 出車 : 寄信 <br/>
     * 主約 GOING -> COMPLETE + 正常還車 50 -> 90 : 寄信 <br/>
     * 主約 維持 GOING + 車損還車 50 -> 80 : 寄信 (車損確認結案不寄信) <br/>
     */
    @Async
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void asyncGenRentalTaskPdfSendECFilesMail(Orders order, Contract contract, MainContract mainContract, String memberId, TaskType taskType) {
        Integer acctId = contract.getMainContract().getAcctId();
        String contractNo = contract.getContractNo();
        boolean hasAccident = OrderUtils.hasAccident(order);
        boolean isDepartOffCar = isStatusEqual(contract.getStatus(), ContractStatus.GOING) && order.getStatus().equals(DEPART.getStatus());
        boolean isDropOffCar = isStatusEqual(mainContract.getStatus(), ContractStatus.COMPLETE) && order.getStatus().equals(CLOSE.getStatus()) && !hasAccident;
        boolean hasAccidentDropOffCar = isStatusEqual(mainContract.getStatus(), ContractStatus.GOING) && order.getStatus().equals(ARRIVE_NO_CLOSE.getStatus()) && hasAccident;
        List<MemberInfo> memberInfos = authorityServer.getMemberInfos(memberId);

        if (isDepartOffCar || isDropOffCar || hasAccidentDropOffCar) {
            if (isERentalPdf(contract, taskType)) {
                Integer gcsUploadUrlId = sendAndGenRentalTaskPdfAndUpLoad(contract, memberId, taskType);
                saveFileRecordToDB(contract, gcsUploadUrlId, String.valueOf(acctId), taskType);
            }

            AuthUser user = authServer.getUser(acctId);
            List<MailAttachment> attachments = new ArrayList<>();
            if (isDepartOffCar) {
                attachments = notifyToCService.notifyEContractFiles(order, contractNo, user, ContractStatus.GOING);
            }

            if (isDropOffCar || hasAccidentDropOffCar) {
                attachments = notifyToCService.notifyEContractFiles(order, contractNo, user, ContractStatus.COMPLETE);
            }

            if (!attachments.isEmpty()) {
                createSendECMailHistory(acctId, memberId, attachments, user, memberInfos, contractNo);
            }
        }
    }

    @Async
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void reSendAndReGenRentalTaskPdfAndUpLoad(String econtractRefEntityNo, String memberId, TaskType taskType) {

        EContractReferencable econtractRefEntity = fetchEContractRefEntity(econtractRefEntityNo);

        Integer gcsUploadUrlId = null;
        EContract eContract = null;
        if (isERentalPdf(econtractRefEntity, taskType)) {
            gcsUploadUrlId = sendAndGenRentalTaskPdfAndUpLoad(econtractRefEntity, memberId, taskType);
            eContract = saveFileRecordToDB(econtractRefEntity, gcsUploadUrlId, String.valueOf(getAcctId(econtractRefEntity)), taskType);
        }

        // 人工寄送
        if (Objects.nonNull(gcsUploadUrlId) && eContract != null) {
            sendContractMail(getAcctId(econtractRefEntity), Collections.singletonList(gcsUploadUrlId), memberId, Collections.singletonList(eContract));
        }
    }

    @Transactional(transactionManager = "mysqlTransactionManager", propagation = Propagation.REQUIRES_NEW)
    public Integer sendAndGenRentalTaskPdfAndUpLoad(EContractReferencable econtractRefEntity, String memberId, TaskType taskType) {
        try {
            log.info("generate e_rental pdf");
            AuthUser authUser = authServer.getAuthUser(econtractRefEntity);
            byte[] pdf = rentalTaskService.generatePdf(econtractRefEntity, authUser);
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            ByteArrayInputStream inputStream = new ByteArrayInputStream(pdf);
            PdfReader reader = new PdfReader(inputStream);
            PdfStamper stamper = new PdfStamper(reader, byteArrayOutputStream);
            stamper.setEncryption(
                authUser.getLoginId().toUpperCase(Locale.ROOT).getBytes(StandardCharsets.UTF_8),
                EContractConstant.PDF_ADMIN_SECRET.getBytes(StandardCharsets.UTF_8),
                PdfWriter.ALLOW_DEGRADED_PRINTING | PdfWriter.ALLOW_SCREENREADERS, PdfWriter.ENCRYPTION_AES_128
            );
            stamper.close();

            // 呼叫 goSmart 服務取得上傳 url
            UploadFilePresignedRes gcsUploadUrl = gcsService.getUploadPresigned(getAcctId(econtractRefEntity),
                UploadFileKindEnum.CUSTOM_CONTRACT, false,
                String.format("%s_%s_%s", taskType.getFileName(), econtractRefEntity.getEntityNo(), econtractRefEntity.getPlateNo()), memberId);
            HttpRequestUtils.upload(gcsUploadUrl.getSignedUrl(), byteArrayOutputStream.toByteArray(), gcsUploadUrl.getMediaType());

            byteArrayOutputStream.close();
            inputStream.close();

            return gcsUploadUrl.getId();
        } catch (Exception e) {
            log.error("產生租車任務PDF，並寄送失敗", e);
        }

        return 0;
    }


    /**
     * 是否產生電子出租單
     */
    private boolean isERentalPdf(EContractReferencable econtractRefEntity, TaskType taskType) {

        String entityNo = econtractRefEntity.getEntityNo();

        String departTaskId = econtractRefEntity.getDepartTaskId();
        String returnTaskId = econtractRefEntity.getReturnTaskId();

        // 如果沒有出車/還車任務ID，直接返回false
        if (StringUtils.isBlank(departTaskId) && taskType.equals(TaskType.DEPART)) {
            log.info("no depart task id, no need generate e_rental pdf");
            return false;
        }

        if (StringUtils.isBlank(returnTaskId) && taskType.equals(TaskType.RETURN)) {
            log.info("no return task id, no need generate e_rental pdf");
            return false;
        }

        if (econtractRefEntity instanceof Contract) {
            // 獲取並篩選有效訂單
            List<Orders> validOrders = eContractRefEntityFetcher.getContractAndOrdersByContractNo(entityNo)
                .getOrders()
                .stream()
                .filter(orders -> orders.getStage() > 0)
                .sorted(Comparator.comparing(Orders::getStage))
                .collect(Collectors.toList());

            // 如果沒有有效訂單，返回false
            if (validOrders.isEmpty()) {
                return false;
            }

            // 單一訂單判斷
            if (validOrders.size() == 1) {
                OrderStatus status = OrderStatus.of(validOrders.get(0).getStatus());
                return status.departNeedGeneratePDF() || status.returnNeedGeneratePDF();
            }

            // 多訂單判斷
            return OrderStatus.of(validOrders.get(0).getStatus()).returnNeedGeneratePDF();
        } else {
            return econtractRefEntity.getStatus().equals(ContractStatus.COMPLETE.getCode());
        }
    }

    /**
     * 保存電子出租單(E_RENTAL)EContract到DB，出車/還車出租單分開儲存
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public EContract saveFileRecordToDB(EContractReferencable econtractRefEntity, Integer fileId, String acctId, TaskType taskType) {
        List<EContract> ecs = getRentalTaskEContractByEContractRefEntityNo(econtractRefEntity.getEntityNo());
        Map<TaskType, EContract> eRentalECMap = ecs.stream()
            .collect(Collectors.toMap(ContractUtils::ecToTaskType, Function.identity()));
        EContract ec = eRentalECMap.get(taskType);
        // 當已經存在出租單，則將原本出租單改為OTHER
        if (ec != null) {
            ec.setEContractType(EContractType.OTHER.name());
            eContractRepository.save(ec);
        }

        log.info("create {} e_contract", taskType);
        ec = new EContract();
        ec.setAcctId(getAcctId(econtractRefEntity));
        ec.setContractNo(econtractRefEntity.getEntityNo());
        ec.setEContractType(EContractType.E_RENTAL.name());
        ec.setUploadFileId(Collections.singletonList(fileId));
        ec.setERentalSignDate(Instant.now());
        ec.setCreateUser(acctId);
        ec.setUpdateUser(acctId);

        if (taskType.equals(TaskType.DEPART)) {
            ec.setDepartTaskId(econtractRefEntity.getDepartTaskId());
            return eContractRepository.save(ec);
        }

        if (taskType.equals(TaskType.RETURN)) {
            ec.setReturnTaskId(econtractRefEntity.getReturnTaskId());
            return eContractRepository.save(ec);
        }


        return ec;
    }


    /**
     * 是否簽約電子合約
     */
    public boolean isSignEContract(String econtractRefEntityNo) {
        EContractReferencable entity = fetchEContractRefEntity(econtractRefEntityNo);
        return isSignEContract(entity);
    }

    /**
     * 是否簽約電子合約
     */
    public boolean isSignEContract(EContractReferencable econtractRefEntity) {
        if (StringUtils.isNotBlank(econtractRefEntity.getEContractId())) {
            return true;
        }
        long availableCount = 0;
        List<EContract> econtractList = getEContracts(econtractRefEntity.getEntityNo(), Arrays.asList(EContractType.E_CONTRACT.name(), EContractType.OTHER.name()));
        // 檢查eontractList中的每個EContract的uploadFileId是否有狀態非2(刪除)的檔案
        for (EContract eContract : econtractList) {
            if (CollectionUtils.isNotEmpty(eContract.getUploadFileId())) {
                availableCount += uploadFileRepository.getByMultiId(eContract.getUploadFileId()).size();
            }
        }
        return availableCount > 0;
    }

    /**
     * 異動訂單時更新合約樣本版本
     */
    public void updateEContractBeforeSign(IOrder iorder, String carNo) {
        if (iorder instanceof DealerOrder) {
            DealerOrder dealerOrder = (DealerOrder) iorder;
            if (StringUtils.isBlank(dealerOrder.getEContractId())) {
                Integer templateId = getTemplateContractIdByCarNo(carNo, EContractSource.CARPLUS, dealerOrder.getSubscribeMonth(), dealerOrder.isDisclaimer());
                dealerOrder.setEContractTemplateId(templateId);
            }
        } else {
            Orders order = (Orders) iorder;
            if (StringUtils.isBlank(order.getContract().getEContractId())) {
                Integer templateId = getTemplateContractIdByCarNo(carNo, EContractSource.CARPLUS, order.getMonth(), Optional.ofNullable(order.getContract().getDisclaimer()).orElse(false));
                if (templateId != null) {
                    order.getContract().setEContractTempVerId(templateId.toString());
                } else {
                    order.getContract().setEContractTempVerId(null);
                }
            }
        }
    }
}
