package com.carplus.subscribe.enums.finbus;

import com.carplus.subscribe.enums.PriceInfoDefinition;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum TransactionItemCodeEnum {

    RENT, // 租金(訂閱用)
    ETAG, // eTag
    MARGIN, // 保證金
    RENT_SUBSCRIBE, // V1月租費
    MONTHLY_CHARGE, // 月租費
    TAPPAY_ADVANCE, // TapPay 預付款
    MERCHANDISE; // 汽車用品

    public static boolean isValid(String name) {
        try {
            TransactionItemCodeEnum.valueOf(name);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public static TransactionItemCodeEnum getTransactionItemCodeEnum(PriceInfoDefinition.PriceInfoCategory category) {

        switch (category) {
            case MonthlyFee:
                return MONTHLY_CHARGE;
            case Merchandise:
                return MERCHANDISE;
            case ETag:
                return ETAG;
            default:
                return RENT;
        }
    }

    public static boolean isNotValid(String name) {
        return !isValid(name);
    }
}
