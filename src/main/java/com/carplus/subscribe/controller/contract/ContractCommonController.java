package com.carplus.subscribe.controller.contract;

import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.model.priceinfo.resp.OrderPriceInfoResponse;
import com.carplus.subscribe.model.request.contract.CalculateRequest;
import com.carplus.subscribe.model.request.contract.InternalContractCreateReq;
import com.carplus.subscribe.model.request.priceinfo.FineDiscountAgreeRequest;
import com.carplus.subscribe.service.ContractLogic;
import com.carplus.subscribe.service.PriceInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@CarPlusRestController
@RequestMapping(HttpConstant.COMMON_URL)
@Tag(name = "Common 訂閱合約API")
public class ContractCommonController {

    @Autowired
    private ContractLogic contractLogic;
    @Autowired
    private PriceInfoService priceInfoService;

    @Operation(summary = "計價")
    @PostMapping("subscribe/calculate")
    public List<OrderPriceInfoResponse> calculatePrice(@Validated @RequestBody CalculateRequest request) {
        InternalContractCreateReq contractCreateReq = new InternalContractCreateReq();
        org.springframework.beans.BeanUtils.copyProperties(request, contractCreateReq, "month");
        contractCreateReq.setMonth(request.getMonth().getValue());
        contractLogic.contractCreateRequestValidate(contractCreateReq, false);
        return priceInfoService.calculateOrderPrice(request);
    }

    @Operation(summary = "主管審核請求折扣")
    @PatchMapping(value = "subscribe/{uid}/decide", produces = MediaType.APPLICATION_JSON_VALUE)
    public void decideDiscountEtag(@PathVariable("uid") String uid,
                                   @RequestBody @Validated FineDiscountAgreeRequest request) {
        priceInfoService.decideDiscount(uid, null, request);
    }

    @Operation(summary = "取得可預約日期")
    @GetMapping("subscribe/availableDate")
    public List<Date> getAvailableDate(@RequestParam String carNo, @RequestParam(defaultValue = "6") Integer days, @RequestParam String stationCode) {
        return contractLogic.getSubscribeAvailableDate(carNo, days, stationCode);
    }
}
