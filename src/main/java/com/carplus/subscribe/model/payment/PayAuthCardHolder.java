package com.carplus.subscribe.model.payment;

import com.carplus.subscribe.model.auth.AuthUser;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.lang.NonNull;

@Data
public class PayAuthCardHolder {

    @Schema(description = "手機號碼")
    private String phoneNumber;
    @Schema(description = "姓名")
    private String custName;
    @Schema(description = "信箱")
    private String email;
    @Schema(description = "持卡人或購買人會員編號")
    private String memberId;

    public PayAuthCardHolder(@NonNull AuthUser user) {
        this.phoneNumber = user.getMainCell();
        this.custName = user.getAcctName();
        this.email = user.getEmail();
        this.memberId = String.valueOf(user.getAcctId());
    }
}
