package com.carplus.subscribe.listener;

import carplus.common.enums.HeaderDefine;
import com.carplus.subscribe.enums.BuIdEnum;
import com.carplus.subscribe.event.car.CarDepartEvent;
import com.carplus.subscribe.event.car.CarReplaceEvent;
import com.carplus.subscribe.event.car.CarReturnEvent;
import com.carplus.subscribe.feign.CrsClient;
import com.carplus.subscribe.model.crs.CarBaseUpdateRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.carplus.subscribe.constant.CarPlusConstant.PLATE_FORM_CODE;

@Slf4j
@Component
public class CarEventListener {

    @Autowired
    private CrsClient crsClient;

    @Async
    @EventListener
    public void handleCarDepartEvent(CarDepartEvent event) {
        log.info("出車 - 長放車註冊事件, orderNo: {}", event.getOrderNo());

        // crsInfo 為 null 時，不發動事件處理，並記錄 carCrsInfo
        if (event.getCarCrsInfo() == null) {
            log.warn("出車 - 長放車註冊事件不處理, carCrsInfo: null");
            return;
        }

        // crsCarNo 為 null 時，不發動事件處理，並記錄 carCrsCarNo
        if (event.getCarCrsInfo().getCarNo() == null) {
            log.warn("出車 - 長放車註冊事件不處理, carCrsCarNo: null");
            return;
        }

        if (BuIdEnum.lRental.getCode().equals(event.getCarCrsInfo().getBuId())) {
            CarBaseUpdateRequest carBaseUpdateRequest = CarBaseUpdateRequest.builder()
                .carNo(event.getCarCrsInfo().getCarNo())
                .carBaseModifyQue("subscribe")
                .memo("訂閱起租註冊" + event.getOrderNo())
                .userId(event.getMemberId())
                .build();
            try {
                crsClient.batchUpdateCarBaseInfo(PLATE_FORM_CODE, HeaderDefine.SystemKind.SUB, Collections.singletonList(carBaseUpdateRequest));
            } catch (Exception e) {
                log.error("出車 - 長放車註冊事件失敗", e);
            }
        } else {
            log.warn("出車 - 車輛庫位非長租, 不進行長放車註冊事件, carBuId: {}", event.getCarCrsInfo().getBuId());
        }
    }

    @Async
    @EventListener
    public void handleCarReturnEvent(CarReturnEvent event) {
        log.info("長放車註銷事件, orderNo: {}", event.getOrderNo());

        // crsInfo 為 null 時，不發動事件處理，並記錄 carCrsInfo
        if (event.getCarCrsInfo() == null) {
            log.warn("長放車註銷事件不處理, carCrsInfo: null");
            return;
        }

        // crsCarNo 為 null 時，不發動事件處理，並記錄 carCrsCarNo
        if (event.getCarCrsInfo().getCarNo() == null) {
            log.warn("長放車註銷事件不處理, carCrsCarNo: null");
            return;
        }

        if (BuIdEnum.lRental.getCode().equals(event.getCarCrsInfo().getBuId())) {
            CarBaseUpdateRequest carBaseUpdateRequest = CarBaseUpdateRequest.builder()
                .carNo(event.getCarCrsInfo().getCarNo())
                .carBaseModifyQue("notDeque")
                .memo("訂閱迄租註銷" + event.getOrderNo())
                .userId(event.getMemberId())
                .build();
            try {
                crsClient.batchUpdateCarBaseInfo(PLATE_FORM_CODE, HeaderDefine.SystemKind.SUB, Collections.singletonList(carBaseUpdateRequest));
            } catch (Exception e) {
                log.error("長放車註銷事件失敗", e);
            }
        } else {
            log.warn("長放車註銷事件不處理, carBuId: {}", event.getCarCrsInfo().getBuId());
        }
    }

    @Async
    @TransactionalEventListener
    public void handleCarReplaceEvent(CarReplaceEvent event) {
        log.info("出車中換車 - 長放車註冊 & 註銷事件, orderNo: {}", event.getOrderNo());

        // crsInfo 為 null 時，不發動事件處理，並記錄 outCarCrsInfo, inCarCrsInfo
        if (event.getOutCarCrsInfo() == null || event.getInCarCrsInfo() == null) {
            log.warn("出車中換車 - 長放車註冊 & 註銷事件不處理, outCarCrsInfo: {}, inCarCrsInfo: {}",
                event.getOutCarCrsInfo(), event.getInCarCrsInfo());
            return;
        }

        // crsCarNo 為 null 時，不發動事件處理，並記錄 outCarCrsCarNo, inCarCrsCarNo
        if (event.getOutCarCrsInfo().getCarNo() == null || event.getInCarCrsInfo().getCarNo() == null) {
            log.warn("出車中換車 - 長放車註冊 & 註銷事件不處理, outCarCrsCarNo: {}, inCarCrsCarNo: {}",
                event.getOutCarCrsInfo().getCarNo(), event.getInCarCrsInfo().getCarNo());
            return;
        }

        List<CarBaseUpdateRequest> carBaseUpdateRequestList = new ArrayList<>();

        if (BuIdEnum.lRental.getCode().equals(event.getOutCarCrsInfo().getBuId())) {
            CarBaseUpdateRequest outCarUpdateRequest = CarBaseUpdateRequest.builder()
                .carNo(event.getOutCarCrsInfo().getCarNo())
                .carBaseModifyQue("notDeque")
                .memo("訂閱迄租註銷" + event.getOrderNo())
                .userId(event.getMemberId())
                .build();
            carBaseUpdateRequestList.add(outCarUpdateRequest);
        } else {
            log.warn("出車中換車 - 汰換車庫位非長租, 不進行長放車註銷事件, outCarBuId: {}", event.getOutCarCrsInfo().getBuId());
        }

        if (BuIdEnum.lRental.getCode().equals(event.getInCarCrsInfo().getBuId())) {
            CarBaseUpdateRequest inCarUpdateRequest = CarBaseUpdateRequest.builder()
                .carNo(event.getInCarCrsInfo().getCarNo())
                .carBaseModifyQue("subscribe")
                .memo("訂閱起租註冊" + event.getOrderNo())
                .userId(event.getMemberId())
                .build();
            carBaseUpdateRequestList.add(inCarUpdateRequest);
        } else {
            log.warn("出車中換車 - 替代車庫位非長租, 不進行長放車註冊事件, inCarBuId: {}", event.getInCarCrsInfo().getBuId());
        }

        if (carBaseUpdateRequestList.isEmpty()) {
            log.warn("出車中換車 - 長放車註冊 & 註銷事件不處理, carBaseUpdateRequestList 為空");
            return;
        }

        try {
            crsClient.batchUpdateCarBaseInfo(PLATE_FORM_CODE, HeaderDefine.SystemKind.SUB, carBaseUpdateRequestList);
        } catch (Exception e) {
            log.error("出車中換車 - 長放車註冊 & 註銷事件失敗", e);
        }
    }
}
