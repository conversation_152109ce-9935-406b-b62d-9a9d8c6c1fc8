package com.carplus.subscribe.mapper.priceinfo;

import carplus.common.response.exception.BadRequestException;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.model.AccidentInfo;
import com.carplus.subscribe.model.priceinfo.CalculateStage;
import com.carplus.subscribe.model.priceinfo.PriceInfoDetail;
import com.carplus.subscribe.model.priceinfo.req.InternalAccidentRequest;
import com.carplus.subscribe.model.request.priceinfo.ExtraFeeRequest;
import com.carplus.subscribe.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

import static com.carplus.subscribe.enums.OrderStatus.DEPART;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoCategory.CarAccident;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoCategory.Insurance;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoType.Pay;

@Slf4j
public class PriceInfoMapper {

    private PriceInfoMapper() {}

    /**
     * 更新車損費用 OrderPriceInfo
     */
    public static void buildUpdatedCarAccidentOPI(ExtraFeeRequest request, ExtraFeeRequest.ExtraFee fee, Orders order, OrderPriceInfo opi, String memberId) {
        opi.setCategory(fee.getCategory());
        opi.setAmount(fee.getAmount());
        if (opi.getInfoDetail() == null) {
            PriceInfoDetail pid = new PriceInfoDetail();
            pid.setReason(fee.getReason());
            pid.setCarLossAmt(fee.getAmount());
            pid.setARCarLossAmt(fee.getARCarLossAmt());
            pid.setAdminId(memberId);
            pid.setChargingPoint(fee.getPoint());
            opi.setInfoDetail(pid);
        } else {
            opi.getInfoDetail().setReason(fee.getReason());
            opi.getInfoDetail().setCarLossAmt(fee.getAmount());
            opi.getInfoDetail().setARCarLossAmt(fee.getARCarLossAmt());
            opi.getInfoDetail().setAdminId(memberId);
            opi.getInfoDetail().setChargingPoint(fee.getPoint());
        }
        // 更新 Order Accident Info
        AccidentInfo info = toCarAccidentTypeInfo(request, memberId);
        order.setAccidentInfo(info);
    }

    /**
     * 更新額外費用 OrderPriceInfo
     */
    public static void buildUpdatedExtraFeeOPI(ExtraFeeRequest.ExtraFee fee, OrderPriceInfo opi, String memberId) {
        opi.setCategory(fee.getCategory());
        opi.setAmount(fee.getAmount());
        if (opi.getInfoDetail() == null) {
            PriceInfoDetail pid = new PriceInfoDetail();
            pid.setReason(fee.getReason());
            pid.setAdminId(memberId);
            pid.setChargingPoint(fee.getPoint());
            opi.setInfoDetail(pid);
        } else {
            opi.getInfoDetail().setReason(fee.getReason());
            opi.getInfoDetail().setAdminId(memberId);
            opi.getInfoDetail().setChargingPoint(fee.getPoint());
        }
    }

    /**
     * OrderPriceInfo Category 車損費用
     */
    public static OrderPriceInfo buildNewCarAccidentOPI(ExtraFeeRequest.ExtraFee fee, Orders order, CalculateStage targetStage, OrderPriceInfo opi, String memberId) {
        PriceInfoDetail pid = new PriceInfoDetail();
        pid.setReason(fee.getReason());
        pid.setAdminId(memberId);
        pid.setChargingPoint(fee.getPoint());
        pid.setDay(targetStage.getDay());
        pid.setMonth(targetStage.getMonth());
        if (order.getStatus() < DEPART.getStatus()) {
            throw new BadRequestException("需出車後才可設定車損");
        }
        pid.setCarLossAmt(fee.getAmount());
        pid.setARCarLossAmt(fee.getARCarLossAmt());
        opi.setStage(fee.getStage());
        opi.setLastPayDate(DateUtil.convertToEndOfInstant(targetStage.getEndDate()));
        opi.setInfoDetail(pid);
        opi.setCategory(fee.getCategory());
        opi.setType(Pay.getCode());
        opi.setAmount(fee.getAmount());
        opi.setOrderNo(order.getOrderNo());
        return opi;
    }

    /**
     * OrderPriceInfo Category 額外費用
     */
    public static OrderPriceInfo buildNewExtraFeeOPI(ExtraFeeRequest.ExtraFee fee, Orders order, CalculateStage targetStage, OrderPriceInfo opi, String memberId) {
        PriceInfoDetail pid = new PriceInfoDetail();
        pid.setReason(fee.getReason());
        pid.setAdminId(memberId);
        pid.setChargingPoint(fee.getPoint());
        pid.setDay(targetStage.getDay());
        pid.setMonth(targetStage.getMonth());
        if (Insurance.equals(fee.getCategory())) {
            pid.setInsurance(fee.getAmount() / pid.getMonth());
        }
        opi.setStage(fee.getStage());
        opi.setLastPayDate(DateUtil.convertToEndOfInstant(targetStage.getEndDate()));
        opi.setInfoDetail(pid);
        opi.setCategory(fee.getCategory());
        opi.setType(Pay.getCode());
        opi.setAmount(fee.getAmount());
        opi.setOrderNo(order.getOrderNo());
        return opi;
    }

    public static AccidentInfo toCarAccidentTypeInfo(final ExtraFeeRequest req, final String memberId) {
        return AccidentInfo.builder()
                .carDamaged(req.isCarDamaged())
                .returnNego(req.getReturnNego())
                .adminId(memberId)
                .build();
    }

    public static ExtraFeeRequest toExtraFeeReq(InternalAccidentRequest accidentRequest, int latestStage) {
        ExtraFeeRequest.ExtraFee extraFee = ExtraFeeRequest.ExtraFee.builder()
                .category(CarAccident)
                .stage(latestStage)
                .amount(accidentRequest.getCarLossAmt())
                .aRCarLossAmt(accidentRequest.getARCarLossAmt())
                .reason(accidentRequest.getRemark())
                .build();
        // Mobile 事故還車 如果金額為0，則不新增一筆 0 的費用 (後台則反之，FE會強制送 0元 費用 payload建立)
        List<ExtraFeeRequest.ExtraFee> extraFees = new ArrayList<>();
        if (accidentRequest.getARCarLossAmt() != 0 && accidentRequest.getCarLossAmt() != 0) {
            extraFees.add(extraFee);
        }
        return ExtraFeeRequest.builder()
                .returnNego(accidentRequest.getReturnNego())
                .carDamaged(accidentRequest.isCarDamaged())
                .extraFeeList(extraFees)
                .build();
    }
}
