package com.carplus.subscribe.db.mysql.entity;

import com.carplus.subscribe.db.mysql.LazyFieldsFilter;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.enums.SubscribeType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.time.Instant;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@FieldNameConstants
@NoArgsConstructor
@Entity
@Table(name = "car_wishlist")
public class CarWishlist extends GeneralEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "acctId", nullable = false)
    private int acctId;

    @Column(name = "plateNo", nullable = false)
    private String plateNo;

    /**
     * 加入收藏時的使用月費
     */
    @Column(name = "useMonthlyFee", nullable = false)
    private int useMonthlyFee;

    /**
     * 加入收藏時的里程費
     */
    @Column(name = "mileageFee", nullable = false)
    private double mileageFee;

    /**
     * 加入收藏時的保證金
     */
    @Column(name = "securityDeposit", nullable = false)
    private int securityDeposit;

    /**
     * 加入收藏時的方案類型
     */
    @Column(name = "type", nullable = false)
    @Enumerated(EnumType.STRING)
    private SubscribeType type;

    /**
     * 加入收藏時的標籤id
     */
    @Column(name = "tagIds")
    @Type(type = "json")
    private List<Integer> tagIds;

    /**
     * 加入收藏時的所在站所代號
     */
    @Column(name = "locationStationCode")
    private String locationStationCode;

    /**
     * 移除時間
     */
    @Column(name = "deletedAt")
    private Instant deletedAt;

    /**
     * 車籍資料
     */
    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "plateNo", referencedColumnName = "plateNo", insertable = false, updatable = false)
    @JsonInclude(value = JsonInclude.Include.CUSTOM, valueFilter = LazyFieldsFilter.class)
    private Cars car;

    public CarWishlist(Integer acctId, Cars car, SubscribeLevel subscribeLevel) {
        this.acctId = acctId;
        this.plateNo = car.getPlateNo();
        this.useMonthlyFee = car.getUseMonthlyFee(subscribeLevel);
        this.mileageFee = subscribeLevel.getMileageFee();
        this.securityDeposit = subscribeLevel.getSecurityDeposit();
        this.type = subscribeLevel.getType();
        this.tagIds = car.getTagIds();
        this.locationStationCode = car.getLocationStationCode();
    }
}
