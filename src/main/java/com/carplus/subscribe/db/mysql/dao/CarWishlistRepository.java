package com.carplus.subscribe.db.mysql.dao;

import com.carplus.subscribe.db.mysql.entity.CarWishlist;
import com.carplus.subscribe.db.mysql.entity.cars.CarBrand;
import com.carplus.subscribe.db.mysql.entity.cars.CarModel;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.model.request.carwishlist.CarWishlistCriteria;
import com.carplus.subscribe.model.request.carwishlist.CommonCarWishlistCriteria;
import lombok.NonNull;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.criteria.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Repository
public class CarWishlistRepository extends SimpleJpaRepository<CarWishlist, Integer> {

    private final EntityManager em;

    public CarWishlistRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(CarWishlist.class, em);
        this.em = em;
    }

    @SuppressWarnings("unchecked")
    private <T extends CommonCarWishlistCriteria> void prepareQuery(CriteriaBuilder cb, CriteriaQuery<?> cq, T criteria, boolean isCount) {
        Root<CarWishlist> carWishlistRoot = cq.from(CarWishlist.class);
        List<Predicate> predicates = new ArrayList<>();

        predicates.add(cb.equal(carWishlistRoot.get(CarWishlist.Fields.acctId), criteria.getAcctId()));
        if (CollectionUtils.isNotEmpty(criteria.getPlateNos())) {
            predicates.add(carWishlistRoot.get(CarWishlist.Fields.plateNo).in(criteria.getPlateNos()));
        }

        if (!criteria.isIncludeDeleted()) {
            predicates.add(cb.isNull(carWishlistRoot.get(CarWishlist.Fields.deletedAt)));
        }

        if (criteria instanceof CarWishlistCriteria) {
            CarWishlistCriteria carWishlistCriteria = (CarWishlistCriteria) criteria;
            if (CollectionUtils.isNotEmpty(carWishlistCriteria.getBrandCodes()) || CollectionUtils.isNotEmpty(carWishlistCriteria.getCarModelCodes())) {
                Join<CarWishlist, Cars> carsJoin = carWishlistRoot.join(CarWishlist.Fields.car, JoinType.LEFT);
                Join<Cars, CarModel> carModelJoin = carsJoin.join(Cars.Fields.carModel, JoinType.LEFT);

                if (CollectionUtils.isNotEmpty(carWishlistCriteria.getBrandCodes())) {
                    Join<CarModel, CarBrand> carBrandJoin = carModelJoin.join(CarModel.Fields.carBrand, JoinType.LEFT);
                    predicates.add(carBrandJoin.get(CarBrand.Fields.brandCode).in(carWishlistCriteria.getBrandCodes()));
                }
                if (CollectionUtils.isNotEmpty(carWishlistCriteria.getCarModelCodes())) {
                    predicates.add(carModelJoin.get(CarModel.Fields.carModelCode).in(carWishlistCriteria.getCarModelCodes()));
                }
            }
        }

        if (isCount) {
            ((CriteriaQuery<Long>) cq).select(cb.count(carWishlistRoot));
        } else {
            ((CriteriaQuery<CarWishlist>) cq).select(carWishlistRoot).orderBy(cb.desc(carWishlistRoot.get("updateDate")));
        }
        cq.where(predicates.toArray(new Predicate[0]));
    }

    public long count(@NonNull CommonCarWishlistCriteria criteria) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Long> cq = cb.createQuery(Long.class);
        prepareQuery(cb, cq, criteria, true);
        return em.createQuery(cq).getSingleResult();
    }

    public <T extends CommonCarWishlistCriteria> List<CarWishlist> findBySearch(T criteria, int offset, int limit) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<CarWishlist> cq = cb.createQuery(CarWishlist.class);
        prepareQuery(cb, cq, criteria, false);
        return em.createQuery(cq)
            .setFirstResult(offset)
            .setMaxResults(limit)
            .getResultList();
    }

    public boolean existsByAcctIdAndPlateNo(Integer acctId, String plateNo) {
        CommonCarWishlistCriteria criteria = new CommonCarWishlistCriteria();
        criteria.setAcctId(acctId);
        criteria.setPlateNos(Collections.singletonList(plateNo));
        return count(criteria) > 0;
    }

    public long countByAcctIdAndDeletedAtIsNull(Integer acctId) {
        CommonCarWishlistCriteria criteria = new CommonCarWishlistCriteria();
        criteria.setAcctId(acctId);
        return count(criteria);
    }

    public Optional<CarWishlist> findByAcctIdAndPlateNo(Integer acctId, String plateNo, boolean includeDeleted) {
        CommonCarWishlistCriteria criteria = new CommonCarWishlistCriteria();
        criteria.setAcctId(acctId);
        criteria.setPlateNos(Collections.singletonList(plateNo));
        criteria.setIncludeDeleted(includeDeleted);

        List<CarWishlist> results = findBySearch(criteria, 0, 1);
        return results.isEmpty() ? Optional.empty() : Optional.ofNullable(results.get(0));
    }
}