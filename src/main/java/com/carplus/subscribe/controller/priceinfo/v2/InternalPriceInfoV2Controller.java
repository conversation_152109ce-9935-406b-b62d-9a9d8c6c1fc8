package com.carplus.subscribe.controller.priceinfo.v2;

import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.model.priceinfo.resp.ExtraOrderPriceModel;
import com.carplus.subscribe.model.priceinfo.resp.StageOrderPriceInfoResponse;
import com.carplus.subscribe.model.request.priceinfo.OrderPriceInfoCriteriaRequest;
import com.carplus.subscribe.service.PriceInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;
import java.util.Map;

@CarPlusRestController
@RequestMapping(HttpConstant.INTERNAL_URL)
@Tag(name = "Internal 訂單費用資訊API")
public class InternalPriceInfoV2Controller {

    @Autowired
    private PriceInfoService priceInfoService;

    @Operation(summary = "訂單付款資訊清單")
    @GetMapping(value = "/subscribe/v2/priceInfo/{orderNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public Map<Integer, StageOrderPriceInfoResponse> priceInfo(
        @PathVariable("orderNo") String orderNo, OrderPriceInfoCriteriaRequest request) {
        return priceInfoService.getStageGroupPriceInfoByOrder(orderNo, request);
    }

    @Operation(summary = "可新增額外費用的訂單付款資訊類別清單")
    @GetMapping(value = "/subscribe/v2/extra/example/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<ExtraOrderPriceModel> getPriceInfoAddableAsExtraFee() {
        return priceInfoService.getPriceInfoAddableAsExtraFee();
    }

    @Operation(summary = "可查詢額外費用明細的費用類別清單")
    @GetMapping(value = "/subscribe/v2/extra/detail/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<ExtraOrderPriceModel> getPriceInfoQueryableAsExtraFee() {
        return priceInfoService.getPriceInfoQueryableAsExtraFee();
    }
}
