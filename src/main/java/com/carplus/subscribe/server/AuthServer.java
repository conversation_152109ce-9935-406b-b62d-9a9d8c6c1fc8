package com.carplus.subscribe.server;

import carplus.common.enums.HeaderDefine;
import carplus.common.response.Result;
import carplus.common.response.exception.BadRequestException;
import carplus.common.response.exception.CarPlusException;
import carplus.common.response.exception.LogicException;
import carplus.common.response.exception.ServerException;
import carplus.common.utils.HttpUtils;
import carplus.common.utils.StringUtils;
import carplus.common.utils.TaiwanIDUtils;
import com.carplus.subscribe.db.mysql.entity.contract.EContractReferencable;
import com.carplus.subscribe.db.mysql.entity.contract.IOrder;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrder;
import com.carplus.subscribe.enums.CreditMechanismType;
import com.carplus.subscribe.enums.CreditRemarkType;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.model.audit.UserDocumentsReviewByCashierPUTV2Req;
import com.carplus.subscribe.model.audit.UserDocumentsReviewInternalRes;
import com.carplus.subscribe.model.auth.AuthUser;
import com.carplus.subscribe.model.auth.req.AuthDealerUserSaveRequest;
import com.carplus.subscribe.model.auth.req.CustomerDriverPostReq;
import com.carplus.subscribe.model.auth.req.MultiQueryRequest;
import com.carplus.subscribe.model.auth.resp.*;
import com.carplus.subscribe.model.credit.CreditCheckFullResponse;
import com.carplus.subscribe.model.credit.CreditInfo;
import com.carplus.subscribe.model.credit.req.CreditCheckRequest;
import com.carplus.subscribe.model.credit.resp.CreditCheckFullRes;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.*;

@Slf4j
@Component
public class AuthServer {

    @Value("${carplus.service.auth}")
    private String authUri;
    @Value("${carplus.service.audit}")
    private String auditUri;
    @Autowired
    private ObjectMapper objectMapper;
    @Value("${audit.timeout}")
    private Integer auditTimeout;

    /**
     * 紀錄自動授信資訊
     */
    private void recordAutoCreditResult(Orders order, CreditCheckFullResponse response, boolean isCredit, CreditCheckFullRes check) {
        if (isCredit) {
            order.setCreditInfo(Optional.ofNullable(order.getCreditInfo()).orElseGet(CreditInfo::new)
                .updateAutoCredit(CreditRemarkType.AUTO_CREDIT_SUCCESS, CreditMechanismType.CRIF, null));
        } else {
            List<String> failMessage = null;
            CreditRemarkType autoCreditRemarkType = (response == null || response.getData() == null || response.getData().getPass() == CreditCheckFullRes.Pass.FAIL)
                ? CreditRemarkType.SYSTEM_CREDIT_FAIL :
                CreditRemarkType.AUTO_CREDIT_FAIL;
            if (check != null) {
                if (check.getPass() == CreditCheckFullRes.Pass.FAIL) {
                    autoCreditRemarkType = CreditRemarkType.CREDIT_FAIL;
                } else if (check.getPass() == CreditCheckFullRes.Pass.MANUALLY) {
                    autoCreditRemarkType = CreditRemarkType.AUTO_CREDIT_FAIL;
                }
                if (!check.getDetails().isEmpty()) {
                    failMessage = check.getDetails().stream().filter(detail -> !detail.getIsValided()).map(CreditCheckFullRes.DetailRes::getMessage).collect(Collectors.toList());
                }
            } else {
                failMessage = Optional.ofNullable(response).filter(resp -> StringUtils.isNotEmpty(resp.getMessage())).map(resp -> Lists.newArrayList(resp.getMessage())).orElse(null);
            }
            order.setCreditInfo(Optional.ofNullable(order.getCreditInfo()).orElseGet(CreditInfo::new)
                .updateAutoCredit(autoCreditRemarkType, CreditMechanismType.CRIF, failMessage));
        }
    }

    /**
     * 自動授信
     */
    public boolean doAutoCredit(@NonNull Orders order, AuthUser user) {
        if (Objects.equals(1, user.getIsForeigner()) || !TaiwanIDUtils.validateTaiwanID(user.getLoginId())) {
            order.setCreditInfo(Optional.ofNullable(order.getCreditInfo()).orElseGet(CreditInfo::new)
                .updateAutoCredit(CreditRemarkType.ID_VALIDATE_FAIL, CreditMechanismType.CRIF, Collections.singletonList(CreditRemarkType.ID_VALIDATE_FAIL.getDescription())));
            return false;
        }

        CreditCheckRequest request = new CreditCheckRequest(order.getOrderNo(), user);
        log.info("訂單編號: {}, 請求授信: {}", order.getOrderNo(), request);
        try {
            return HttpUtils.post(
                    auditUri + "/internal/audit/v2/users/ccis/creditCheck",
                    HttpUtils.Options.custom()
                        .requestConfig(builder -> builder.setSocketTimeout(auditTimeout))
                        .entity(() -> new StringEntity(objectMapper.writeValueAsString(request), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    CreditCheckFullResponse response = objectMapper.readValue(res.getEntity().getContent(), CreditCheckFullResponse.class);

                    boolean isCredit = false;
                    CreditCheckFullRes check = null;
                    if (response != null && response.getStatusCode() == 0) {
                        check = response.getData();

                        log.info("訂單編號: {}, 授信結果：{}", order.getOrderNo(), check);
                        if (check != null && check.getPass() == CreditCheckFullRes.Pass.PASS) {
                            isCredit = true;
                        }
                    }
                    // 紀錄自動授信資訊
                    recordAutoCreditResult(order, response, isCredit, check);
                    return isCredit;
                })
                .fetch();
        } catch (Exception e) {
            log.error("call audit-service for CRIF check error：", e);
            order.setCreditInfo(Optional.ofNullable(order.getCreditInfo()).orElseGet(CreditInfo::new)
                .updateAutoCredit(CreditRemarkType.SYSTEM_CREDIT_FAIL, CreditMechanismType.CRIF, Lists.newArrayList(e.getMessage())));
        }

        return false;
    }

    public AuthUser getUserWithRetry(int acctId) {
        synchronized (this) {
            return getUserWithRetry(acctId, 0);
        }
    }

    private AuthUser getUserWithRetry(int acctId, int count) {
        try {
            return getUser(acctId);
        } catch (Exception e) {
            count++;
            if (count > 3) {
                log.error("查詢使用者已嘗試查詢3次皆失敗，不再查詢{}", acctId);
                throw new ServerException(e);
            } else {
                try {
                    wait(500);
                } catch (InterruptedException ex) {
                    // ignore
                }
                return getUserWithRetry(acctId, count);
            }
        }
    }

    /**
     * 取得會員資料 by account id
     */
    @NonNull
    public AuthUser getUser(int acctId) {
        try {
            return HttpUtils.post(
                auditUri + "/internal/audit/v1/users/multiQuery",
                    HttpUtils.Options.custom().entity(() -> new StringEntity(objectMapper.writeValueAsString(new MultiQueryRequest(acctId)), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    MultiQueryResponse response = objectMapper.readValue(res.getEntity().getContent(), MultiQueryResponse.class);

                    if (response.getStatusCode() != 0) {
                        throw new ServerException(response.getMessage());
                    }

                    if (response.getData() != null && response.getData().getUsers() != null && !response.getData().getUsers().isEmpty()) {
                        return response.getData().getUsers().get(0);
                    }

                    throw new ServerException(String.format("找不到該ACCTID[%d]使用者", acctId));
                })
                .fetch();
        } catch (CarPlusException e) {
            log.error("call audit-service for get user error：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("call audit-service for get user error：{}", e.getMessage());
            throw new ServerException(e);
        }
    }

    /**
     * 取得會員資料 by 身分證+電話
     */
    @Nullable
    public AuthUser getUser(@Nullable String cid, @Nullable String phone) {
        if (StringUtils.isBlank(cid) && StringUtils.isBlank(phone)) {
            return null;
        }

        try {
            return HttpUtils.get(
                    auditUri + "/internal/audit/v1/users/query",
                    HttpUtils.Options.custom().queryString("loginId", StringUtils.trim(cid)).queryString("mainCell", StringUtils.trim(phone)))
                .then(res -> {
                    AuthUserResponse response = objectMapper.readValue(res.getEntity().getContent(), AuthUserResponse.class);

                    if (response.getStatusCode() != 0) {
                        throw new ServerException(response.getMessage());
                    }

                    return response.getData();
                })
                .fetch();
        } catch (CarPlusException e) {
            log.error("call audit-service for get user error：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("call audit-service for get user error：{}", e.getMessage());
            throw new ServerException(e);
        }
    }

    /**
     * 取得會員資料 by 姓名/電話
     */
    @NonNull
    public List<Integer> getUsers(@Nullable String acctName, @Nullable String phone, @Nullable String idNo) {
        return getUserAcctIds(acctName, phone, idNo).stream().map(AuthUser::getAcctId).collect(Collectors.toList());
    }

    /**
     * 取得多筆會員資料 by accountId ignore Exception
     */
    @NonNull
    public List<AuthUser> getUserAcctIds(Integer... acctIds) {
        try {
            return HttpUtils.post(
                    auditUri + "/internal/audit/v1/users/multiQuery",
                    HttpUtils.Options.custom().entity(() -> new StringEntity(objectMapper.writeValueAsString(new MultiQueryRequest(acctIds)), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    MultiQueryResponse response = objectMapper.readValue(res.getEntity().getContent(), MultiQueryResponse.class);

                    return Optional.ofNullable(response)
                        .filter(r -> r.getStatusCode() == 0)
                        .map(Result::getData)
                        .map(com.carplus.subscribe.model.auth.resp.MultiQueryRes::getUsers)
                        .orElseGet(Lists::newArrayList);
                })
                .fetch();
        } catch (Exception e) {
            log.error("getUsers error：", e);
        }

        return Lists.newArrayList();
    }


    /**
     * 取得會員資料 by 姓名/電話
     */
    @NonNull
    public List<AuthUser> getUserAcctIds(@Nullable String acctName, @Nullable String phone, @Nullable String idNo) {
        List<AuthUser> users = Lists.newArrayList();

        if (StringUtils.isBlank(acctName) && StringUtils.isBlank(phone) && StringUtils.isBlank(idNo)) {
            return users;
        }

        try {
            HttpUtils.Options options = HttpUtils.Options.custom();
            if (StringUtils.isNotBlank(acctName)) {
                options.queryString("acctName", acctName);
            }
            if (StringUtils.isNotBlank(phone)) {
                options.queryString("mainCell", phone);
            }
            if (StringUtils.isNotBlank(idNo)) {
                options.queryString("loginId", idNo);
            }
            HttpUtils.get(auditUri + "/internal/audit/v1/users/queries", options)
                .then(res -> {
                    QueriesResponse response = objectMapper.readValue(res.getEntity().getContent(), QueriesResponse.class);

                    if (response.getStatusCode() != 0) {
                        throw new ServerException(response.getMessage());
                    }

                    users.addAll(Optional.ofNullable(response.getData()).map(QueriesRes::getList).orElseGet(Lists::newArrayList));

                    return null;
                })
                .fetch();
        } catch (CarPlusException e) {
            log.error("call audit-service for get user error：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("call audit-service for get user error：{}", e.getMessage());
            throw new ServerException(e);
        }

        return users;
    }

    /**
     * IVR需求單 會員中心註冊戳記
     **/
    public void ivrSignUp(@NonNull String ivrId, @NonNull String phone) {
        try {
            Map<String, Object> body = Maps.newHashMap();
            body.put("ivrId", ivrId);
            body.put("mainCell", phone);
            HttpUtils.post(authUri + "/internal/auth/v1/users/ivr/fastPass",
                    HttpUtils.Options
                        .custom()
                        .entity(() -> new StringEntity(objectMapper.writeValueAsString(body), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    Result result = objectMapper.readValue(res.getEntity().getContent(), Result.class);
                    if (result.getStatusCode() != 0) {
                        log.error("ivr會員註冊戳記失敗：", result.getMessage() + ", " + result.getData());
                    }
                    return null;
                })
                .fetch();
        } catch (Exception e) {
            log.error("ivr會員註冊戳記失敗：", e);
        }
    }

    /**
     * 檢查黑名單
     */
    @Nullable
    public Boolean checkBlackList(@Nullable String idNo, @Nullable Integer acctId) {
        try {
            HttpUtils.Options options = HttpUtils.Options.custom();
            if (StringUtils.isNotBlank(idNo)) {
                options.queryString("loginId", idNo);
            } else if (Objects.nonNull(acctId)) {
                options.queryString("acctId", String.valueOf(acctId));
            }
            return HttpUtils.get(auditUri + "/internal/audit/v1/users/blacklist/verify", options)
                .then(res -> {
                    Result<List<Map>> result = objectMapper.readValue(res.getEntity().getContent(), Result.class);
                    if (result.getStatusCode() == 0 && result.getData().size() == 0) {
                        return true;
                    }
                    if (result.getStatusCode() == 0 && result.getData().size() > 0) {
                        log.warn("身份證字號或會員帳號為黑名單, pid=" + idNo + ", acctId=" + acctId);
                        return false;
                    }
                    log.info("身份證字號或會員帳號黑名單檢查, pid:" + idNo + ", acctId=" + acctId + ", 結果=" + result);
                    return null;
                })
                .fetch();
        } catch (Exception e) {
            log.error("call audit-service for check blacklist error:", e);
        }
        return null;
    }

    /**
     * 檢查黑名單 by pId身分證
     */
    @NonNull
    public List<String> checkPidBlackList(@NonNull List<String> idNos) {
        CopyOnWriteArrayList<String> pidBlackList = Lists.newCopyOnWriteArrayList();
        CompletableFuture[] completableFutures = new CompletableFuture[idNos.size()];
        for (int i = 0; i < idNos.size(); i++) {
            String pid = idNos.get(i);
            completableFutures[i] = CompletableFuture.runAsync(() -> Optional.ofNullable(this.checkBlackList(pid, null))
                .ifPresent(b -> {
                    if (!b) {
                        pidBlackList.add(pid);
                    }
                }));
        }
        CompletableFuture.allOf(completableFutures)
            .join();
        return pidBlackList;
    }

    /**
     * 檢查黑名單 by acctId會員
     */
    @NonNull
    public List<Integer> checkAcctBlackList(@NonNull List<Integer> acctIds) {
        CopyOnWriteArrayList<Integer> acctBlackList = Lists.newCopyOnWriteArrayList();
        CompletableFuture[] completableFutures = new CompletableFuture[acctIds.size()];
        for (int i = 0; i < acctIds.size(); i++) {
            Integer acctId = acctIds.get(i);
            completableFutures[i] = CompletableFuture.runAsync(() -> Optional.ofNullable(this.checkBlackList(null, acctId))
                .ifPresent(b -> {
                    if (!b) {
                        acctBlackList.add(acctId);
                    }
                }));
        }
        CompletableFuture.allOf(completableFutures)
            .join();
        return acctBlackList;
    }

    /**
     * 駕駛轉換成會員
     */
    @NonNull
    public DriverDTOResponse driverToUsers(CustomerDriverPostReq customerDriverPostReq) {
        try {
            return HttpUtils.post(
                    auditUri + "/internal/audit/v1/users/driver/convert",
                    HttpUtils.Options.custom()
                        .header("X-System-Kind", HeaderDefine.SystemKind.CASHIER)
                        .header("X-Platform", HeaderDefine.Platform.SERVER)
                        .header("X-MemberId", "SUB")
                        .entity(() -> {
                            StringEntity stringEntity = new StringEntity(objectMapper.writeValueAsString(customerDriverPostReq), StandardCharsets.UTF_8);
                            stringEntity.setContentType("application/json");
                            return stringEntity;
                        }))
                .then(res -> {
                    return objectMapper.readValue(res.getEntity().getContent(), DriverDTOResponse.class);

                })
                .fetch();
        } catch (Exception e) {
            log.error("getUsers error：", e);
        }

        return null;
    }

    /**
     * 經銷商客戶資料查詢
     */
    @NonNull
    public List<AuthDealerUserResponse> getDealerUsers(@NonNull AuthDealerUserUnionQuery query) {
        HttpUtils.Options options = HttpUtils.Options.custom();
        options.header("X-MemberId", "SUB")
            .header("X-Platform", HeaderDefine.Platform.SERVER)
            .header("X-System-Kind", HeaderDefine.SystemKind.SUB)
            .entity(() -> {
                StringEntity stringEntity = new StringEntity(objectMapper.writeValueAsString(query), StandardCharsets.UTF_8);
                stringEntity.setContentType("application/json");
                return stringEntity;
            });

        try {
            return HttpUtils.post(auditUri + "/internal/audit/v1/seaLand/users/unionQuery", options)
                .then(res -> {
                    QueryAuthDealerUserResponse response = objectMapper.readValue(res.getEntity().getContent(), QueryAuthDealerUserResponse.class);

                    if (response.getStatusCode() != 0) {
                        throw new ServerException(response.getMessage());
                    }

                    return Optional.ofNullable(response.getData()).map(QueryAuthDealerUserRes::getUsers).orElseGet(ArrayList::new);
                })
                .fetch();
        } catch (Exception e) {
            log.error("getDealerUsers error：", e);
            throw new SubscribeException(DEALER_USER_NOT_FOUND);
        }
    }

    @NonNull
    public AuthUser getAuthUser(DealerOrder dealerOrder) {
        AuthDealerUserResponse dealerUser = getDealerUser(dealerOrder.getDealerUserId());
        if (dealerUser == null) {
            throw new SubscribeException(DEALER_USER_NOT_FOUND);
        }
        List<AuthUser> authUsers = getUserAcctIds(dealerUser.getUserName(), dealerUser.getMainCell(), dealerUser.getIdNo());
        if (authUsers.isEmpty()) {
            throw new SubscribeException(ECONTRACT_ACCOUNT_NOT_EXISTS);
        }
        return authUsers.get(0);
    }

    @NonNull
    public AuthUser getAuthUser(EContractReferencable entity) {
        Integer acctId = entity.getAcctId();

        if (acctId != null) {
            return getUser(acctId);
        } else {
            return getAuthUser((DealerOrder) entity);
        }
    }

    public <T> AuthDealerUserResponse getDealerUser(@NonNull T idNoOrId) throws SubscribeException {
        AuthDealerUserUnionQuery query = new AuthDealerUserUnionQuery();
        if (idNoOrId instanceof String) {
            query.setIdNo(Collections.singletonList((String) idNoOrId));
        } else if (idNoOrId instanceof Long) {
            query.setIds(Collections.singletonList(((Long) idNoOrId).intValue()));
        } else {
            throw new SubscribeException(DEALER_USER_QUERY_PARAMETER_ERROR);
        }
        List<AuthDealerUserResponse> dealerUsers = getDealerUsers(query);
        if (dealerUsers.isEmpty()) {
            return null;
        }

        return dealerUsers.get(0);
    }

    /**
     * 經銷商客戶資訊從 AuthServer 取得
     * Generate DealerUserMap
     */
    public Map<Long, AuthDealerUserResponse> generateDealerUserMap(Set<Long> dealerUserIds) {
        AuthDealerUserUnionQuery query = new AuthDealerUserUnionQuery();
        query.setIds(dealerUserIds.stream().map(Long::intValue).collect(Collectors.toList()));
        return getDealerUsers(query).stream().collect(Collectors.toMap(AuthDealerUserResponse::getId, Function.identity()));
    }

    /**
     * 經銷商客戶資料新增/更新
     */
    @NonNull
    public List<AuthDealerUserResponse> saveDealerUsers(@NonNull AuthDealerUserSaveRequest request) {
        try {
            return HttpUtils.post(auditUri + "/internal/audit/v1/seaLand/users",
                    HttpUtils.Options.custom()
                        .header("X-MemberId", "SUB")
                        .header("X-Platform", HeaderDefine.Platform.SERVER)
                        .header("X-System-Kind", HeaderDefine.SystemKind.SUB)
                        .entity(() -> new StringEntity(objectMapper.writeValueAsString(request), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    QueryAuthDealerUserResponse response = objectMapper.readValue(res.getEntity().getContent(), QueryAuthDealerUserResponse.class);

                    if (response.getStatusCode() != 0) {
                        throw new ServerException(response.getMessage());
                    }
                    if (response.getData() != null) {
                        if (CollectionUtils.isNotEmpty(response.getData().getErrorInfos())) {
                            throw new ServerException(response.getData().getErrorInfos().stream().map(error -> String.format("錯誤訊息: %s", error.getErrorMsg())).collect(Collectors.joining(",")));
                        } else {
                            return response.getData().getUsers();
                        }
                    }
                    throw new SubscribeException(DEALER_USER_CREATE_FAIL);
                })
                .fetch();
        } catch (CarPlusException e) {
            log.error("call audit-service for save dealer user error：{}", e.getMessage());
            throw LogicException.of(DEALER_USER_CREATE_FAIL, Optional.ofNullable(e.getReason()).orElse("Unknown reason"));
        } catch (Exception e) {
            log.error("saveDealerUsers error：", e);
            throw new SubscribeException(DEALER_USER_CREATE_FAIL);
        }
    }


    /**
     * 使用者簽署最新使用者條款
     */
    @Async
    public void privacyPolicyAccept(String memberId, IOrder order) {
        if (StringUtils.isBlank(order.getDepartTaskId())) {
            return;
        }
        Map<String, String> parameter = new HashMap<>();
        parameter.put("corporation", "CARPLUS");
        parameter.put("userOrderNo", order.getOrderNo());
        try {
            Integer acctId = order instanceof Orders ? ((Orders) order).getContract().getMainContract().getAcctId() : getAuthUser((DealerOrder) order).getAcctId();
            parameter.put("acctId", acctId.toString());
            HttpUtils.post(
                    String.format("%s/internal/auth/v3/agreements/users/accept", authUri),
                    HttpUtils.Options.custom()
                        .header("X-Platform", HeaderDefine.Platform.SERVER)
                        .header("X-MemberId", memberId)
                        .header("X-System-Kind", HeaderDefine.SystemKind.CASHIER)
                        .entity(() -> new StringEntity(objectMapper.writeValueAsString(parameter), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    Result<?> result = objectMapper.readValue(res.getEntity().getContent(), Result.class);
                    log.info("approval Policy response: {}", result);
                    return result;
                })
                .fetch();
        } catch (Exception e) {
            log.error("call audit-service for approval Policy error：{}", e.getMessage(), e);
        }
    }

    /**
     * 拿取使用者證件照
     */
    @Nullable
    public UserDocumentsReviewInternalRes getAuditPreSignedReview(Integer acctId) {
        try {
            HttpUtils.Options options = HttpUtils.Options.custom();
            options.queryString("acctId", String.valueOf(acctId))
                .queryString("systemKind", HeaderDefine.SystemKind.CASHIER);
            return HttpUtils.get(auditUri + "/internal/audit/v2/users/documents/review", options)
                .then(res -> {
                    TypeReference<Result<UserDocumentsReviewInternalRes>> typeReference = new TypeReference<Result<UserDocumentsReviewInternalRes>>() {
                    };
                    Result<UserDocumentsReviewInternalRes> result = objectMapper.readValue(res.getEntity().getContent(), typeReference);
                    if (result.getStatusCode() == 0 && result.getData() != null) {
                        return result.getData();
                    }
                    // 查無審核文件當作狀態-1
                    if (Objects.equals(result.getStatusCode(), 1308)) {
                        UserDocumentsReviewInternalRes resp = new UserDocumentsReviewInternalRes();
                        resp.setVerifyStatus(-1);
                        return resp;
                    }
                    log.info("拿取證件結果 fail.{},acctId:{}", result, acctId);
                    throw new BadRequestException("拿取證件結果 fail");
                })
                .fetch();
        } catch (Exception e) {
            log.error("call audit-service for document review error:", e);
        }
        return null;
    }


    /**
     * 通過使用者證件照審核
     */
    @Nullable
    public void approveAuditPreSignedReview(UserDocumentsReviewByCashierPUTV2Req req, String memberId) {
        try {
            HttpUtils.Options options = HttpUtils.Options.custom();
            options.header("X-Platform", HeaderDefine.Platform.SERVER);
            options.header("X-System-Kind", HeaderDefine.SystemKind.CASHIER);
            options.header("X-MemberId", memberId);
            options.queryString(UserDocumentsReviewByCashierPUTV2Req.Fields.acctId, req.getAcctId().toString());
            options.queryString(UserDocumentsReviewByCashierPUTV2Req.Fields.idCardFrontId, req.getIdCardFrontId().toString());
            options.queryString(UserDocumentsReviewByCashierPUTV2Req.Fields.idCardBackId, req.getIdCardBackId().toString());
            options.queryString(UserDocumentsReviewByCashierPUTV2Req.Fields.driverLicenseFrontId, req.getDriverLicenseFrontId().toString());
            options.queryString(UserDocumentsReviewByCashierPUTV2Req.Fields.reviewStatus, req.getReviewStatus().name());
            HttpUtils.put(auditUri + "/internal/audit/v2/users/documents/review/cashier", options)
                .then(res -> {
                    Result<?> result = objectMapper.readValue(res.getEntity().getContent(), Result.class);
                    // statusCode=1316, message=審核已通過，不可重複送審
                    if (result.getStatusCode() == 0 || result.getStatusCode() == 1316) {
                        return result.getData();
                    }
                    log.info("通過證件 fail.{},req:{}", result, req);
                    throw new BadRequestException("通過證件結果 fail");
                })
                .fetch();
        } catch (Exception e) {
            log.error("call audit-service for approve document review error:", e);
        }
    }


    /**
     * 通過使用者證件照審核
     */
    @Nullable
    @Async
    public void approveAuditPreSignedReview(Integer acctId, String memberId) {
        UserDocumentsReviewInternalRes reviewInternalRes = getAuditPreSignedReview(acctId);
        if (reviewInternalRes != null
            && reviewInternalRes.getVerifyStatus() != -1
            && reviewInternalRes.getIdCardFrontId() != null
            && reviewInternalRes.getIdCardBackId() != null
            && reviewInternalRes.getDriverLicenceFrontId() != null) {
            UserDocumentsReviewByCashierPUTV2Req req = new UserDocumentsReviewByCashierPUTV2Req();
            req.setAcctId(acctId);
            req.setIdCardFrontId(reviewInternalRes.getIdCardFrontId());
            req.setIdCardBackId(reviewInternalRes.getIdCardBackId());
            req.setDriverLicenseFrontId(reviewInternalRes.getDriverLicenceFrontId());
            req.setReviewStatus(UserDocumentsReviewByCashierPUTV2Req.CashierReviewStatus.PASS);
            approveAuditPreSignedReview(req, memberId);
        }
    }
}
