package com.carplus.subscribe.enums;

import com.carplus.subscribe.model.notify.maac.BaseDataDTO;
import com.carplus.subscribe.model.notify.maac.data.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum NotifyCategory {
    /**
     * 出車提醒
     */
    SR100("已訂車", "", null),
    /**
     * 用車結束
     */
    SR101("已還車", "", null),
    /**
     * IVR
     */
    IVR("關懷名單", "", null),
    /**
     * INVENTORY_WARN
     */
    INVT_WARN("警示", "", null),
    /**
     * 訂閱車訂單完成
     */
    SUB_COMPLETE,
    /**
     * 通知財務
     */
    TO_FINANCE,
    /**
     * 通支契館
     */
    TO_CONTRACT,
    /**
     * 訂閱車授信成功
     */
    SUB_CREDIT_SUCCESS,
    /**
     * 訂閱車授信失敗
     */
    SUB_CREDIT_FAIL,
    /**
     * 訂閱車授信需求
     */
    SUB_CREDIT_DEMAND,
    /**
     * 訂閱車取消訂單
     */
    SUB_CANCEL_ORDER("", "", CancelOrderDataDTO.class),
    /**
     * 訂閱車續約提醒
     */
    SUB_RENEW_CALL("", "", RenewCallOrderDataDTO.class),
    /**
     * 訂閱車續約確認
     */
    SUB_RENEW_CONFIRM,
    /**
     * 訂閱不續約確認
     */
    SUB_NOT_RENEW_CONFIRM,
    /**
     * 訂閱訂單異動提醒通知(出車時間、出車&還車站所)
     */
    SUB_UPDATE_ORDER("", "", ModifyOrderDataDTO.class),
    /**
     * 訂閱訂單已還車通知
     */
    SUB_RETURN_COMPLETE,
    /**
     * 訂閱訂單已還車未結案
     */
    SUB_RETURN_NOT_CLOSE,
    /**
     * 訂閱訂單已還車未結案請求
     */
    SUB_RETURN_NOT_CLOSE_REQUEST,
    /**
     * 訂閱訂單已還車未結案總結
     */
    SUB_RETURN_NOT_CLOSE_SUMMARY,
    /**
     * 訂閱已出車未還車總結
     */
    SUB_DEPART_NOT_RETURN_SUMMARY,
    /**
     * 訂閱出車異常
     */
    SUB_DEPART_ABNORMAL,
    /**
     * 訂單逾期未結算
     */
    SUB_MONTHLY_FEE_UNPAID,
    /**
     * 非營業出車提醒 - 將出車提醒
     */
    NBORDER_WILL_DEPART("即將出車提醒", "", null),
    /**
     * 非營業出車提醒 - 應還車提醒
     */
    NBORDER_NEED_RETURN("應還車提醒", "", null),
    /**
     * 訂閱日結失敗提醒
     */
    SUB_DAILY_CLOSED_FAIL,
    /**
     * 訂閱車保證金人工退款提醒
     */
    SUB_MANUAL_REFUND,
    /**
     * 遠通出車失敗
     */
    ETAG_DEPART_FAIL,
    /**
     * CRS新增車輛
     */
    ADD_CAR_FROM_CRS,
    /**
     * 車輛管制
     */
    CAR_CHANGE_LIMIT,
    /**
     * 長租約取消
     */
    LRENTAL_CANCEL,
    /**
     * 長租約異動
     */
    LRENTAL_CHANGE,
    /**
     * 投保客戶
     */
    APPLY_INSURANCE,
    /**
     * 投保結束
     */
    CANCEL_INSURANCE,
    /**
     * 投保提醒
     */
    INSURANCE_REMIND,
    /**
     * 日結異常
     */
    CHECKOUT_FAIL,
    /**
     * 保證金繳納成功
     */
    SECURITY_DEPOSIT_PAID("", "", SecurityDepositPaidDataDTO.class),
    /**
     * 每期費用開放繳款
     */
    OPEN_FOR_PAY_STAGE_FEE("", "", OpenForPayStageFeeDataDTO.class),
    /**
     * TapPay退款成功
     */
    TAPPAY_REFUND_SUCCESS("", "", RefundSuccessDataDTO.class),
    /**
     * 訂閱確認收訂通知
     */
    SUB_ORDER_RECEIVED_CONFIRMATION,
    /**
     * 出車中換車
     */
    SUB_REPLACE_CAR_SUCCESS;

    private String orderDesc;
    private String emailDesc2C;
    private Class<? extends BaseDataDTO> baseDataDtoType;
}
