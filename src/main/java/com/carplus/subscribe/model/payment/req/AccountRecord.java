package com.carplus.subscribe.model.payment.req;

import com.carplus.subscribe.enums.AccountType;
import com.carplus.subscribe.enums.PayFor;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Data
public class AccountRecord {

    @Schema(description = "帳目編號")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Schema(description = "舊短租收支主檔序號")
    private Integer collectAuto;

    /**
     * 訂單編號
     */
    @JsonIgnore
    private String orderNo;

    /**
     * 訂單費用資訊編號
     */
    private List<Integer> orderPriceInfoIds;

    /**
     * db: 該收支淨收金額<br>
     */
    @Schema(description = "金額", required = true)
    @NotNull
    private Integer amount;

    /**
     * req: 總退款金額
     */
    private Integer refundAmount = 0;

    /**
     * db: 總退款金額
     */
    @Schema(description = "總退款金額")
    private Integer totalRefundAmount = 0;

    @Schema(description = "收退款種類 Credit: 信用卡, Cash: 現金, Remit: 匯款, Check: 支票", required = true)
    @NotNull
    private AccountType accountType;

    @Schema(description = "支付目的 Depart: 出車款, Return: 還車款, Accident: 車損款, Other: 其他款項, All: 所有款項", required = true)
    @NotNull
    private PayFor payFor;

    @Schema(description = "站所代碼")
    private String stationCode;

    @Schema(description = "TapPay 查帳與退款用 id")
    private String tradeId;

    @Schema(description = "刷卡方式 1: EDC-台新銀, 2: EDC-聯信(AE), 10: EDC-聯信(花旗), 6: 傳刷-台新銀, 7: 網路刷卡-中信銀, 11: 網路刷卡-台新銀, 12: 簡訊刷卡-台新銀, 14: 中信銀Tappay, 15: 台新銀Tappay , 16: 聯信Tappay, 3: 手刷-台新銀, 4: 手刷-聯信, 5: 手刷-匯豐, 13: 台新銀行後台")
    private Integer chargeType;

    @Schema(description = "信用卡號前六後四")
    private String cardNumber;

    @Schema(description = "信用卡授權碼")
    private String authCode;

    @Schema(description = "匯款人")
    private String remitter;

    @Schema(description = "匯款帳號")
    private String remitAccCode;

    @Schema(description = "匯款編號")
    private Long remitNo;

    @Schema(description = "支票號碼")
    private String checkNo;

    @Schema(description = "開票人")
    private String drawer;

    @Schema(description = "票據指定銀行(銀行名稱)")
    private String checkAppointBack;

    @Schema(description = "甲存帳號")
    private String checkingAccNo;

    @Schema(description = "票據到期日")
    private Date checkDueDate;

    @Schema(description = "銀行交易序號")
    private String transactionNumber;

    @Schema(description = "是否刪除")
    @JsonProperty("isDeleted")
    private boolean isDeleted;

    /**
     * req: 總收款金額（不包含退款）
     */
    @Schema(description = "應退原始收款金額")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Integer originalAmount = 0;

    /**
     * 建立日期時間
     */
    @Column(name = "createDate", insertable = false, updatable = false)
    private Date createDate;

    /**
     * 更新日期時間
     */
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private Date updateDate;

    public Integer getRefundAmount() {
        return Optional.ofNullable(refundAmount).orElse(0);
    }

    public Integer getTotalRefundAmount() {
        return Optional.ofNullable(totalRefundAmount).orElse(0);
    }

    public Integer getOriginalAmount() {
        return Optional.ofNullable(originalAmount).orElse(0);
    }
}
