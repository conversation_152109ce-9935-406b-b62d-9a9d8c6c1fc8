env: local
service: subscribe
logging:
  config: classpath:logback-dev.xml
  level:
    com.carplus.authorization.mapper: debug
carplus:
  p6spy:
    modulelist: com.p6spy.engine.spy.P6SpyFactory,com.p6spy.engine.logging.P6LogFactory,com.p6spy.engine.outage.P6OutageFactory
    dateformat: yyyy-MM-dd HH:mm:ss.SSS
    appender: com.carplus.subscribe.config.p6spy.appender.Slf4JLogger
    executionThreshold: 0 #超過此秒數則顯示SQL
    outagedetection: true #開啟slow sql log
    outagedetectioninterval: 2 #超過此秒數則打印error訊息
    logMessageFormat: com.p6spy.engine.spy.appender.CustomLineFormat
    customLogMessageFormat: '%(category) | Execution Time:%(executionTime)ms | Execution SQL:%(sql)'
    excludecategories: info,debug,result,commit,resultset
  redis:
    enabled: true
    host: 127.0.0.1
    port: 6379
  service:
    srental: https://srentalapi.api.int.car-plus.cool
    lrental: https://lrentald.internalapi.int.car-plus.cool
    gosmart: https://gosmart.api.int.car-plus.cool # https://gosmart.api.int.car-plus.cool
    auth: https://auth.api.int.car-plus.cool # https://auth.api.int.car-plus.cool
    audit: https://audit.api.int.car-plus.cool
    authority: https://authority.api.int.car-plus.cool # https://authority.api.int.car-plus.cool
    push: https://push.api.int.car-plus.cool # https://push.api.int.car-plus.cool
    coupon: https://coupon.api.int.car-plus.cool # https://coupon.api.int.car-plus.cool
    finance: https://finance.internalapi.int.car-plus.cool # https://finance.internalapi.int.car-plus.cool
    payment: http://payment.api.int.car-plus.cool # http://payment.api.int.car-plus.cool
    shorter: https://s.int.car-plus.cool
    cashier: https://cashier.int.car-plus.cool
    fe-carplus: https://int.car-plus.cool
    fe-official: https://www.int.car-plus.cool
    fe-s-official: https://s.int.car-plus.cool
    edm: https://edm.int.car-plus.cool # https://edm.int.car-plus.cool
    fin-service-bus: https://finservicebus.api.int.car-plus.cool
    crs: https://crs.api.int.car-plus.cool
    sensor: https://gateway.api.int.car-plus.cool/partner/sensors
    insurance: https://carinsurance.api.int.car-plus.cool
    taskservice: https://taskservice.api.int.car-plus.cool

  rabbitmq:
    enabled: true
    primary:
      host: **************
      port: 5672
      username: guest
      password: MDenW1Ojsw805SnPm97FpatyOs360HL4
      listener:
        manual-ack-mode: true
        concurrency: 10
        max-concurrency: 10

spring:
  datasource:
    mysql:
      jdbc-url: ***************************************************************************************************************************************************
      username: rd
      password: 72*Q3wMQN6&Cn@%O
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      max-lifetime: 30000
      connection-timeout: 10000
  jpa:
    show-sql: false
    open-in-view: false

springdoc:
  swagger-ui:
    path: /doc/swagger-ui.html
    operationsSorter: alpha
    disable-swagger-default-url: true
    groups-order: desc
    docExpansion: none
  default-produces-media-type: application/json

task:
  notify:
    push:
      sr100: 0/30 * * * * ?

lrental:
  userId: K2185

invoice:
  allow-editable:
    check-sysDate: false

mattermost:
  enabled: true
  webhook: https://im.car-plus.cool/hooks/eb957ohrmfbgiqxs8odongw6ch # Webhook-Test


station:
  subscribe: 233

authority:
  test: # 簡訊/mail授權使用
    enabled: true # 是否起用測試
    member-id-L1: K2456 # 協理
    member-id-L2: K2456 # 副理
    member-id-N1: K2456 # 北1區督導
    member-id-N2: K2456 # 北2區督導
    member-id-C: K2456 # 中區督導
    member-id-S: K2456 # 南區督導
    member-id-E: K2456 # 東區督導
    member-id-Master: K2456 # 站長
    member-id-CDM: K2456 # 授信管理課(課)
    member-id-SCARM: K2456 # 訂閱車業務管理課(課)
  department-code:
    L1: F03003 # 短租事業群(處)
    L2: F03003 # 門市部(部)
    N1: F03003 # 站所北ㄧ區
    N2: F03003 # 站所北二區
    C: F03003 # 站所中區
    S: F03003 # 站所南區
    E: F03003 # 站所東區
    CDM: F03003 # 授信管理課(課)
    SCARM: F03003 # 訂閱車業務管理課(課)
    SCARD: F03003 # 訂閱車業務管理部(部)
    SCARM2: F03003 # 訂閱車業務管理部(課)
    SCARG: F03003 # 汽車訂閱事業群 B00200

audit:
  timeout: 30000

cpmrmst:
  url: https://cpmrmst.beta.car-plus.cool

notifications:
  test:
    enabled: true
    to: <EMAIL>
    cc: <EMAIL>
  emails:
    dispatch-center: <EMAIL> #<EMAIL>
    security-deposit: <EMAIL>
    contract-manager: <EMAIL> #<EMAIL>
    audit-center: <EMAIL> #<EMAIL>
    sub: <EMAIL> #<EMAIL>
    sub-business: <EMAIL> #<EMAIL>
    finance: <EMAIL> #<EMAIL>,<EMAIL>

feign:
  client:
    config:
      default:
        connectTimeout: 30000
        readTimeout: 30000
        loggerLevel: FULL
        decode404: true

gcs:
  url: https://warehouse.int.car-plus.cool