package com.carplus.subscribe.model.priceinfo;

import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.exception.SubscribeException;

import java.util.List;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.ORDER_PRICE_INFO_MILEAGE_FEE_NOT_FUND;

public class MileagePriceInfoWrapper extends PriceInfoWrapper {

    protected MileagePriceInfoWrapper(List<OrderPriceInfo> value) {
        super(value);
    }

    public double getMileage() {
        return orderPriceInfoList.stream().findAny().map(OrderPriceInfo::getInfoDetail).map(PriceInfoDetail::getMileageFee)
            .orElseThrow(() -> new SubscribeException(ORDER_PRICE_INFO_MILEAGE_FEE_NOT_FUND));
    }
}
