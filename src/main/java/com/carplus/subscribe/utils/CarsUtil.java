package com.carplus.subscribe.utils;

import carplus.common.enums.CRS.CRS;
import carplus.common.response.exception.BadRequestException;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.db.mysql.entity.contract.MainContract;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.enums.BuIdEnum;
import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.enums.CompanyIdentifier;
import com.carplus.subscribe.enums.ContractStatus;
import com.carplus.subscribe.model.crs.CarBaseInfoSearchResponse;
import com.carplus.subscribe.model.request.CarsAddRequest;
import org.springframework.lang.NonNull;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static com.carplus.subscribe.enums.CompanyIdentifier.CARPLUS;

public class CarsUtil {

    private CarsUtil() {
    }

    /**
     * 1. 將車號第2,3碼搬到最後面
     * 2. 所有英文轉數字 , A->01 , B->02 .... Y->25 , Z->26
     **/
    @NonNull
    public static String encodePlateNo(@NonNull String plateNo) {
        StringBuilder encode = new StringBuilder();
        String[] plateNos = plateNo.toUpperCase().split("-");
        int size = plateNos[0].length();
        String platNo = plateNos[0].substring(0, size - 2) + plateNos[1] + plateNos[0].substring(size - 2);
        for (char c : platNo.toCharArray()) {
            if (!Character.isDigit(c)) {
                encode.append(StringUtils.pad(String.valueOf((int) c - 64), '0', 2));
            } else {
                encode.append(c);
            }
        }
        return encode.toString();
    }

    public static boolean isCarsNotEmpty(List<Cars> cars) {
        return !cars.isEmpty();
    }

    /**
     * 過濾被刪除、deprecated車輛
     */
    public static List<Cars> getCarsWithoutDeletedAndDeprecated(List<Cars> crsCars) {
        return crsCars.stream()
            .filter(car -> !car.getIsDeleted() || !car.getLaunched().equals(CarDefine.Launched.deprecate))
            .collect(Collectors.toList());
    }

    /**
     * 取得既有車牌最後一筆資料: Key=Value : CrsCarNo=Cars <br/>
     * {@param cs from  } <br/>
     * {@link #getCarsWithoutDeletedAndDeprecated(List)} <br/>
     */
    public static Map<Integer, Cars> getLatestCarInfo(List<Cars> cs) {
        Map<Integer, Cars> latestCarInfo = new HashMap<>();
        cs.stream()
            .max(Comparator.comparing(Cars::getUpdateDate))
            .ifPresent(car -> latestCarInfo.put(car.getCrsCarNo(), car));
        return latestCarInfo;
    }

    /**
     * 非虛擬車且為格上車 以外須 validate required fields
     */
    public static void setAndValidateNonVirtualCarRequiredFields(Cars car, CarsAddRequest request) {
        if (request.getDisplacement() == null || request.getDisplacement().compareTo(BigDecimal.ZERO) < 0) {
            throw new BadRequestException("[排氣量不可為空或小於 0]");
        }
        if (request.getEnergyType() == null) {
            throw new BadRequestException("[能源類別不可為空]");
        }
        if (request.getSeat() <= 0) {
            throw new BadRequestException("[座位數不可小於等於 0]");
        }
        if (request.getMfgYear() == null || request.getMfgYear().length() != 4) {
            throw new BadRequestException("[出廠年份不可為空或格式不正確]");
        }
        if (request.getCurrentMileage() == null || request.getCurrentMileage() < 0) {
            throw new BadRequestException("[里程數不可小於 0]");
        }
        car.setDisplacement(request.getDisplacement());
        car.setEnergyType(request.getEnergyType());
        car.setSeat(request.getSeat());
        car.setMfgYear(request.getMfgYear());
        car.setCurrentMileage(request.getCurrentMileage());
        car.setCrsCarNo(-1);
    }

    /**
     * 出車中換車前置條件檢核 <br/>
     * - 替代車是不是虛擬車? <br/>
     * - 汰換車與替代車是否相同車牌號碼? <br/>
     * - 主約車牌號碼與汰換車車牌號碼是否相符? <br/>
     * - 主約狀態是不是GOING(1)? <br/>
     * - 替代車狀態是不是空車(Free(00))? <br/>
     * - 替代車上架狀態(launched)是不是(open, close)? <br/>
     * - 替代車 buId 是不是訂閱(4)或長租(1)? <br/>
     * - 汰換車與替代車兩者訂閱方案是否相等? <br/>
     * - 汰換車當前里程數是否小於訂單出車里程數? <br/>
     * - 替代車出車里程數是否小於車籍表(cars)當前里程數? <br/>
     * - 如替代車為訂閱車 : <br/>
     * i. 是否至少選擇一個代步車方案? <br/>
     * ii. 代步車方案是否同時選擇 0 和其他(1~6)? <br/>
     * iii. 是否請填寫長租契約備註? <br/>
     */
    public static List<String> replaceCarAudit(MainContract mainContract, Orders order, List<String> lrContractReplaceCodes, String lrContractMemo,
                                               Cars inCar, Integer inCarStartMileage, CarBaseInfoSearchResponse inCarCrsInfo,
                                               Cars outCar, Integer outCarEndMileage) {
        List<String> errMessages = new ArrayList<>();
        boolean isCarPlusCar = isCarPlusCar(outCar.getVatNo());
        boolean areInAndOutPlateNoTheSame = Objects.equals(outCar.getPlateNo(), inCar.getPlateNo());
        boolean areOrderAndReqPlateNoTheSame = Objects.equals(mainContract.getPlateNo(), outCar.getPlateNo());
        boolean isMainContractGoing = Objects.equals(ContractStatus.codeOfValue(mainContract.getStatus()), ContractStatus.GOING);
        boolean isInCarStatusFree = Objects.equals(CarDefine.CarStatus.of(inCar.getCarStatus()), CarDefine.CarStatus.Free);
        boolean isInCarSub = !isCarPlusCar || Objects.equals(inCarCrsInfo.getBuId(), BuIdEnum.subscribe.getCode());
        boolean isInCarLaunchedOpenOrClose = Objects.equals(inCar.getLaunched(), CarDefine.Launched.open) || Objects.equals(inCar.getLaunched(), CarDefine.Launched.close);
        boolean isInCarBuIdIsSubOrLRental = !isCarPlusCar || isInCarSub || Objects.equals(inCarCrsInfo.getBuId(), BuIdEnum.lRental.getCode());
        boolean isSameSubscribeLevel = Objects.equals(inCar.getSubscribeLevel(), outCar.getSubscribeLevel());
        int outMileageCompared = Integer.compare(outCarEndMileage, order.getDepartMileage());
        int replacedMileageCompared = Integer.compare(inCarStartMileage, inCar.getCurrentMileage());

        if (inCar.isVirtualCar()) {
            errMessages.add("替代車不能為虛擬車");
        }
        if (!inCar.getVatNo().equalsIgnoreCase(outCar.getVatNo())) {
            errMessages.add("汰換車與替代車車輛所有權公司不同");
        }
        if (areInAndOutPlateNoTheSame) {
            errMessages.add("汰換車與替代車相同車牌號碼");
        }
        if (!areOrderAndReqPlateNoTheSame) {
            errMessages.add("主約車牌號碼與汰換車車牌號碼不相符");
        }
        if (!isMainContractGoing) {
            errMessages.add("主約狀態不是 GOING(1)");
        }
        if (!isInCarStatusFree) {
            errMessages.add("替代車狀態不是空車(Free(00))");
        }
        if (!isInCarLaunchedOpenOrClose) {
            errMessages.add("替代車上架狀態(launched)不是(open, close)");
        }
        if (!isInCarBuIdIsSubOrLRental) {
            errMessages.add("替代車 buId 不是訂閱(4)或長租(1)");
        }
        if (!isSameSubscribeLevel) {
            errMessages.add("汰換車與替代車兩者訂閱方案並不相等");
        }
        if (outMileageCompared < 0) {
            String format = String.format("汰換車當前里程數小於訂單出車里程數(%d)", order.getDepartMileage());
            errMessages.add(format);
        }
        if (replacedMileageCompared < 0) {
            String format = String.format("替代車出車里程數小於車籍表當前里程數(%d)", inCar.getCurrentMileage());
            errMessages.add(format);
        }

        // 特定情況檢查: 替換車若符合需要提供預期領牌照日條件，則拒絕換車 (QA-19445)
        // licenseStatus != 0 || licenseType != 1 ||
        // company is not in (CompanyIdentifier.CARPLUS)
        boolean checkLicense = isCarPlusCar
            && (!CRS.LicenseStatus.BUY.getCode().equals(inCarCrsInfo.getCarLicense().getLicenseStatus())
            || !CRS.LicenseType.A.getCode().equals(CRS.LicenseType.ofDesc(inCarCrsInfo.getCarLicense().getLicenseCarType()).getCode())
            || !CompanyIdentifier.contains(CARPLUS, inCarCrsInfo.getCarBase().getCompanyNo()));

        if (checkLicense) {
            errMessages.add("替換車因符合下述任一條件(牌種非甲牌,狀態非購入,公司別非格上)，需通知投保強制險。");
        }

        // 特定情況檢查: [BuId=1]換[BuId=4] || [BuId=4]換[BuId=4]
        // 代步車方案必填至少一個選擇
        if (isInCarSub && isCarPlusCar) {
            if (lrContractReplaceCodes.isEmpty()) {
                errMessages.add("替代車為訂閱車，則至少選擇一個代步車方案");
            }
            Set<String> reqSet = new HashSet<>(lrContractReplaceCodes);
            Set<String> targetSet = new HashSet<>(Arrays.asList("1", "2", "3", "4", "5", "6"));
            boolean isContainsAtLeastOne = targetSet.stream().anyMatch(reqSet::contains);
            if (lrContractReplaceCodes.contains("0") && isContainsAtLeastOne) {
                errMessages.add("替代車為訂閱車，代步車方案不可同時選擇 0 和其他");
            }
            if (lrContractMemo.isEmpty()) {
                errMessages.add("替代車為訂閱車，請填寫長租契約備註");
            }
        }

        return errMessages;
    }

    /**
     * 還車 - 計算還車里程數
     */
    public static Integer calculateCarMileageFee(double mileageFee, Integer orderDepartMileage, Integer carEndMileage) {
        int useMileage = carEndMileage - orderDepartMileage;
        return  BigDecimal.valueOf(mileageFee).multiply(BigDecimal.valueOf(useMileage)).setScale(0, RoundingMode.HALF_UP).intValue();
    }

    /**
     * OrderPriceInfo <br/>
     * 實際還車時間 / 預期還車時間 二取一
     */
    public static Instant getLastPayDate(Orders order) {
        return DateUtil.convertToEndOfInstant(Optional.ofNullable(order.getEndDate()).orElse(order.getExpectEndDate()));
    }

    /**
     * 建立長租 - 代步車方案處理
     */
    public static String replaceCodesHandle(List<String> replaceCodes) {
        return Optional.ofNullable(replaceCodes)
            .map(codes -> codes.isEmpty() ? "0" : String.join("", codes))
            .orElse("0");
    }

    public static boolean isCarPlusCar(String vatNo) {
        return Optional.ofNullable(vatNo).map(v -> v.equalsIgnoreCase(CarPlusConstant.CARPLUS_COMPANY_VAT_NO)).orElse(false);
    }
}
