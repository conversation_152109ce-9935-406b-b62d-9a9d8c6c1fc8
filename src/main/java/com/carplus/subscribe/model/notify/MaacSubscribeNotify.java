package com.carplus.subscribe.model.notify;

import com.carplus.subscribe.model.notify.maac.BaseDataDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@Schema(description = "訂閱車MAAC通知")
public class MaacSubscribeNotify extends SubscribeNotify {
    private final Integer pnpSettingId;
    private Integer linePushTemplateId;
    private String lastPayDate;
    private String contractExpiredDate;
    private String renewUrl;
    private String carInfo;
    private Class<? extends BaseDataDTO> baseDataDtoType;
}
