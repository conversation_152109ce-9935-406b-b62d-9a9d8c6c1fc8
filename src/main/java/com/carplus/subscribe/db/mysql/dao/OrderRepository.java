package com.carplus.subscribe.db.mysql.dao;

import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.entity.cars.CarBrand;
import com.carplus.subscribe.db.mysql.entity.cars.CarModel;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.db.mysql.entity.contract.Contract;
import com.carplus.subscribe.db.mysql.entity.contract.MainContract;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.db.mysql.entity.invoice.Invoices;
import com.carplus.subscribe.enums.OrderStatus;
import com.carplus.subscribe.model.request.order.OrdersCriteria;
import com.carplus.subscribe.model.response.order.OrderDTO;
import com.carplus.subscribe.utils.DateUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.*;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Repository
public class OrderRepository extends SimpleJpaRepository<Orders, String> {

    private final EntityManager em;

    public OrderRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(Orders.class, em);
        this.em = em;
    }

    /**
     * 取得最後的訂單編號
     */
    public String getLastOrderNo() {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<String> cq = cb.createQuery(String.class);
        Root<Orders> root = cq.from(Orders.class);
        cq.select(root.get("orderNo")).orderBy(cb.desc(root.get("orderNo")));
        try {
            return em.createQuery(cq).setMaxResults(1).getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }

    /**
     * 取得當日的訂單編號
     */
    public List<String> getToDayOrderNos() {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<String> cq = cb.createQuery(String.class);
        Root<Orders> root = cq.from(Orders.class);
        cq.select(root.get("orderNo")).where(cb.greaterThanOrEqualTo(root.get("createDate"), DateUtil.convertToStartOfInstant(new Date())));
        return em.createQuery(cq).getResultList();
    }

    public long count(@NonNull OrdersCriteria queryRequest) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Long> cq = cb.createQuery(Long.class);
        prepareQuery(cb, cq, queryRequest, true);
        return em.createQuery(cq).getResultList().size();
    }

    public List<OrderDTO> findBySearch(@NonNull OrdersCriteria queryRequest,
                                       @Nullable Integer limit, @Nullable Integer offset) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Object[]> cq = cb.createQuery(Object[].class);
        prepareQuery(cb, cq, queryRequest, false);
        TypedQuery<Object[]> query = em.createQuery(cq);
        if (limit != null) {
            query.setMaxResults(limit);
        }
        if (offset != null) {
            query.setFirstResult(offset);
        }
        return query.getResultList().stream().map(o -> new OrderDTO((Orders) o[0], (Contract) o[1], (MainContract) o[2], (CarModel) o[3], (CarBrand) o[4], (Cars) o[5])).collect(Collectors.toList());
    }

    @NonNull
    public void prepareQuery(CriteriaBuilder cb, CriteriaQuery<?> cq, @NonNull OrdersCriteria queryRequest, boolean isCount) {
        Root<Orders> ordersRoot = cq.from(Orders.class);

        Join<Orders, Contract> contractJoin = ordersRoot.join("contract", JoinType.LEFT);
        Join<Contract, MainContract> mainContractJoin = contractJoin.join("mainContract", JoinType.LEFT);
        Join<MainContract, Cars> carsJoin = mainContractJoin.join("cars", JoinType.LEFT);
        Join<Cars, CarModel> carModelJoin = carsJoin.join("carModel", JoinType.LEFT);
        Join<CarModel, CarBrand> carBrandJoin = carModelJoin.join("carBrand", JoinType.LEFT);
        Join<Orders, Invoices> invoicesJoin = null;

        List<Predicate> predicateList = new ArrayList<>();

        if (Objects.nonNull(queryRequest.getStationCode()) && !queryRequest.getStationCode().isEmpty()) {
            predicateList.add(cb.and(cb.or(mainContractJoin.get("departStationCode").in(queryRequest.getStationCode()), mainContractJoin.get("returnStationCode").in(queryRequest.getStationCode()))));
        }
        if (Objects.nonNull(queryRequest.getStatus()) && !queryRequest.getStatus().isEmpty()) {
            predicateList.add(cb.and(ordersRoot.get("status").in(queryRequest.getStatus())));
        }
        if (StringUtils.isNotBlank(queryRequest.getMainContractNo())) {
            predicateList.add(cb.equal(mainContractJoin.get("mainContractNo"), queryRequest.getMainContractNo()));
        }
        if (StringUtils.isNotBlank(queryRequest.getContractNo())) {
            predicateList.add(cb.equal(contractJoin.get("contractNo"), queryRequest.getContractNo()));
        }
        if (StringUtils.isNotBlank(queryRequest.getOrderNo())) {
            predicateList.add(cb.equal(ordersRoot.get("orderNo"), queryRequest.getOrderNo()));
        }
        if (Objects.nonNull(queryRequest.getAcctId()) && !queryRequest.getAcctId().isEmpty()) {
            predicateList.add(cb.and(mainContractJoin.get("acctId").in(queryRequest.getAcctId())));
        }
        if (queryRequest.getPlateNo() != null && !queryRequest.getPlateNo().isEmpty()) {
            predicateList.add(mainContractJoin.get("plateNo").in(queryRequest.getPlateNo()));
        }
        if (StringUtils.isNotBlank(queryRequest.getInvNo())) {
            invoicesJoin = ordersRoot.join("invoices", JoinType.LEFT);
            predicateList.add(cb.equal(invoicesJoin.get("invNo"), queryRequest.getInvNo()));
        }
        if (Objects.nonNull(queryRequest.getRenewType())) {
            predicateList.add(cb.equal(ordersRoot.get("renewType"), queryRequest.getRenewType()));
        }
        if (Objects.nonNull(queryRequest.getCreateFrom()) && Objects.nonNull(queryRequest.getCreateTo())) {
            predicateList.add(cb.between(ordersRoot.get("securityDepositDate"), queryRequest.getCreateFrom().toInstant(), queryRequest.getCreateTo().toInstant()));
        }
        if (Objects.nonNull(queryRequest.getExpectDepartFrom()) && Objects.nonNull(queryRequest.getExpectDepartTo())) {
            predicateList.add(cb.between(ordersRoot.get("expectStartDate"), queryRequest.getExpectDepartFrom().toInstant(), queryRequest.getExpectDepartTo().toInstant()));
        }
        if (Objects.nonNull(queryRequest.getDepartFrom()) && Objects.nonNull(queryRequest.getDepartTo())) {
            predicateList.add(cb.between(ordersRoot.get("startDate"), queryRequest.getDepartFrom().toInstant(), queryRequest.getDepartTo().toInstant()));
        }
        if (Objects.nonNull(queryRequest.getExpectReturnFrom()) && Objects.nonNull(queryRequest.getExpectReturnTo())) {
            predicateList.add(cb.between(ordersRoot.get("expectEndDate"), queryRequest.getExpectReturnFrom().toInstant(), queryRequest.getExpectReturnTo().toInstant()));
        }
        if (Objects.nonNull(queryRequest.getReturnFrom()) && Objects.nonNull(queryRequest.getReturnTo())) {
            predicateList.add(cb.between(ordersRoot.get("endDate"), queryRequest.getReturnFrom().toInstant(), queryRequest.getReturnTo().toInstant()));
        }
        if (Objects.nonNull(queryRequest.getIsUnpaid())) {
            predicateList.add(cb.equal(ordersRoot.get("isUnpaid"), queryRequest.getIsUnpaid()));
        }
        if (Objects.nonNull(queryRequest.getIsNewOrder())) {
            predicateList.add(cb.equal(ordersRoot.get("isNewOrder"), queryRequest.getIsNewOrder()));
        }
        if (CollectionUtils.isNotEmpty(queryRequest.getMonth())) {
            predicateList.add(ordersRoot.get("month").in(queryRequest.getMonth()));
        }
        if (CollectionUtils.isNotEmpty(queryRequest.getCarVatNo())) {
            predicateList.add((carsJoin.get("vatNo").in(queryRequest.getCarVatNo())));
        }

        cq.where(predicateList.toArray(new Predicate[0]));
        if (isCount) {
            cq.select(ordersRoot.get("orderNo"));
        } else {
            cq.multiselect(ordersRoot, contractJoin, mainContractJoin, carModelJoin, carBrandJoin, carsJoin);
            cq.orderBy(cb.desc(ordersRoot.get("createDate")));
        }
    }

    /**
     * 拿取指定訂單狀態的訂單清單
     */
    public List<Orders> getOrderByStatus(OrderStatus orderStatus) {
        return findAll((Specification<Orders>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            predicateList.add(cb.equal(root.get("status"), orderStatus.getStatus()));
            return cb.and(predicateList.toArray(new Predicate[0]));
        });
    }

    /**
     * 取得前一期訂單
     */
    public Orders getPreviousOrders(String nextStageOrderNo) {
        return findAll((Specification<Orders>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            predicateList.add(cb.equal(root.get("nextStageOrderNo"), nextStageOrderNo));
            return cb.and(predicateList.toArray(new Predicate[0]));
        }).stream().findAny().orElse(null);
    }

    /**
     * 取得短租同一母約定單
     */
    public List<Orders> getOrdersBySrentalParentOrderNo(String parentOrderNo) {
        return findAll((Specification<Orders>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            predicateList.add(cb.equal(root.get("srentalParentOrderNo"), parentOrderNo));
            return cb.and(predicateList.toArray(new Predicate[0]));
        });
    }

    public List<Orders> getOrdersByAcctIds(List<Integer> acctIds) {
        if (CollectionUtils.isEmpty(acctIds)) {
            return new ArrayList<>();
        }
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Orders> cq = cb.createQuery(Orders.class);
        Root<Orders> ordersRoot = cq.from(Orders.class);

        Join<Orders, Contract> contractJoin = ordersRoot.join("contract", JoinType.LEFT);
        Join<Contract, MainContract> mainContractJoin = contractJoin.join("mainContract", JoinType.LEFT);
        List<Predicate> predicateList = new ArrayList<>();

        predicateList.add(cb.and(mainContractJoin.get("acctId").in(acctIds)));
        cq.select(ordersRoot).where(predicateList.toArray(new Predicate[0]));
        return em.createQuery(cq).getResultList();

    }

    /**
     * 返回訂單編號 list 符合以下提醒建立車籍契約及投保條件
     * 1. status = 10(已訂車)
     * 2. expectStartDate = 未來 3 天內
     * 3. lrentalContractNo is null
     * 4. 車輛庫位 ≠ 長租 (CRS)
     */
    public List<Orders> getOrderNoForRemindContractAndInsurance() {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Orders> cq = cb.createQuery(Orders.class);
        Root<Orders> ordersRoot = cq.from(Orders.class);

        cq.where(
            cb.and(
                cb.equal(ordersRoot.get("status"), OrderStatus.BOOKING.getStatus()),
                cb.lessThan(ordersRoot.get("expectStartDate"), DateUtil.convertToStartOfInstant(Instant.now().plus(4, ChronoUnit.DAYS))),
                cb.isNull(ordersRoot.get("lrentalContractNo"))
            )
        );
        return em.createQuery(cq).getResultList();
    }
}
