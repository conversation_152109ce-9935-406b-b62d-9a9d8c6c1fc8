package com.carplus.subscribe.service;

import com.carplus.subscribe.db.mysql.dao.MainContractRepository;
import com.carplus.subscribe.db.mysql.entity.contract.Contract;
import com.carplus.subscribe.db.mysql.entity.contract.MainContract;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.enums.SubscribeRenewMonth;
import com.carplus.subscribe.model.order.Remark;
import com.carplus.subscribe.model.request.contract.OrderRenewRequest;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;
import java.util.Objects;

import static com.carplus.subscribe.enums.OrderStatus.CLOSE;
import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class ContractServiceTest {

    @Autowired
    private ContractService contractService;
    @Autowired
    private ContractLogic contractLogic;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private OrderService orderService;

    @Autowired
    private MainContractRepository mainContractRepository;

    @Test
    public void create() {

    }

    @Test
    public void findAll() throws JsonProcessingException {
        List<Contract> list = contractService.findAll();
        System.out.println(objectMapper.writeValueAsString(list.get(0).getOrders().get(0)));
    }

    @Test
    public void renewOrder() throws JsonProcessingException {
        OrderRenewRequest orderRenewRequest = new OrderRenewRequest();
        orderRenewRequest.setMonth(SubscribeRenewMonth.THREE);
        orderRenewRequest.setAcctId(632751);
        System.out.println(objectMapper.writeValueAsString(contractLogic.renewOrder("M202310176839", orderRenewRequest, true, "EC0182", true, null)));
        objectMapper.writeValueAsString(contractLogic.renewOrder("M202310176839", orderRenewRequest, true, "EC0182", true, null));


    }

    @Test
    public void isRenewable() {
        contractLogic.isRenewable("U2024062600002");
    }

    @Test
    public void getMainContractByNo() {
        Orders order = orderService.getOrder("M202310249027");
        MainContract mainContract = mainContractRepository.getMainContractByNo(order.getContract().getMainContract().getMainContractNo(), true);
        Orders departOrders =
            mainContract.getContracts().stream().map(c -> c.getOrders().stream().filter(o -> o.getStatus() == CLOSE.getStatus() && !o.getOrderNo().equals(order.getOrderNo())).findAny().orElse(null)).filter(Objects::nonNull).findAny().orElse(null);
        System.out.println("");
    }


    @Test
    public void getMainContractAndContractAndOrdersByMainContractNoAndSort_shouldReturnMainContractWithRemarksSorted() {
        MainContract mainContract = contractService.getMainContractAndContractAndOrdersByMainContractNoAndSort("U2025092600145");
        assertNotNull(mainContract);
        assertNotNull(mainContract.getContracts());
        assertFalse(mainContract.getContracts().isEmpty());

        for (Contract contract : mainContract.getContracts()) {
            assertNotNull(contract);
            assertNotNull(contract.getOrders());
            assertFalse(contract.getOrders().isEmpty());

            for (Orders order : contract.getOrders()) {
                assertNotNull(order);
                List<Remark> sortedRemarks = order.getRemarks();
                assertNotNull(sortedRemarks);
                assertFalse(sortedRemarks.isEmpty());

                if (sortedRemarks.size() > 1) {
                    for (int i = 0; i < sortedRemarks.size() - 1; i++) {
                        Remark current = sortedRemarks.get(i);
                        Remark next = sortedRemarks.get(i + 1);

                        // 驗證排序邏輯
                        if (current.getCreateTime() == null) {
                            assertNull(next.getCreateTime(), "如果當前 createTime 為 null, 則下一個 createTime 也必須為 null");
                        } else if (next.getCreateTime() != null) {
                            assertTrue(current.getCreateTime().after(next.getCreateTime()), "如果兩個 createTime 都不為 null, 則當前 createTime 必須晚於下一個 createTime");
                        }
                    }
                }
            }
        }
    }
}
