package com.carplus.subscribe.db.mysql.entity;

import carplus.common.enums.etag.ETagPayFlow;
import com.carplus.subscribe.db.mysql.LazyFieldsFilter;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrderPriceInfo;
import com.carplus.subscribe.enums.CarPlusFleet;
import com.carplus.subscribe.enums.ETagPayment;
import com.carplus.subscribe.enums.NotPayableReason;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldNameConstants;

import javax.persistence.*;
import java.time.Instant;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

@Data
@Schema(description = "etag 相關資訊")
@Builder
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@NoArgsConstructor(access = AccessLevel.PUBLIC)
@FieldNameConstants
@Entity(name = "eTag_info")
public class ETagInfo extends GeneralEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    @JsonProperty("eTagAmt")
    @Schema(description = "應收金額")
    private Integer eTagAmt;
    @Schema(description = "遠通金額")
    private int fetcAmt;
    @Schema(description = "差別費率")
    private int fetcOffSetAmt;
    @Schema(description = "是否為經銷商&格上車")
    private Boolean dealerAndETagAccount = false;
    @Schema(description = "付款銀行別")
    @JsonProperty("eTagBankUnitCode")
    private String eTagBankUnitCode;
    @Schema(description = "已付實收金額, null=門市沒有登打到")
    private Integer paidETagAmt;
    @Schema(description = "車號判斷是否為合法通路(查資料庫有無建檔)")
    @JsonProperty("isValid")
    private Boolean isValid = false;
    @Schema(description = "是否出/還車遠通回傳 status F")
    @JsonProperty("isSuccess")
    private Boolean isSuccess;
    @JsonProperty("eTagPayment")
    @Schema(description = "收款方式")
    private ETagPayment eTagPayment;
    @Schema(description = "收據含通行明細")
    private boolean existETagDetail;
    @Schema(description = "不收款原因")
    private NotPayableReason notPayableReason;
    @Schema(description = "備註")
    private String remark;
    @Schema(description = "信用卡刷卡方式")
    private Integer creditBankAuto = 3; //參考mrms_sit.CreditBank
    @Schema(description = "EDC結帳批號")
    private String edcBatchNo;
    @Schema(description = "是否要收款")
    private boolean payabled;
    @Schema(description = "銀行交易序號")
    private String transactionNumber;
    @Schema(description = "鎖定編輯eTag金額")
    private boolean lockETagAmt = false;
    @Schema(description = "是否略過遠通還車")
    private boolean passReturnCar = false;
    @JsonProperty("eTagFlow")
    @Schema(description = "出還車流程節點")
    private Integer eTagFlow;
    @Schema(description = "付款判別")
    private Integer eTagPayFlow;
    @Schema(description = "格上車籍")
    @Enumerated(EnumType.STRING)
    private CarPlusFleet carPlusFleet;
    @Schema(description = "是否立賬")
    private boolean uploaded = false;
    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.REFRESH)
    @JoinColumn(name = "orderNo", referencedColumnName = "orderNo", insertable = false, updatable = false, nullable = false)
    @JsonIgnore
    @JsonInclude(value = JsonInclude.Include.CUSTOM, valueFilter = LazyFieldsFilter.class)
    private Orders order;
    private String orderNo;
    @OneToOne
    @JoinColumn(name = "orderPriceInfoId", referencedColumnName = "id", insertable = false, updatable = false)
    @JsonInclude(value = JsonInclude.Include.CUSTOM, valueFilter = LazyFieldsFilter.class)
    private OrderPriceInfo orderPriceInfo;
    private Integer orderPriceInfoId;
    @OneToOne
    @JoinColumn(name = "dealerOrderPriceInfoId", referencedColumnName = "tradeId", insertable = false, updatable = false)
    @JsonInclude(value = JsonInclude.Include.CUSTOM, valueFilter = LazyFieldsFilter.class)
    private DealerOrderPriceInfo dealerOrderPriceInfo;
    private String dealerOrderPriceInfoId;
    /**
     * 第幾期
     */
    private Integer stage;
    /**
     * 出車時間
     */
    private Instant departDate;
    /**
     * 還車時間
     */
    private Instant returnDate;

    /**
     * 舊短租契約編號
     */
    private String srentalContractNo;

    /**
     * 出車失敗代碼
     */
    private Integer departFailCode;
    /**
     * 還車失敗代碼
     */
    private Integer returnFailCode;

    /**
     * 出車失敗原因
     */
    private String departFailMsg;

    /**
     * 還車失敗原因
     */
    private String returnFailMsg;

    public void setDepartFail(String msg) {
        try {
            departFailCode = Integer.valueOf(msg.substring(0, 4));
            departFailMsg = msg.substring(4);
        } catch (Exception e) {
            departFailMsg = msg;
        }
    }

    public void setReturnFail(String msg) {
        try {
            returnFailCode = Integer.valueOf(msg.substring(0, 4));
            returnFailMsg = msg.substring(4);
        } catch (Exception e) {
            returnFailMsg = msg;
        }
    }

    public boolean isPaymentCompleted() {
        // 若eTagPayFlow = DONE% 直接視為付款完成
        Integer eTagPayFlow = getETagPayFlow();
        Set<Integer> completedStatuses = new HashSet<>(Arrays.asList(
            ETagPayFlow.DONE.getCode(),
            ETagPayFlow.DONE_NOT_NEED_PAY.getCode(),
            ETagPayFlow.DONE_PAYABLE_FALSE.getCode()
        ));
        return completedStatuses.contains(eTagPayFlow);
    }
}
