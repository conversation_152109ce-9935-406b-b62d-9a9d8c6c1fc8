package com.carplus.subscribe.service;

import carplus.common.utils.DateUtils;
import com.carplus.subscribe.db.mysql.dao.BuChangeLogRepository;
import com.carplus.subscribe.db.mysql.dao.MainContractRepository;
import com.carplus.subscribe.db.mysql.entity.BuChangeLog;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.db.mysql.entity.contract.IOrder;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.enums.BuChangeEnum;
import com.carplus.subscribe.enums.BuIdEnum;
import com.carplus.subscribe.enums.ContractStatus;
import com.carplus.subscribe.enums.PropertyEnum;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.feign.CrsClient;
import com.carplus.subscribe.model.cars.BuChangeNotify;
import com.carplus.subscribe.model.cars.resp.CarResponse;
import com.carplus.subscribe.model.config.SubscribeConfig;
import com.carplus.subscribe.model.crs.CarBaseInfoSearchResponse;
import com.carplus.subscribe.model.crs.CarLicenseResponse;
import com.carplus.subscribe.model.crs.PurchaseProjectCarSearchResponse;
import com.carplus.subscribe.model.crs.assign.AssignAddReq;
import com.carplus.subscribe.model.crs.assign.BUChangeDetailSearchResponse;
import com.carplus.subscribe.model.crs.assign.BUChangeSearchResponse;
import com.carplus.subscribe.model.crs.assign.ChangeListResponse;
import com.carplus.subscribe.model.lrental.UpdateCarProperty;
import com.carplus.subscribe.server.MattermostServer;
import com.carplus.subscribe.server.cars.LrentalServer;
import com.carplus.subscribe.server.cars.model.CarHistorySearchRep;
import com.carplus.subscribe.server.cars.model.CarPropertyResp;
import com.carplus.subscribe.utils.CarsUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.carplus.subscribe.enums.BuChangeEnum.ChangeTypeOfAssign.BATCH_RETURN;
import static com.carplus.subscribe.enums.BuChangeEnum.ChangeTypeOfAssign.SELL;
import static com.carplus.subscribe.enums.OrderStatus.*;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.CRS_CAR_NOT_FOUND;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.PREOWNED_INV_CONFIG_NOT_FUND;

@Slf4j
@Service
public class BuChangeService {

    @Autowired
    private CrsService crsService;

    @Autowired
    private CrsClient crsClient;

    @Autowired
    @Lazy
    private BuChangeService self;
    @Autowired
    private ConfigService configService;
    @Autowired
    private CarsService carsService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private DealerOrderService dealerOrderService;

    @Autowired
    private NotifyService notifyService;

    @Autowired
    private BuChangeLogRepository buChangeLogRepository;

    @Autowired
    private MainContractRepository mainContractRepository;

    @Autowired
    private LrentalServer lrentalServer;

    @Autowired
    private MattermostServer mattermostServer;
    @Autowired
    private ContractService contractService;


    @Async
    public void addBuChangeLog(String orderNo, String plateNo, AssignAddReq assignAddReq, Integer buChangeMasterId) {
        BuChangeLog buChangeLog = new BuChangeLog();
        buChangeLog.setOrderNo(orderNo);
        buChangeLog.setPlateNo(plateNo);
        buChangeLog.setFromBuId(assignAddReq.getAssignDetailList().get(0).getFromBu());
        buChangeLog.setToBuId(assignAddReq.getAssignDetailList().get(0).getTargetBu());
        buChangeLog.setChangeType(assignAddReq.getChangeType());
        buChangeLog.setMemo(assignAddReq.getMemo());
        buChangeLog.setBuChangeMasterId(buChangeMasterId);
        buChangeLogRepository.save(buChangeLog);
    }

    public BuChangeLog addBuChangeLog(BUChangeDetailSearchResponse response, String orderNo, String memo, BuChangeEnum.ChangeTypeOfAssign changeType) {
        BuChangeLog buChangeLog = new BuChangeLog();
        buChangeLog.setOrderNo(orderNo);
        buChangeLog.setPlateNo(response.getPlateNo());
        buChangeLog.setFromBuId(response.getFromBu());
        buChangeLog.setToBuId(response.getTargetBu());
        buChangeLog.setChangeType(changeType);
        buChangeLog.setMemo(memo);
        buChangeLog.setBuChangeMasterId(response.getBuChangeMasterId());
        buChangeLogRepository.save(buChangeLog);
        return buChangeLog;
    }

    public BuChangeLog addBuChangeLogByCar(Cars cars, String orderNo) {
        if (cars.getBuChangeMasterId() != null) {
            BUChangeSearchResponse buChangeSearchResponse = crsService.getChangeBuInfoSearch(cars.getBuChangeMasterId()).get(0);
            BUChangeDetailSearchResponse buChangeDetailSearchResponse = crsService.getChangeBuInfoDetail(cars.getBuChangeMasterId(), cars.getPlateNo(), cars.getCrsCarNo());
            return addBuChangeLog(buChangeDetailSearchResponse, orderNo, buChangeSearchResponse.getMemo(), BuChangeEnum.ChangeTypeOfAssign.of(buChangeSearchResponse.getChangeType()));
        }
        return null;
    }

    public Map<String, BuChangeLog> getLastPlateNosLog(List<String> plateNos) {
        return buChangeLogRepository.findPlatesLogs(plateNos).stream().collect(Collectors.toMap(BuChangeLog::getPlateNo, Function.identity(), (v1, v2) -> v1));
    }


    public List<BuChangeLog> getLastPlateNoLog(String plateNo) {
        return buChangeLogRepository.findByPlateNo(plateNo);
    }

    public BuChangeLog getLastOrderNoLog(String orderNo) {
        return buChangeLogRepository.findOrderNoLastLog(orderNo);
    }

    public BuChangeLog getToSubscribeFirstLog(String mainContractNo, String plateNo) {
        List<String> orderNos = mainContractRepository.getOrderNosByMainContractNo(mainContractNo);
        return getToSubscribeFirstLog(orderNos, plateNo);
    }

    public BuChangeLog getToSubscribeFirstLog(List<String> orderNos, String plateNo) {
        if (CollectionUtils.isEmpty(orderNos)) {
            return null;
        }
        return buChangeLogRepository.findToBuFirstLog(orderNos, plateNo, BuIdEnum.subscribe.getCode());
    }

    public BuChangeLog getOrAddLastPlateNoLog(Cars car, String orderNo) {
        BuChangeLog buChangeLog = buChangeLogRepository.findPlateLastLog(car.getPlateNo());
        if (buChangeLog == null && car.getBuChangeMasterId() != null) {
            addBuChangeLogByCar(car, orderNo);
        }
        return null;
    }

    public BuChangeLog getOrAddLastPlateNoLog(Cars car) {
        BuChangeLog buChangeLog = buChangeLogRepository.findPlateLastLog(car.getPlateNo());
        if (buChangeLog == null && car.getBuChangeMasterId() != null) {
            addBuChangeLogByCar(car, null);
        }
        return null;
    }

    public void checkSubscribeCarBuChange(CarBaseInfoSearchResponse carBase, Cars cars, String orderNo) {
        if (carBase.getBuId().equals(BuIdEnum.subscribe.getCode())) {
            BuChangeLog log = buChangeLogRepository.findPlateLastLog(carBase.getPlateNo());
            if (log == null) {
                log = addBuChangeLogByCar(cars, null);
            }
            if (log != null) {
                if (log.getChangeType() == SELL || log.getChangeType() == BATCH_RETURN) {
                    ChangeListResponse changeListResponse = crsService.getChangeBuInfos(log.getBuChangeMasterId());
                    // 未出車
                    if (BuChangeEnum.BUChangeMasterStatus.NONOUT.getCode().equals(changeListResponse.getStatus())) {
                        crsService.cancelLeaveCar(changeListResponse, carBase.getPlateNo(), carBase.getCarNo(), orderNo);
                        changeListResponse = crsService.getChangeBuInfos(log.getBuChangeMasterId());
                        if (BuChangeEnum.BUChangeMasterStatus.CANCELOUT.getCode().equals(changeListResponse.getStatus())) {
                            cars.setBuChangeMasterId(null);
                        }
                        // 未審核
                    } else if (BuChangeEnum.BUChangeMasterStatus.NOTSIGN.getCode().equals(changeListResponse.getStatus())) {
                        crsService.cancelChangeCar(changeListResponse, orderNo);
                        changeListResponse = crsService.getChangeBuInfos(log.getBuChangeMasterId());
                        if (BuChangeEnum.BUChangeMasterStatus.INVALID.getCode().equals(changeListResponse.getStatus())) {
                            cars.setBuChangeMasterId(null);
                        }
                        cars.setBuChangeMasterId(null);
                    }

                }
            }
        }
    }

    public void changeBuFromPreowned(String orderNo, CarBaseInfoSearchResponse carBase, String memberId, Instant preUseDateStart, Instant preUseDateEnd, Instant licenseExpDate, Cars cars) {
        if (carBase != null && (carBase.getBuId().equals(BuIdEnum.secondHand.getCode()) || carBase.getBuId().equals(BuIdEnum.carCenter.getCode()))) {
            crsService.subscribeDoneCarControl(carBase);
            Integer buChangeId = crsService.autoChangeBu(orderNo, carBase, memberId, preUseDateStart, preUseDateEnd, licenseExpDate);
            cars.setBuChangeMasterId(buChangeId);
            IOrder order = orderService.isCarPlusOrder(orderNo) ? orderService.getOrder(orderNo) : dealerOrderService.getOrder(orderNo);
            if (order.getIsNewOrder()) {
                notifyService.notifyOrderReceivedConfirm(order, cars, carBase);
            }
            updatePropertyWhenToSubscribe(carBase, orderNo);
        }
    }

    public void batchChange(String orderNo, CarBaseInfoSearchResponse carBase, String memberId, Instant preUseDateStart, Instant preUseDateEnd, Cars cars, List<String> attachmentId) {
        if (carBase.getBuId().equals(BuIdEnum.s2g.getCode()) || carBase.getBuId().equals(BuIdEnum.sRental.getCode())) {
            Integer buChangeId = crsService.batchChangeBu(orderNo, carBase, memberId, preUseDateStart, preUseDateEnd, attachmentId);
            cars.setBuChangeMasterId(buChangeId);
        }
    }

    public void generalChange(String orderNo, CarBaseInfoSearchResponse carBase, String memberId, Instant preUseDateStart, Instant preUseDateEnd, Cars cars, List<String> attachmentId) {
        if (carBase.getBuId().equals(BuIdEnum.s2g.getCode()) || carBase.getBuId().equals(BuIdEnum.sRental.getCode())) {
            Integer buChangeId = crsService.generalChangeBu(orderNo, carBase, memberId, preUseDateStart, preUseDateEnd, attachmentId);
            cars.setBuChangeMasterId(buChangeId);
        }
    }

    public void changeReturn(Orders order, String memberId) {
        Cars car = carsService.findByPlateNo(order.getPlateNo());
        CarBaseInfoSearchResponse carBase = crsService.getCar(car.getPlateNo());
        changeReturn(order, carBase, order.getMemberId(), car);
    }

    @Transactional
    public void changeReturn(String orderNo, String memberId) {
        IOrder order = orderService.getOrder(orderNo);
        if (order == null) {
            return;
        }
        Orders oOrders = (Orders) order;
        Cars car = carsService.findByPlateNo(oOrders.getPlateNo());
        CarBaseInfoSearchResponse carBase = crsService.getCar(car.getPlateNo());
        changeReturn(order, carBase, memberId, car);
        orderService.updateOrder(oOrders);
        carsService.update(car);

    }

    public void changeReturn(IOrder order, CarBaseInfoSearchResponse carBase, String memberId, Cars cars) {
        if (cars.isVirtualCar() || !CarsUtil.isCarPlusCar(cars.getVatNo())) {
            return;
        }
        if (carBase == null) {
            throw new SubscribeException(CRS_CAR_NOT_FOUND);
        }
        try {
            BuChangeLog buChangeLog = buChangeLogRepository.findPlateLastLog(carBase.getPlateNo());
            BUChangeSearchResponse buChangeSearchResponse = null;
            BUChangeDetailSearchResponse buChangeDetailSearchResponse = null;
            BuIdEnum buIdEnum = BuIdEnum.ofEnum(carBase.getBuId());
            if (Objects.equals(buIdEnum, BuIdEnum.secondHand) || carBase.getBuId().equals(BuIdEnum.carCenter.getCode())) {
                crsService.unsubscribeCarControl(carBase);
                return;
            }

            if (cars.getBuChangeMasterId() == null) {
                if (subscribeCarSellToPreowned(order.getOrderNo(), carBase)) {
                    cars.setBuChangeMasterId(null);
                    return;
                }
                log.warn("退訂車輛未找到撥車紀錄，車牌號碼: {}", cars.getPlateNo());
                return;
            }

            ChangeListResponse changeListResponse = crsService.getChangeBuInfos(cars.getBuChangeMasterId());
            if (buChangeLog == null) {
                buChangeSearchResponse = crsService.getChangeBuInfoSearch(cars.getBuChangeMasterId()).get(0);
                buChangeDetailSearchResponse = crsService.getChangeBuInfoDetail(cars.getBuChangeMasterId(), carBase.getPlateNo(), carBase.getCarNo());
                buChangeLog = addBuChangeLog(buChangeDetailSearchResponse, order.getOrderNo(), buChangeSearchResponse.getMemo(), BuChangeEnum.ChangeTypeOfAssign.of(buChangeSearchResponse.getChangeType()));
            }
            if (buChangeLog != null) {

                CarHistorySearchRep carHistorySearchRep = lrentalServer.getCarLastHistory(carBase.getCarNo());
                /* IF
                 * 退訂車輛 庫位 = 訂閱 and
                 *
                 */
                /* IF
                 * 退訂車輛 buChangeMasterId 查撥車單 changeType = autoBatchChange and
                 * 退訂車輛 有 入庫調度
                 *
                 * THAN
                 * 退訂車輛 buChangeMasterId is null and  退訂車輛庫位 = 中古 or 調度
                 * 若中古狀態為「訂閱」則改為「開放」
                 * 若中古用途分派為「訂閱待訂」、「短租」or「訂閱式租賃」則改為「中古」
                 */
                if (buIdEnum == BuIdEnum.subscribe && buChangeLog.getChangeType() == BuChangeEnum.ChangeTypeOfAssign.AUTO_BATCH_CHANGE && isCarInStock(carHistorySearchRep)) {
                    crsService.autoChangeReturnBu(order.getOrderNo(), carBase, memberId, buChangeLog.getBuChangeMasterId());
                    CarPropertyResp carProperty = lrentalServer.getCarProperty(carBase.getPlateNo());
                    UpdateCarProperty updateCarProperty = new UpdateCarProperty(carProperty);
                    updateCarProperty.setPyAuto(null);
                    if (Objects.equals(carProperty.getStatus(), PropertyEnum.PropertyStatus.subscribe.getCode())) {
                        updateCarProperty.setPropertyStatus(PropertyEnum.PropertyStatus.open.getCode());
                        updateCarProperty.setPyAuto(carProperty.getPyAuto());
                    }
                    if (Objects.equals(carProperty.getPurposeCode(), PropertyEnum.PurposeCode.subscribe.getPurposeCode()) || Objects.equals(carProperty.getPurposeCode(), PropertyEnum.PurposeCode.sRental.getPurposeCode())
                        || Objects.equals(carProperty.getPurposeCode(), PropertyEnum.PurposeCode.subscribeRental.getPurposeCode())) {
                        updateCarProperty.setPurposeCode(PropertyEnum.PurposeCode.secondHand.getPurposeCode());
                        updateCarProperty.setPyAuto(carProperty.getPyAuto());
                    }
                    if (updateCarProperty.getPyAuto() != null) {
                        lrentalServer.updateCarProperty(updateCarProperty);
                    }
                    cars.setBuChangeMasterId(null);

                } else if (buIdEnum == BuIdEnum.subscribe && buChangeLog.getChangeType() == BuChangeEnum.ChangeTypeOfAssign.BATCH_CHANGE
                    && Objects.equals(changeListResponse.getStatus(), BuChangeEnum.BUChangeMasterStatus.DONE.getCode())) {
                    /* changeType = batchChange & status = 40(已完成)
                     * 一鍵還車
                     */
                    crsService.batchChangeReturnBu(order.getOrderNo(), carBase, memberId, buChangeLog.getBuChangeMasterId());
                } else if (buIdEnum == BuIdEnum.subscribe && buChangeLog.getChangeType() == BuChangeEnum.ChangeTypeOfAssign.CHANGE
                    && Arrays.asList(BuChangeEnum.BUChangeMasterStatus.OUT.getCode(),
                        BuChangeEnum.BUChangeMasterStatus.NONRECEIVE.getCode(),
                        BuChangeEnum.BUChangeMasterStatus.RECEIVE.getCode(),
                        BuChangeEnum.BUChangeMasterStatus.DONE.getCode())
                    .contains(Optional.ofNullable(changeListResponse.getStatus()).orElse(""))
                    && carHistorySearchRep.isCarInStock()) {
                    /* changeType = change & status = 10(已出車)
                     * 賣車申請
                     */
                    crsService.findLocationAndSellToPreowned(order.getOrderNo(), carBase, String.valueOf(carHistorySearchRep.getSgAuto()));
                }
                cars.setBuChangeMasterId(null);
            } else {
                /* IF
                 * 退訂車輛.buChangeMasterId 查撥車單
                 * changeType = batchChange or change & status = 01(未審核)
                 *
                 * THAN
                 * 取消撥車
                 */

                if (buChangeLog.getChangeType() == BuChangeEnum.ChangeTypeOfAssign.BATCH_CHANGE || buChangeLog.getChangeType() == BuChangeEnum.ChangeTypeOfAssign.CHANGE) {

                    if (Objects.equals(changeListResponse.getStatus(), BuChangeEnum.BUChangeMasterStatus.NOTSIGN.getCode())) {
                        crsService.cancelChangeCar(changeListResponse, order.getOrderNo());
                        cars.setBuChangeMasterId(null);
                    } else if (Objects.equals(changeListResponse.getStatus(), BuChangeEnum.BUChangeMasterStatus.NONOUT.getCode())) {
                        crsService.cancelLeaveCar(changeListResponse, carBase.getPlateNo(), carBase.getCarNo(), order.getOrderNo());
                        cars.setBuChangeMasterId(null);
                    }
                }
            }

        } catch (Exception e) {
            mattermostServer.notify("退訂車輛失敗", Collections.singletonMap("車牌號碼: ", cars.getPlateNo()), e);
        }
    }

    public boolean subscribeCarSellToPreowned(String orderNo, CarBaseInfoSearchResponse carBase) {
        CarHistorySearchRep carHistory = lrentalServer.getCarLastHistory(carBase.getCarNo());
        CarLicenseResponse carLicenseResponse = crsService.getCarLicense(carBase.getCarNo());
        if (carBase.getBuId().equals(BuIdEnum.subscribe.getCode())
            && isCarInStock(carHistory)
            && carLicenseResponse != null && carLicenseResponse.getFirstLicenseDate() != null
            && carLicenseResponse.getFirstLicenseDate().isBefore(Instant.now().atZone(DateUtils.ZONE_TPE).minusYears(1).toInstant())) {
            PurchaseProjectCarSearchResponse purchaseProjectCarSearchResponse = crsService.searchProjectCar(carBase.getCarNo());
            if (purchaseProjectCarSearchResponse == null || !purchaseProjectCarSearchResponse.getIsProjectCar()) {
                crsService.unsubscribeCarControl(carBase);
                crsService.findLocationAndSellToPreowned(orderNo, carBase, String.valueOf(carHistory.getSgAuto()));
                return true;
            }
        }
        return false;
    }

    public void notifyReturn() {
        Map<String, Cars> carsMap = carsService.getIdleCar().stream().collect(Collectors.toMap(Cars::getPlateNo, Function.identity()));
        List<String> plateNos = carsMap.values().stream().map(Cars::getPlateNo).collect(Collectors.toList());
        List<Integer> crsCarNos = carsMap.values().stream().map(Cars::getCrsCarNo).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, CarBaseInfoSearchResponse> crsCarMap = crsService.getCars(plateNos);
        Map<String, BuChangeLog> buChangeLogMap = getLastPlateNosLog(plateNos);
        Map<Integer, CarLicenseResponse> carLicenseMap = new HashMap<>();
        Map<Integer, CarHistorySearchRep> carHistoryMap = new HashMap<>();

        for (List<Integer> subCrsCarNo : Lists.partition(crsCarNos, 100)) {
            carLicenseMap.putAll(crsService.getCarLicenses(subCrsCarNo).stream().collect(Collectors.toMap(CarLicenseResponse::getCarNo, Function.identity())));
        }
        for (List<Integer> subCrsCarNo : Lists.partition(crsCarNos, 100)) {
            carHistoryMap.putAll(lrentalServer.getCarLastHistoryMap(subCrsCarNo));
        }
        List<String> notifyToCheckIsInStock = new ArrayList<>();
        Map<String, Object> errorMessages = new HashMap<>();

        // 遍歷所有空閒車輛
        for (Cars car : carsMap.values()) {
            try {
                String plateNo = car.getPlateNo();
                CarBaseInfoSearchResponse carBase = crsCarMap.get(plateNo);

                // 只處理訂閱庫位的車輛
                if (carBase != null && carBase.getBuId().equals(BuIdEnum.subscribe.getCode())) {
                    BuChangeLog buChangeLog = buChangeLogMap.get(plateNo);
                    CarLicenseResponse carLicense = carLicenseMap.get(carBase.getCarNo());

                    // 檢查車輛條件:
                    // 1. 車齡是否超過12個月
                    boolean isLicenseValid = carLicense != null
                        && carLicense.getFirstLicenseDate() != null
                        && carLicense.getFirstLicenseDate().atZone(DateUtils.ZONE_TPE)
                        .isBefore(Instant.now().atZone(DateUtils.ZONE_TPE).minusMonths(12));

                    // 2. 車輛是否已入庫中古所
                    CarHistorySearchRep carHistory = carHistoryMap.get(car.getCrsCarNo());
                    boolean isCarInStock = isCarInStock(carHistory);

                    // 3. 是否為專案車
                    boolean isProjectCar = car.isProjectCar();

                    // 情境一: 專案車自動撥回處理
                    // 條件: 有自動營業用撥車紀錄 + 已入庫中古所
                    if (buChangeLog != null && buChangeLog.getChangeType() == BuChangeEnum.ChangeTypeOfAssign.AUTO_BATCH_CHANGE && isCarInStock) {
                        crsService.unsubscribeCarControl(carBase);
                        crsService.autoChangeReturnBu(buChangeLog.getOrderNo(), carBase, configService.getSubscribeConfig().getSubscribeDefaultMemberId(), buChangeLog.getBuChangeMasterId());

                        // 情境二: 賣車至中古車部門
                        // 條件: (無撥車紀錄或為撥車申請) + 車齡超過12個月 + 已入庫中古所
                    } else if ((buChangeLog == null || buChangeLog.getChangeType() == BuChangeEnum.ChangeTypeOfAssign.CHANGE) && !isProjectCar && isLicenseValid && isCarInStock) {
                        String orderNo = buChangeLog == null ? null : buChangeLog.getOrderNo();
                        try {
                            subscribeCarSellToPreowned(orderNo, carBase);
                        } catch (SubscribeException e) {
                            if (PREOWNED_INV_CONFIG_NOT_FUND.equals(e.getCode())) {
                                // 當遇到找不到中古車庫存設定時，加入 notifyToCheckIsInStock
                                notifyToCheckIsInStock.add(plateNo);
                            }
                        }

                        // 情境三: 需要檢查車輛庫存狀態
                        // 條件: (無撥車紀錄或為撥車申請/自動營業用撥車) + 車齡超過12個月 + 尚未入庫中古所
                    } else if ((buChangeLog == null || (buChangeLog.getChangeType() == BuChangeEnum.ChangeTypeOfAssign.CHANGE
                        || buChangeLog.getChangeType() == BuChangeEnum.ChangeTypeOfAssign.AUTO_BATCH_CHANGE))
                        && isLicenseValid && !isCarInStock) {
                        notifyToCheckIsInStock.add(plateNo);
                    }
                }
            } catch (Exception e) {
                errorMessages.put(car.getPlateNo(), String.format("撥車失敗: %s", e.getMessage()));
            }
        }

        // 發送錯誤通知
        if (!errorMessages.isEmpty()) {
            mattermostServer.notify("撥車失敗", errorMessages, null);
        }

        if (!notifyToCheckIsInStock.isEmpty()) {
            processNotificationsForNonInStockCars(notifyToCheckIsInStock, carsMap, carHistoryMap);
        }
    }

    /**
     * 檢查車輛是否已入庫
     */
    private Boolean isCarInStock(CarHistorySearchRep carHistory) {
        return Optional.ofNullable(carHistory)
            .map(CarHistorySearchRep::isCarInStock)
            .orElse(false);
    }

    private void processNotificationsForNonInStockCars(List<String> notifyToCheckIsInStock, Map<String, Cars> carsMap, Map<Integer, CarHistorySearchRep> carHistoryMap) {
        if (notifyToCheckIsInStock.isEmpty()) {
            return;
        }

        // 獲取並合併訂單通知列表
        List<BuChangeNotify> notifyList = getOrderNotifications(notifyToCheckIsInStock, carHistoryMap, carsMap);

        // 建立車牌對應通知的映射
        Map<String, BuChangeNotify> plateNoNotifyMap = createPlateNoNotifyMap(notifyList);

        // 建立站點對應訂單的映射
        Map<String, List<BuChangeNotify>> stationNotificationsMap = createStationOrderMap(notifyToCheckIsInStock, plateNoNotifyMap, carsMap);

        // 發送通知
        notifyService.notifyWaitForCarReturn(stationNotificationsMap);
    }

    private List<BuChangeNotify> getOrderNotifications(List<String> plateNos, Map<Integer, CarHistorySearchRep> carHistoryMap, Map<String, Cars> carsMap) {
        List<BuChangeNotify> notifyList = new ArrayList<>();

        // 獲取一般訂單通知
        List<BuChangeNotify> orderNotifications = orderService
            .getOrdersByPlateNosAndStatus(plateNos, Arrays.asList(CANCEL, CLOSE, ARRIVE_NO_CLOSE))
            .stream()
            .map(order -> {
                Cars car = carsMap.get(order.getPlateNo());
                CarHistorySearchRep carHistory = car != null ? carHistoryMap.get(car.getCrsCarNo()) : null;
                return new BuChangeNotify(order, carHistory);
            })
            .collect(Collectors.toList());

        // 獲取經銷商訂單通知
        List<BuChangeNotify> dealerNotifications = dealerOrderService
            .getDealerOrdersByPlateNosAndStatus(plateNos, Arrays.asList(ContractStatus.COMPLETE, ContractStatus.CANCEL))
            .stream()
            .map(order -> {
                Cars car = carsMap.get(order.getPlateNo());
                CarHistorySearchRep carHistory = car != null ? carHistoryMap.get(car.getCrsCarNo()) : null;
                return new BuChangeNotify(order, carHistory);
            })
            .collect(Collectors.toList());

        // 合併並排序通知列表
        notifyList.addAll(orderNotifications);
        notifyList.addAll(dealerNotifications);
        notifyList.sort(Comparator.comparing(BuChangeNotify::getEffectiveDate, Comparator.nullsFirst(Comparator.naturalOrder())));

        return notifyList;
    }

    private Map<String, BuChangeNotify> createPlateNoNotifyMap(List<BuChangeNotify> notifyList) {
        return notifyList.stream()
            .collect(Collectors.toMap(
                BuChangeNotify::getPlateNo,
                Function.identity(),
                (v1, v2) -> v2
            ));
    }

    private Map<String, List<BuChangeNotify>> createStationOrderMap(
        List<String> plateNos,
        Map<String, BuChangeNotify> plateNoNotifyMap,
        Map<String, Cars> carsMap
    ) {
        Map<String, List<BuChangeNotify>> stationNotificationsMap = new HashMap<>();

        plateNos.forEach(plateNo -> {
            BuChangeNotify notify = plateNoNotifyMap.get(plateNo);
            if (notify != null) {
                stationNotificationsMap.computeIfAbsent(
                    notify.getReturnStationCode(),
                    k -> new ArrayList<>()
                ).add(notify);
            } else {
                Cars car = carsMap.get(plateNo);
                // 避免覆蓋已存在的 map
                stationNotificationsMap.computeIfAbsent(
                    car.getLocationStationCode(),
                    k -> new ArrayList<>()
                );
            }
        });

        return stationNotificationsMap;
    }

    private void updatePropertyWhenToSubscribe(CarBaseInfoSearchResponse carBase, String orderNo) {
        CarResponse carResponse = carsService.getCarInfo(carBase.getPlateNo());
        boolean isProjectCar = Optional.ofNullable(crsService.searchProjectCar(carBase.getCarNo())).map(PurchaseProjectCarSearchResponse::getIsProjectCar).orElse(false);
        PropertyEnum.PurposeCode code = PropertyEnum.PurposeCode.sRental;
        String brandCode = Optional.ofNullable(configService.getSubscribeConfig()).map(SubscribeConfig::getSubscribeSpecialBrandCode).orElse("00V01");
        if (carResponse.getCarBrand().getBrandCode().equals(brandCode) && isProjectCar) {
            code = PropertyEnum.PurposeCode.subscribe;
        } else if (!orderService.isCarPlusOrder(orderNo)) {
            code = PropertyEnum.PurposeCode.subscribeRental;
        }
        UpdateCarProperty updateCarProperty = new UpdateCarProperty();
        updateCarProperty.setPyAuto(carBase.getCarBase().getPyAuto());
        updateCarProperty.setPurposeCode(code.getPurposeCode());
        updateCarProperty.setPropertyStatus(PropertyEnum.PropertyStatus.subscribe.getCode());
        lrentalServer.updateCarProperty(updateCarProperty);
    }
}


