package com.carplus.subscribe.model.request.task;

import carplus.common.utils.StringUtils;
import com.carplus.subscribe.enums.RentalFormType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotNull;

@Data
public class DepartTaskRequest {
    @NotNull(message = "出租單類型不可為空")
    @Schema(description = "出租單類型")
    private RentalFormType rentalFormType;

    @Schema(description = "出車人員編號")
    private String departMemberId;

    @AssertTrue(message = "若為紙本出租單，則出車人員編號不可為空")
    @Schema(hidden = true)
    public boolean isDepartMemberIdValid() {
        // 若為紙本出租單，則出車人員編號不可為空
        return rentalFormType != RentalFormType.PAPER || StringUtils.isNotBlank(departMemberId);
    }
}