package com.carplus.subscribe.rabbitmq;

import com.carplus.subscribe.db.mysql.entity.contract.MainContract;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.db.mysql.entity.payment.PaymentInfo;
import com.carplus.subscribe.enums.PayFor;
import com.carplus.subscribe.enums.PaymentCategory;
import com.carplus.subscribe.model.queue.PaymentQueue;
import com.carplus.subscribe.server.MattermostServer;
import com.carplus.subscribe.service.*;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.map.SingletonMap;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.Objects;

@ConditionalOnExpression("${carplus.rabbitmq.enabled}")
@Slf4j
@Component
public class PaymentListener {

    @Autowired
    private MattermostServer mattermostServer;

    @Autowired
    private ContractService contractService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private PaymentServiceV2 paymentServiceV2;
    @Autowired
    private CheckoutService checkoutService;
    @Autowired
    private PriceInfoService priceInfoService;

    @RabbitListener(queues = "#{'${env}' + '-payment-order-Subscribe-sync'}")
    public void oriSubscribeReceive(
        @Payload PaymentQueue paymentQueue,
        @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
        Channel channel
    ) {
        subscribeReceive(paymentQueue, deliveryTag, channel);
    }


    @RabbitListener(queues = "#{'${env}' + '-payment-order-SubscribeV2-sync'}")
    public void subscribeReceive(
        @Payload PaymentQueue paymentQueue,
        @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
        Channel channel
    ) {
        log.info("[receive rabbitmq PaymentQueue]: {}", paymentQueue);
        try {
            paymentServiceV2.savePaymentQueue(paymentQueue);
            String orderNo = paymentQueue.getOrderId();
            List<PaymentInfo> paymentInfoList = paymentServiceV2.getPaymentInfosByTradeId(paymentQueue.getTradeId());
            // 處理保證金
            if (Objects.equals(paymentQueue.getPayFor(), PayFor.SecurityDeposit) || paymentInfoList.stream().anyMatch(p -> Objects.equals(p.getPayFor(), (PayFor.SecurityDeposit)))) {
                Orders order = orderService.getOrder(orderNo);
                MainContract mainContract = contractService.getMainContractAndContractAndOrdersByMainContractNo(order.getContract().getMainContract().getMainContractNo());
                paymentServiceV2.consumeSecurityDepositPayment(mainContract, paymentQueue);
            } else {
                paymentServiceV2.consumePayment(paymentQueue);
                paymentServiceV2.afterPay(paymentQueue);
                if (paymentQueue.getPaymentCategory() == PaymentCategory.PayAuth) {
                    checkoutService.checkOut(orderNo);
                }
            }
            log.info("[consume rabbitmq PaymentQueue] success");
        } catch (Exception e) {
            log.error("[consume rabbitmq PaymentQueue] failed", e);
            mattermostServer.notify("[consume rabbitmq PaymentQueue] failed", new SingletonMap<>("Queue", paymentQueue), e);
        } finally {
            try {
                if (channel != null) {
                    channel.basicAck(deliveryTag, false);
                }
            } catch (IOException e) {
                log.error("[basicAck rabbitmq PaymentQueue] failed", e);
                mattermostServer.notify("[consume rabbitmq PaymentQueue] IOE failed", new SingletonMap<>("Queue", paymentQueue), e);
            }
        }
    }
}
