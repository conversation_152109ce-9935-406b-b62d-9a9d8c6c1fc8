package com.carplus.subscribe.controller.etag;

import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.db.mysql.entity.ETagInfo;
import com.carplus.subscribe.model.etag.EtagInfoResponse;
import com.carplus.subscribe.model.etag.EtagRequest;
import com.carplus.subscribe.service.ETagService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@CarPlusRestController
@RequestMapping(HttpConstant.INTERNAL_URL)
@Tag(name = "Internal 遠通ETAG API")
public class EtagInternalController {

    @Autowired
    private ETagService eTagService;

    @Operation(summary = "Etag出車")
    @PostMapping("subscribe/etag/rent")
    public void etagRentCar(
        @RequestHeader(CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
        @Validated @RequestBody EtagRequest request) {
        eTagService.rentCar(request.getOrderNo(), memberId);
    }


    @Operation(summary = "Etag產生新的出車")
    @PostMapping("subscribe/etag/rentCarForNewStage")
    public void etagRentCarForNewStage(@Validated @RequestBody EtagRequest request) {
        eTagService.rentCarForNewStage(request.getOrderNo());
    }

    @Operation(summary = "Etag還車")
    @PostMapping("subscribe/etag/return")
    public void etagReturn(@Validated @RequestBody EtagRequest request) {
        eTagService.returnCar(request.getOrderNo());
    }

    @Operation(summary = "Etag結單")
    @PostMapping("subscribe/etag/close")
    public ETagInfo etagClose(@Validated @RequestBody EtagRequest request) {
        return eTagService.closeCar(request.getOrderNo());
    }

    @Operation(summary = "拿取所有EtagInfo紀錄")
    @GetMapping("subscribe/{orderNo}/etag")
    public List<EtagInfoResponse> etagInfos(@PathVariable("orderNo") String orderNo) {
        return eTagService.getETagIntoByOrderNo(orderNo);
    }
}
