package com.carplus.subscribe.service;

import carplus.common.model.Page;
import carplus.common.model.PageRequest;
import carplus.common.response.exception.BadRequestException;
import com.carplus.subscribe.db.mysql.dao.SubscribeLevelRepository;
import com.carplus.subscribe.db.mysql.entity.SubscribeLevel;
import com.carplus.subscribe.db.mysql.entity.cars.CarPropertyProvider;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.model.request.subscribelevel.SubscribeLevelAddRequest;
import com.carplus.subscribe.model.request.subscribelevel.SubscribeLevelCriteria;
import com.carplus.subscribe.model.request.subscribelevel.SubscribeLevelUpdateRequest;
import com.carplus.subscribe.model.response.subscribelevel.SubscribeLevelCommonResponse;
import com.carplus.subscribe.model.response.subscribelevel.SubscribeLevelInternalResponse;
import com.carplus.subscribe.model.subscribelevel.LevelPriceRange;
import com.carplus.subscribe.server.AuthorityServer;
import com.google.common.base.Objects;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static carplus.common.utils.BeanUtils.ignorePropertyNames;

@Service
public class SubscribeLevelService {

    @Autowired
    private SubscribeLevelRepository subscribeLevelRepository;

    @Autowired
    private AuthorityServer authorityServer;

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void add(SubscribeLevelAddRequest request, String memberId) {

        // 同時檢查 id, level 和 name 是否已存在
        SubscribeLevel existingEntity = subscribeLevelRepository.findByLevelOrName(request.getLevel(), request.getName());

        if (existingEntity != null) {
            if (Objects.equal(existingEntity.getId(), request.getLevel())) {
                throw new BadRequestException(String.format("[訂閱方案id已存在, id: %s]", request.getLevel()));
            } else if (existingEntity.getLevel() == request.getLevel()) {
                throw new BadRequestException(String.format("[訂閱方案已存在, level: %s]", request.getLevel()));
            } else {
                throw new BadRequestException(String.format("[訂閱方案名稱已存在, name: %s]", request.getName()));
            }
        }

        validateDiscountLevel(request.getDiscountLevel(), request.getMonthlyFee());

        SubscribeLevel entity = new SubscribeLevel();
        BeanUtils.copyProperties(request, entity, ignorePropertyNames(request));
        entity.setId(request.getLevel());
        entity.setDeleted(false);
        entity.setCreatedBy(memberId);
        entity.setUpdatedBy(memberId);
        subscribeLevelRepository.save(entity);
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void update(SubscribeLevelUpdateRequest request, String memberId) {

        SubscribeLevel entity = subscribeLevelRepository.findById(request.getId())
            .orElseThrow(() -> new BadRequestException(String.format("[訂閱方案不存在, id: %s]", request.getId())));

        // 檢查 level 和 name 是否重複（排除自己）
        SubscribeLevel existingEntity = subscribeLevelRepository.findByName(request.getName());
        if (existingEntity != null && !existingEntity.getId().equals(request.getId())) {
            throw new BadRequestException(String.format("[訂閱方案名稱已存在, name: %s]", request.getName()));
        }

        validateDiscountLevel(request.getDiscountLevel(), request.getMonthlyFee());

        BeanUtils.copyProperties(request, entity, ignorePropertyNames(request));
        entity.setUpdatedBy(memberId);
        subscribeLevelRepository.save(entity);
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void updateIsDeleted(Integer id, String memberId) {
        subscribeLevelRepository.updateIsDeleted(id, true, memberId);
    }

    /**
     * 獲取訂閱方案的建立者和更新者的名稱
     */
    private Map<String, String> retrieveMemberNames(SubscribeLevel subscribeLevel) {
        String creatorName = Optional.ofNullable(subscribeLevel.getCreatedBy())
            .map(authorityServer::getMemberName)
            .orElse(null);

        String updaterName = Optional.ofNullable(subscribeLevel.getUpdatedBy())
            .map(memberId -> Objects.equal(memberId, subscribeLevel.getCreatedBy())
                ? creatorName
                : authorityServer.getMemberName(memberId))
            .orElse(null);

        Map<String, String> names = new HashMap<>();
        names.put(SubscribeLevelInternalResponse.Fields.creatorName, creatorName);
        names.put(SubscribeLevelInternalResponse.Fields.updaterName, updaterName);
        return names;
    }

    /**
     * 用於獲取訂閱方案列表中 discountLevel 名稱的輔助方法
     */
    private Map<Integer, String> fetchDiscountLevelNames(List<SubscribeLevel> subscribeLevels) {
        // 提取所有需要查詢的 discountLevel ids
        Set<Integer> discountLevelIds = subscribeLevels.stream()
            .map(SubscribeLevel::getDiscountLevel)
            .filter(java.util.Objects::nonNull)
            .collect(Collectors.toSet());

        // 在單一批次查詢中獲取所有需要的 discountLevels
        if (discountLevelIds.isEmpty()) {
            return Collections.emptyMap();
        }

        return subscribeLevelRepository.findAllById(discountLevelIds).stream()
            .collect(Collectors.toMap(SubscribeLevel::getLevel, SubscribeLevel::getName));
    }

    @Transactional(transactionManager = "mysqlTransactionManager", readOnly = true)
    public Page<SubscribeLevelInternalResponse> searchByPage(SubscribeLevelCriteria criteria) {
        int skip = criteria.getSkip();
        int limit = criteria.getLimit();

        long total = subscribeLevelRepository.count(criteria);
        if (total == 0) {
            return Page.of(0, Collections.emptyList(), skip, limit);
        }

        List<SubscribeLevel> subscribeLevels = subscribeLevelRepository.findByPage(criteria, skip, limit);
        Map<Integer, String> discountLevelNames = fetchDiscountLevelNames(subscribeLevels);

        List<SubscribeLevelInternalResponse> responseList = subscribeLevels.stream()
            .map(subscribeLevel -> {
                Map<String, String> memberNames = retrieveMemberNames(subscribeLevel);
                String discountLevelName = Optional.ofNullable(subscribeLevel.getDiscountLevel())
                    .map(discountLevelNames::get)
                    .orElse(null);

                return new SubscribeLevelInternalResponse(
                    subscribeLevel,
                    discountLevelName,
                    memberNames.get(SubscribeLevelInternalResponse.Fields.creatorName),
                    memberNames.get(SubscribeLevelInternalResponse.Fields.updaterName)
                );
            })
            .collect(Collectors.toList());

        return Page.of(total, responseList, skip, limit);
    }

    @Transactional(transactionManager = "mysqlTransactionManager", readOnly = true)
    public Page<SubscribeLevelCommonResponse> searchByPage(PageRequest pageRequest) {
        SubscribeLevelCriteria criteria = new SubscribeLevelCriteria();
        criteria.setSkip(pageRequest.getSkip());
        criteria.setLimit(pageRequest.getLimit());

        int skip = criteria.getSkip();
        int limit = criteria.getLimit();

        long total = subscribeLevelRepository.count(criteria);
        if (total == 0) {
            return Page.of(0, Collections.emptyList(), skip, limit);
        }

        List<SubscribeLevel> subscribeLevels = subscribeLevelRepository.findByPage(criteria, skip, limit);
        Map<Integer, String> discountLevelNames = fetchDiscountLevelNames(subscribeLevels);

        List<SubscribeLevelCommonResponse> responseList = subscribeLevels.stream()
            .map(subscribeLevel -> {
                String discountLevelName = Optional.ofNullable(subscribeLevel.getDiscountLevel())
                    .map(discountLevelNames::get)
                    .orElse(null);
                return new SubscribeLevelCommonResponse(subscribeLevel, discountLevelName);
            })
            .collect(Collectors.toList());

        return Page.of(total, responseList, skip, limit);
    }


    @Transactional(transactionManager = "mysqlTransactionManager", readOnly = true)
    public SubscribeLevelCommonResponse findById(Integer id, boolean fromInternal) {
        return subscribeLevelRepository.findById(id)
            .map(subscribeLevel -> {
                String discountLevelName = Optional.ofNullable(subscribeLevel.getDiscountLevel())
                    .map(discountLevel -> subscribeLevelRepository.findByLevel(discountLevel))
                    .map(SubscribeLevel::getName)
                    .orElse(null);

                if (fromInternal) {
                    Map<String, String> memberNames = retrieveMemberNames(subscribeLevel);
                    return new SubscribeLevelInternalResponse(
                        subscribeLevel,
                        discountLevelName,
                        memberNames.get(SubscribeLevelInternalResponse.Fields.creatorName),
                        memberNames.get(SubscribeLevelInternalResponse.Fields.updaterName)
                    );
                }
                return new SubscribeLevelCommonResponse(subscribeLevel, discountLevelName);
            })
            .orElse(null);
    }

    @Transactional(transactionManager = "mysqlTransactionManager", readOnly = true)
    public SubscribeLevel findByLevel(int level) {
        return Optional.ofNullable(subscribeLevelRepository.findByLevel(level))
            .orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.SUBSCRIBE_LEVEL_NOT_FOUND));
    }

    @Transactional(transactionManager = "mysqlTransactionManager", readOnly = true)
    public void checkSubscribeLevelExistence(Integer level) {
        if (subscribeLevelRepository.findByLevel(level) == null) {
            throw new SubscribeException(SubscribeHttpExceptionCode.SUBSCRIBE_LEVEL_NOT_FOUND);
        }
    }

    /**
     * 驗證超激優惠應存在
     *
     * @param discountLevel 超激優惠方案等級
     *
     * @return 超激優惠方案實體，如果 discountLevel 為 null 則返回 null
     */
    @Transactional(transactionManager = "mysqlTransactionManager", readOnly = true)
    public SubscribeLevel checkDiscountLevelExistence(Integer discountLevel) {
        if (discountLevel == null) {
            return null;
        }
        SubscribeLevel discountSubscribeLevel = subscribeLevelRepository.findByLevel(discountLevel);
        if (discountSubscribeLevel == null) {
            throw new SubscribeException(SubscribeHttpExceptionCode.DISCOUNT_LEVEL_NOT_FOUND);
        }
        return discountSubscribeLevel;
    }

    /**
     * 組合驗證方法：驗證超激優惠方案存在性和月費限制
     *
     * @param discountLevel 超激優惠方案等級
     * @param basicMonthlyFee 基本方案月費
     */
    private void validateDiscountLevel(Integer discountLevel, Integer basicMonthlyFee) {
        SubscribeLevel discountSubscribeLevel = checkDiscountLevelExistence(discountLevel);
        if (discountSubscribeLevel != null && discountSubscribeLevel.getMonthlyFee() > basicMonthlyFee) {
            throw new SubscribeException(SubscribeHttpExceptionCode.DISCOUNT_LEVEL_MONTHLY_FEE_INVALID);
        }
    }

    @Transactional(transactionManager = "mysqlTransactionManager", readOnly = true)
    public SubscribeLevel getDiscountLevelBySubscriptionLevel(SubscribeLevel subscribeLevel) {
        return Optional.ofNullable(subscribeLevelRepository.findByLevel(subscribeLevel.getDiscountLevel()))
            .map(discountLevel -> {
                discountLevel.setDiscountLevelEnabled(true);
                return discountLevel;
            }).orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.DISCOUNT_LEVEL_NOT_FOUND));
    }

    /**
     * 是否啟用超激優惠方案
     */
    protected boolean isDiscountLevelEnabled(CarPropertyProvider carPropertyProvider, SubscribeLevel subscribeLevel) {
        return carPropertyProvider.hasLevelDiscountedTag() && subscribeLevel.getDiscountLevel() != null;
    }

    @Transactional(transactionManager = "mysqlTransactionManager", readOnly = true)
    public boolean isDiscountLevelEnabled(Cars car) {
        return isDiscountLevelEnabled(car, findByLevel(car.getSubscribeLevel()));
    }

    /**
     * 根據是否啟用超激優惠方案決定採用一般訂閱方案或超激優惠方案
     */
    @Transactional(transactionManager = "mysqlTransactionManager", readOnly = true)
    public SubscribeLevel determineSubscribeLevel(Cars car, SubscribeLevel subscribeLevel) {
        return isDiscountLevelEnabled(car, subscribeLevel) ? getDiscountLevelBySubscriptionLevel(subscribeLevel) : subscribeLevel;
    }

    @Transactional(transactionManager = "mysqlTransactionManager", readOnly = true)
    public SubscribeLevel determineSubscribeLevel(Cars car) {
        SubscribeLevel subscribeLevel = findByLevel(car.getSubscribeLevel());
        return determineSubscribeLevel(car, subscribeLevel);
    }

    /**
     * 取得全部的訂閱方案
     *
     * @return 訂閱方案清單
     */
    public List<SubscribeLevel> getAll() {
        return subscribeLevelRepository.findAll();
    }

    /**
     * 取得方案最高最低金額
     */
    public LevelPriceRange getPriceRange() {
        LevelPriceRange range = new LevelPriceRange(0, Integer.MAX_VALUE);
        subscribeLevelRepository.findAll().stream().forEach(s -> {
            if (s.getMonthlyFee() > range.getMaxPrice()) {
                range.setMaxPrice(s.getMonthlyFee());
            }
            if (s.getMonthlyFee() < range.getMinPrice()) {
                range.setMinPrice(s.getMonthlyFee());
            }
        });
        return range;
    }
}
