package com.carplus.subscribe.model.crs;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 異動憑證類型 serviceCode=car_base_log-depencedoctype
 * car_base_log & car_license_log 共用
 */
@Getter
@AllArgsConstructor
public enum LogDepencedoctype {

    /** 1 契約編號 da21.da_no */
    contractNo("1", "契約編號"),

    /** 2 契約編號(短租) */
    contractNoOfSRental("2", "契約編號(短租)"),

    /** 3 契約編號(S2G) */
    contractNoOfS2G("3", "契約編號(S2G)"),

    /** 4 車輛整備單  **/
    remediationNo("4", "車輛整備單"),

    /** 5 撥車申請單單號 **/
    assignNo("5", "撥車申請單單號"),

    /** 6 車輛交修單 **/
    repairNo("6", "交修單單號"),

    /** 7 公務車出車編號 **/
    officalCar("7", "公務車出車編號"),

    /** 8 還車確認表序號 **/
    issueCarNo("8", "還車分配序號"),

    /**
     * 9 保險中台系統-要保單號
     */
    insurance("9", "保險中台系統-要保單號"),

    /**
     * 10 系統排程
     */
    cronJob("10", "系統排程"),
    ;

    private final String code;
    private final String name;

    private static final Map<String, LogDepencedoctype> map = Arrays.stream(LogDepencedoctype.values())
        .collect(Collectors.toMap(LogDepencedoctype::getCode, Function.identity()));

    public static LogDepencedoctype of(String code) {
        return map.getOrDefault(code, null);
    }
}
