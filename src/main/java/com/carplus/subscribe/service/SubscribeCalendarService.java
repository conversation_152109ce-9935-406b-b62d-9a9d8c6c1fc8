package com.carplus.subscribe.service;

import carplus.common.model.Page;
import carplus.common.model.PageRequest;
import carplus.common.utils.DateUtils;
import com.carplus.subscribe.db.mysql.dao.SubscribeCalendarRepository;
import com.carplus.subscribe.db.mysql.dto.SubscribeCalendarDto;
import com.carplus.subscribe.db.mysql.entity.Stations;
import com.carplus.subscribe.db.mysql.entity.calendar.SubscribeCalendar;
import com.carplus.subscribe.db.mysql.entity.change.EntityChangeLog;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.model.calendar.CalendarDeleteRequest;
import com.carplus.subscribe.model.calendar.CalendarUpdateRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.CALENDAR_DATE_EXIST;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.CALENDAR_UPDATABLE_DATE_NOT_FOUND;

@Service
public class SubscribeCalendarService {

    @Autowired
    private SubscribeCalendarRepository subscribeCalendarRepository;
    @Autowired
    private StationService stationService;
    @Autowired
    private EntityChangeLogService entityChangeLogService;

    /**
     * 拿取不可使用訂閱日期
     */
    public List<SubscribeCalendarDto> getUnavailableCalendarList(Date date) {
        String dateStr = DateUtils.toDateString(date, "yyyyMMdd", DateUtils.ZONE_TPE);

        return subscribeCalendarRepository.findAfterTargetDate(dateStr);
    }

    /**
     * 拿取不可使用訂閱日期 (分頁)
     */
    public Page<SubscribeCalendarDto> getUnavailableCalendarList(Date date, PageRequest pageRequest) {
        int skip = pageRequest.getSkip();
        int limit = pageRequest.getLimit();

        String dateStr = DateUtils.toDateString(date, "yyyyMMdd", DateUtils.ZONE_TPE);

        long count = subscribeCalendarRepository.countAfterTargetDate(dateStr);
        if (count == 0) {
            return Page.of(0, Collections.emptyList(), skip, limit);
        }

        List<SubscribeCalendarDto> result = subscribeCalendarRepository.findAfterTargetDate(dateStr, skip, limit);

        return Page.of(count, result, skip, limit);
    }

    public Page<EntityChangeLog> getCalendarChangeLogs(PageRequest pageRequest) {
        return entityChangeLogService.getByMainEntityAndPrimaryKey(SubscribeCalendar.class, null, pageRequest);
    }

    /**
     * 檢查 stationCodes 是否有效
     */
    private void validateStationCodes(List<String> stationCodes) {
        if (CollectionUtils.isNotEmpty(stationCodes)) {
            Map<String, Stations> stationMap = stationService.getStationsMap();
            for (String stationCode : stationCodes) {
                if (!stationMap.containsKey(stationCode)) {
                    throw new SubscribeException(HttpStatus.OK, SubscribeHttpExceptionCode.STATION_NOT_FOUND, String.format("站點代碼不存在, 站點代碼: %s", stationCode));
                }
            }
        }
    }

    /**
     * 處理日期並返回 SubscribeCalendar 列表
     */
    private List<SubscribeCalendar> buildCalendarList(List<String> dateList, List<String> stationCodes) {
        List<SubscribeCalendar> calendarList = new ArrayList<>();
        for (String date : dateList) {
            SubscribeCalendar calendar = new SubscribeCalendar();
            calendar.setDate(date);
            calendar.setStationCodes(stationCodes);
            calendarList.add(calendar);
        }
        return calendarList;
    }

    /**
     * 設定不可取車日期
     */
    public List<SubscribeCalendar> setUnavailableSubscribeCalendar(CalendarUpdateRequest request) {
        if (!subscribeCalendarRepository.findAllByDateIn(request.getDateList()).isEmpty()) {
            throw new SubscribeException(CALENDAR_DATE_EXIST);
        }

        validateStationCodes(request.getStationCodes());
        List<SubscribeCalendar> calendarList = buildCalendarList(request.getDateList(), request.getStationCodes());
        return subscribeCalendarRepository.saveAll(calendarList);
    }

    /**
     * 取得存在的不可取車日期列表
     */
    private List<SubscribeCalendar> getExistingCalendarsOrThrow(List<String> dateList) {
        List<SubscribeCalendar> calendarList = subscribeCalendarRepository.findAllByDateIn(dateList);
        if (CollectionUtils.isEmpty(calendarList)) {
            throw new SubscribeException(HttpStatus.OK, CALENDAR_UPDATABLE_DATE_NOT_FOUND, CALENDAR_UPDATABLE_DATE_NOT_FOUND.getMsg() + ": " + dateList);
        }
        List<String> notFoundDates = new ArrayList<>(dateList);
        notFoundDates.removeAll(calendarList.stream().map(SubscribeCalendar::getDate).collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(notFoundDates)) {
            throw new SubscribeException(HttpStatus.OK, CALENDAR_UPDATABLE_DATE_NOT_FOUND, CALENDAR_UPDATABLE_DATE_NOT_FOUND.getMsg() + ": " + notFoundDates);
        }
        return calendarList;
    }

    /**
     * 編輯不可取車日期
     */
    public List<SubscribeCalendar> updateUnavailableSubscribeCalendar(CalendarUpdateRequest request) {
        List<SubscribeCalendar> updateList = getExistingCalendarsOrThrow(request.getDateList());
        validateStationCodes(request.getStationCodes());
        for (SubscribeCalendar calendar : updateList) {
            calendar.setStationCodes(request.getStationCodes());
        }
        return subscribeCalendarRepository.saveAll(updateList);
    }

    /**
     * 刪除不可取車日期
     */
    public void deleteUnavailableSubscribeCalendar(CalendarDeleteRequest request) {
        List<SubscribeCalendar> deleteList = getExistingCalendarsOrThrow(request.getDateList());
        subscribeCalendarRepository.deleteAll(deleteList);
    }

}
