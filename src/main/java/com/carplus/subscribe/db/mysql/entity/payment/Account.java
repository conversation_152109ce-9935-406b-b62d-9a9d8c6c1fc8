package com.carplus.subscribe.db.mysql.entity.payment;

import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.entity.GeneralEntity;
import com.carplus.subscribe.enums.AccountType;
import com.carplus.subscribe.enums.PayFor;
import com.carplus.subscribe.model.payment.req.AccountRecord;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.NonNull;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@NoArgsConstructor
@Data
@Entity
@Table(name = "accounts")
public class Account extends GeneralEntity {

    @Schema(description = "帳目編號")
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Schema(description = "舊短租收支主檔序號")
    @Column(name = "collectAuto")
    private Integer collectAuto;

    /**
     * 訂單編號
     */
    @JsonIgnore
    @Column(name = "orderNo", nullable = false)
    private String orderNo;

    /**
     * db: 該收支淨收金額<br>
     * req: 總退款金額
     */
    @Schema(description = "金額", required = true)
    @NotNull
    @Column(name = "amount", nullable = false)
    private Integer amount;

    /**
     * db: 總退款金額
     */
    @Schema(description = "總退款金額")
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    @Column(name = "totalRefundAmount", nullable = false)
    private Integer totalRefundAmount;

    @Schema(description = "收退款種類 Credit: 信用卡, Cash: 現金, Remit: 匯款, Check: 支票", required = true)
    @NotNull
    @Column(name = "accountType", nullable = false)
    @Enumerated(EnumType.STRING)
    private AccountType accountType;

    @Schema(description = "支付目的 Depart: 出車款, Return: 還車款, Accident: 車損款, Other: 其他款項, All: 所有款項", required = true)
    @NotNull
    @Column(name = "payFor", nullable = false)
    @Enumerated(EnumType.STRING)
    private PayFor payFor;

    @Schema(description = "站所代碼")
    @Column(name = "stationCode")
    private String stationCode;

    @Schema(description = "TapPay 查帳與退款用 id")
    @Column(name = "tradeId")
    private String tradeId;

    @Schema(description = "刷卡方式 1: EDC-台新銀, 2: EDC-聯信(AE), 10: EDC-聯信(花旗), 6: 傳刷-台新銀, 7: 網路刷卡-中信銀, 11: 網路刷卡-台新銀, 12: 簡訊刷卡-台新銀, 14: 中信銀Tappay, 15: 台新銀Tappay , 16: 聯信Tappay, 3: 手刷-台新銀, 4: 手刷-聯信, 5: 手刷-匯豐, 13: 台新銀行後台")
    @Column(name = "chargeType")
    private Integer chargeType;

    @Schema(description = "信用卡號前六後四")
    @Column(name = "cardNumber")
    private String cardNumber;

    @Schema(description = "信用卡授權碼")
    @Column(name = "authCode")
    private String authCode;

    @Schema(description = "匯款人")
    @Column(name = "remitter")
    private String remitter;

    @Schema(description = "匯款帳號")
    @Column(name = "remitAccCode")
    private String remitAccCode;

    @Schema(description = "匯款編號")
    @Column(name = "remitNo")
    private Long remitNo;

    @Schema(description = "支票號碼")
    @Column(name = "checkNo")
    private String checkNo;

    @Schema(description = "開票人")
    @Column(name = "drawer")
    private String drawer;

    @Schema(description = "票據指定銀行(銀行名稱)")
    @Column(name = "checkAppointBack")
    private String checkAppointBack;

    @Schema(description = "甲存帳號")
    @Column(name = "checkingAccNo")
    private String checkingAccNo;

    @Schema(description = "票據到期日")
    @Column(name = "checkDueDate")
    private Date checkDueDate;

    @Schema(description = "銀行交易序號")
    @Column(name = "transactionNumber")
    private String transactionNumber;

    @Schema(description = "是否刪除")
    @JsonProperty("isDeleted")
    @Column(name = "isDeleted", nullable = false, columnDefinition = "TINYINT(1)")
    private boolean isDeleted;

    /**
     * req: 總收款金額（不包含退款）
     */
    @Schema(description = "應退原始收款金額")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    @Transient
    private Integer originalAmount;

    /**
     * req: 退款金額
     */
    @Schema(description = "應退")
    @Transient
    private Integer refundAmount;

    /**
     * req: 訂單費用資訊編號
     */
    @Schema(description = "訂單費用資訊編號")
    @Transient
    private List<Integer> orderPriceInfoIds;

    public Account(
        @NonNull AccountRecord accountReq,
        @NonNull String orderNo,
        @NonNull Integer amount,
        @NonNull Integer totalRefundAmount,
        List<Integer> orderPriceInfoIds
    ) {
        collectAuto = accountReq.getCollectAuto();
        this.orderNo = orderNo;
        this.amount = amount;
        this.totalRefundAmount = totalRefundAmount;
        accountType = accountReq.getAccountType();
        chargeType = accountReq.getChargeType();
        String carNo = StringUtils.trim(accountReq.getCardNumber());
        if (StringUtils.isNotBlank(carNo) && !carNo.contains("*")) {
            if (carNo.length() >= 4) {
                carNo = carNo.substring(carNo.length() - 4);
            }
            carNo = StringUtils.pad(carNo, '*', 16);
        }
        cardNumber = carNo;
        authCode = accountReq.getAuthCode();
        remitter = accountReq.getRemitter();
        remitAccCode = accountReq.getRemitAccCode();
        remitNo = accountReq.getRemitNo();
        tradeId = accountReq.getTradeId();
        payFor = accountReq.getPayFor();
        stationCode = accountReq.getStationCode();
        checkNo = accountReq.getCheckNo();
        drawer = accountReq.getDrawer();
        checkAppointBack = accountReq.getCheckAppointBack();
        checkingAccNo = accountReq.getCheckingAccNo();
        checkDueDate = accountReq.getCheckDueDate();
        transactionNumber = accountReq.getTransactionNumber();
        this.orderPriceInfoIds = orderPriceInfoIds;
    }
}
