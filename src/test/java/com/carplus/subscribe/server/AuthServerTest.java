package com.carplus.subscribe.server;

import com.carplus.subscribe.model.auth.AuthDealerUser;
import com.carplus.subscribe.model.auth.req.AuthDealerUserSaveRequest;
import com.carplus.subscribe.model.auth.resp.AuthDealerUserResponse;
import com.carplus.subscribe.model.auth.resp.AuthDealerUserUnionQuery;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class AuthServerTest {
    @Autowired
    private AuthServer authServer;

    @Test
    public void getDealerUsers(){
        AuthDealerUserUnionQuery query = new AuthDealerUserUnionQuery();
        query.setIdNo(Collections.singletonList("E190746076"));
        List<AuthDealerUserResponse> list = authServer.getDealerUsers(query);
        System.out.println(list.size());
    }

    @Test
    public void saveDealerUsers(){
        AuthDealerUserSaveRequest request = new AuthDealerUserSaveRequest(new ArrayList<>());
        AuthDealerUser user = new AuthDealerUser();
        user.setIdNo("11111");
        user.setUserName("劉帥哥");
        user.setNationalCode("886");
        user.setMainCell("0912345678");
        user.setBirthDay("1992/07/07");
        user.setEmail("<EMAIL>");
        user.setHhcityId(1);
        user.setHhareaId(1);
        user.setHhaddress("測試路");
        request.getUsers().add(user);
        List<AuthDealerUserResponse> list = authServer.saveDealerUsers(request);
        System.out.println(list.size());
    }

    @Test
    public void approveAuditPreSignedReview(){
        authServer.approveAuditPreSignedReview(1000214,"K2456");
    }
}
