package com.carplus.subscribe.listener;

import com.carplus.subscribe.enums.OrderStatus;
import com.carplus.subscribe.event.order.*;
import com.carplus.subscribe.server.AuthorityServer;
import com.carplus.subscribe.server.MattermostServer;
import com.carplus.subscribe.service.OrderService;
import com.carplus.subscribe.service.SkuShipmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.LinkedHashMap;
import java.util.Map;

@Slf4j
@Component
public class OrderEventListener {

    @Autowired
    private AuthorityServer authorityServer;

    @Autowired
    private OrderService orderService;

    @Autowired
    private MattermostServer mattermostServer;

    @Autowired
    private SkuShipmentService skuShipmentService;

    /**
     * 監聽訂單授信通過事件
     */
    @Async
    @EventListener
    public void handleOrderCreditApproved(OrderCreditApprovedEvent event) {
        authorityServer.updateUserApproval(event.getAcctId());
    }

    @Async
    @EventListener
    public void handleOrderBookingEvent(OrderBookingEvent event) {
        try {
            skuShipmentService.createShipments(event.getOrder(), event.getOperator());
        } catch (Exception e) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("orderNo", event.getOrder().getOrderNo());
            map.put("memberId", event.getOperator());
            mattermostServer.notify("訂單狀態變更為已訂車，建立出貨資料失敗", map, e);
        }
    }

    @Async
    @EventListener
    public void handleOrderReturnedEvent(OrderReturnedEvent event) {
        String content = String.format("訂單迄租成功，迄租後訂單狀態為「%s」", OrderStatus.of(event.getOrder().getStatus()).getName());
        try {
            orderService.addRemark(event.getOrder(), content, event.getMemberId());
        } catch (Exception e) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("orderNo", event.getOrder().getOrderNo());
            map.put("content", content);
            mattermostServer.notify("訂單迄租成功，建立備註失敗", map, e);
        }
    }

    @Async
    @TransactionalEventListener
    public void handleOrderClosedEvent(OrderClosedEvent event) {
        String content = String.format("訂單結案成功，結案備註「%s」", event.getCloseRemark());
        try {
            orderService.addRemark(event.getOrder(), content, event.getMemberId());
        } catch (Exception e) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("orderNo", event.getOrder().getOrderNo());
            map.put("content", content);
            mattermostServer.notify("訂單結案成功，建立備註失敗", map, e);
        }
    }

    @Async
    @TransactionalEventListener
    public void handleOrderReopenedEvent(OrderReopenedEvent event) {
        String content = "訂單異動成功，迄租後訂單狀態為「已還車未結案」";
        try {
            orderService.addRemark(event.getOrder(), content, event.getMemberId());
        } catch (Exception e) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("orderNo", event.getOrder().getOrderNo());
            map.put("content", content);
            mattermostServer.notify("訂單異動成功，建立備註失敗", map, e);
        }
    }

    @Async
    @TransactionalEventListener
    public void handleOrderCreditPendingEvent(OrderCreditPendingEvent event) {
        String content = "訂單異動成功，審核失敗訂單狀態異動為「待審核」";
        try {
            orderService.addRemark(event.getOrder(), content, event.getMemberId());
        } catch (Exception e) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("orderNo", event.getOrder().getOrderNo());
            map.put("content", content);
            mattermostServer.notify("訂單異動成功，建立備註失敗", map, e);
        }
    }
}
