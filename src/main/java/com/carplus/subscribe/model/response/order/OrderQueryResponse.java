package com.carplus.subscribe.model.response.order;

import com.carplus.subscribe.db.mysql.entity.cars.CarBrand;
import com.carplus.subscribe.db.mysql.entity.cars.CarModel;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.db.mysql.entity.contract.MainContract;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.enums.CarReady;
import com.carplus.subscribe.enums.ContractStatus;
import com.carplus.subscribe.enums.OrderStatus;
import com.carplus.subscribe.enums.RenewType;
import com.carplus.subscribe.model.SecurityDepositInfo;
import com.carplus.subscribe.model.credit.Auditor;
import com.carplus.subscribe.model.credit.AutoCreditInfo;
import com.carplus.subscribe.model.credit.CreditInfo;
import com.carplus.subscribe.model.credit.ManualCreditInfo;
import com.carplus.subscribe.model.order.Remark;
import com.carplus.subscribe.model.subscribelevel.SubscribeLevelResponse;
import com.carplus.subscribe.utils.DateUtil;
import com.carplus.subscribe.utils.OrderUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.univocity.parsers.annotations.Parsed;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static java.time.temporal.ChronoUnit.DAYS;

@FieldNameConstants
@AllArgsConstructor
@NoArgsConstructor
@Data
public class OrderQueryResponse {

    @Schema(description = "訂單編號")
    private String orderNo;

    @Schema(description = "訂單狀態")
    private int status;

    @Schema(description = "訂單狀態名稱")
    private String statusName;

    @Schema(description = "訂單租期")
    private Integer month;

    @Schema(description = "是否新訂單")
    private boolean isNewOrder;

    @Schema(description = "第幾期訂單")
    private int stage;

    @Schema(description = "授信狀態")
    private CreditInfo creditInfo;

    @Schema(description = "是否有未繳款項")
    private boolean isUnpaid;

    @Schema(description = "訂單建立時間")
    private Instant createDate;

    @Schema(description = "訂單取消時間")
    private Instant cancelDate;

    @Schema(description = "預計出車日")
    private Instant expectStartDate;

    @Schema(description = "實際出車日")
    private Instant startDate;

    @Schema(description = "預計還車日")
    private Instant expectEndDate;

    @Schema(description = "實際還車日")
    private Instant endDate;

    @Schema(description = "剩餘/預期天數")
    private Long diffDays;

    @Schema(description = "主約備註")
    private String mainContractRemark;

    @Schema(description = "訂單備註")
    private List<Remark> remarks;

    @Schema(description = "客戶訂單備註")
    private String custRemark;

    @Schema(description = "出車站點")
    private String departStation;
    @Schema(description = "出車站點名稱")
    private String departStationName;

    @Schema(description = "還車站點")
    private String returnStation;
    @Schema(description = "還車站點名稱")
    private String returnStationName;

    @Schema(description = "出車人員")
    private String departMemberId;

    @Schema(description = "出車人員名稱")
    private String departMemberName;

    @Schema(description = "出車備註")
    private String departRemark;

    @Schema(description = "還車人員")
    private String returnMemberId;

    @Schema(description = "還車人員名稱")
    private String returnMemberName;

    @Schema(description = "還車備註")
    private String returnRemark;

    @Schema(description = "續約狀態")
    private RenewType renewType;

    @Schema(description = "續約訂單號碼")
    private String nextStageOrderNo;

    @Schema(description = "不續約原因")
    private String nonRenewRemark;

    @Schema(description = "合約編號")
    private String contractNo;

    @Schema(description = "主約編號")
    private String mainContractNo;

    @JsonProperty("isEContractSigned")
    @Schema(description = "是否完成電子合約簽約")
    private boolean isEContractSigned;

    @Schema(description = "主約狀態")
    private ContractStatus mainContractStatus;

    @Schema(description = "用戶編號")
    private Integer acctId;

    @Schema(description = "用戶名稱")
    private String custName;

    @Schema(description = "身分證號/居留證號")
    private String idNo;

    @Schema(description = "手機號碼")
    private String mainCell;

    @Schema(description = "國碼")
    private String nationalCode;

    @Schema(description = "客戶備註")
    private String custMemo;

    @Schema(description = "車牌號碼")
    private String plateNo;

    @Schema(description = "備車狀態")
    private CarReady carReady;

    @Schema(description = "車型名稱")
    private String carModelName;

    @Schema(description = "車型編號")
    private String carModelCode;


    @Schema(description = "廠牌名稱")
    private String brandName;

    @Schema(description = "廠牌編號")
    private String brandCode;

    @Schema(description = "訂單成立時間")
    private Instant securityDepositDate;

    @Schema(description = "訂單成立時間")
    private Instant auditApprovedDate;

    @Schema(description = "訂閱方案")
    private SubscribeLevelResponse subscribeLevel;

    @Schema(description = "月費")
    private Integer useMonthlyFee;

    @Schema(description = "戶籍縣市編號")
    @Parsed(field = "戶籍縣市編號")
    private Integer city;

    @Schema(description = "戶籍區域編號")
    @Parsed(field = "戶籍區域編號")
    private Integer area;

    @Schema(description = "通訊地址")
    @Parsed(field = "通訊地址")
    private String address;

    @Schema(description = "戶籍地址")
    @Parsed(field = "戶籍地址")
    private String hhrAddr;

    @Schema(description = "戶籍地址-行政區ID")
    @Parsed(field = "戶籍地址-行政區ID")
    private Integer hhrAreaId;

    @Schema(description = "戶籍地址-縣市ID")
    @Parsed(field = "戶籍地址-縣市ID")
    private Integer hhrCityId;

    @Schema(description = "實際付款金額,不含保證金")
    private Integer paidAmt;

    @Schema(description = "實際金額,不含保證金")
    private Integer totalAmt;

    @Schema(description = "車籍統編")
    private String carVatNo;

    @JsonIgnore
    private String lrentalOrderNo;

    public OrderQueryResponse(OrderDTO dto) {
        this(dto.getOrders(),
            Optional.ofNullable(dto.getMainContract()).orElseGet(MainContract::new),
            Optional.ofNullable(dto.getCarModel()).orElseGet(CarModel::new),
            Optional.ofNullable(dto.getCarBrand()).orElseGet(CarBrand::new),
            Optional.ofNullable(dto.getCar()).orElseGet(Cars::new));
    }


    public OrderQueryResponse(Orders orders, MainContract mainContract, CarModel carModel, CarBrand carBrand, Cars car) {
        OrderUtils.sortRemarks(orders);
        this.orderNo = orders.getOrderNo();
        this.status = orders.getStatus();
        this.statusName = OrderStatus.of(status).getName();
        this.month = orders.getMonth();
        this.isNewOrder = orders.getIsNewOrder();
        this.stage = orders.getStage();
        this.creditInfo = orders.getCreditInfo();
        this.isUnpaid = Optional.ofNullable(orders.getIsUnpaid()).orElse(false);
        this.createDate = orders.getInstantCreateDate();
        this.cancelDate = orders.getCancelDate();
        this.expectStartDate = orders.getExpectStartDate();
        this.startDate = orders.getStartDate();
        this.expectEndDate = orders.getExpectEndDate();
        this.endDate = orders.getEndDate();
        this.diffDays = DateUtil.calculateDiffDate(DateUtil.convertToStartOfInstant(Instant.now()), DateUtil.convertToStartOfInstant(expectEndDate), DAYS);
        this.mainContractRemark = mainContract.getRemark();
        this.remarks = orders.getRemarks();
        this.custRemark = mainContract.getCustRemark();
        this.departStation = mainContract.getDepartStationCode();
        this.returnStation = mainContract.getReturnStationCode();
        this.departRemark = orders.getDepartRemark();
        this.returnRemark = orders.getReturnRemark();
        this.departMemberId = orders.getDepartMemberId();
        this.returnMemberId = orders.getReturnMemberId();
        this.renewType = orders.getRenewType();
        this.nextStageOrderNo = orders.getNextStageOrderNo();
        this.nonRenewRemark = orders.getNonRenewRemark();
        this.contractNo = orders.getContractNo();
        this.mainContractNo = mainContract.getMainContractNo();
        this.mainContractStatus = ContractStatus.codeOfValue(mainContract.getStatus());
        this.acctId = mainContract.getAcctId();
        this.idNo = mainContract.getIdNo();
        this.plateNo = mainContract.getPlateNo();
        this.carReady = mainContract.getCarReady();
        this.carModelCode = mainContract.getCarModelCode();
        this.carModelName = carModel.getCarModelName();
        this.brandName = carBrand.getBrandNameEn();
        this.brandCode = carBrand.getBrandCode();
        this.carVatNo = car.getVatNo();
        this.lrentalOrderNo = orders.getLrentalContractNo();

        // 若為付款前顯示為空
        if (orders.getIsNewOrder() && orders.getStatus() > OrderStatus.CREDITED.getStatus() && orders.getStatus() <= OrderStatus.CLOSE.getStatus()) {
            this.securityDepositDate =
                Optional.ofNullable(orders.getSecurityDepositDate()).orElse(
                    Optional.ofNullable(mainContract.getOriginalPriceInfo().getSecurityDepositInfo()).map(SecurityDepositInfo::getSecurityDepositDate).map(Date::toInstant)
                        .orElse(orders.getInstantCreateDate()));
        }
        // auditApprovedDate 訂單成立時間
        if (!orders.getIsNewOrder() && isOrderStatusValid(orders)) {
            Optional<Date> creditDate = Optional.ofNullable(orders.getCreditInfo()).map(CreditInfo::getManualCreditInfo).map(ManualCreditInfo::getAuditor).map(Auditor::getAuditDate);

            if (!creditDate.isPresent()) {
                creditDate = Optional.ofNullable(orders.getCreditInfo()).map(CreditInfo::getAutoCreditInfo).orElseGet(ArrayList::new)
                    .stream()
                    .map(AutoCreditInfo::getCreditDate)
                    .filter(Objects::nonNull)
                    .reduce((first, second) -> second);
            }

            this.auditApprovedDate = creditDate.map(Date::toInstant).orElse(null);
        }
        this.useMonthlyFee = mainContract.getOriginalPriceInfo().getUseMonthlyFee();
    }

    private static boolean isOrderStatusValid(Orders orders) {
        Set<Integer> validStatus = Arrays.stream(new Integer[] {
            OrderStatus.BOOKING.getStatus(),
            OrderStatus.DEPART.getStatus(),
            OrderStatus.ARRIVE_NO_CLOSE.getStatus(),
            OrderStatus.CLOSE_WITH_SUB.getStatus(),
            OrderStatus.CLOSE.getStatus()
        }).collect(Collectors.toSet());
        return validStatus.contains(orders.getStatus());
    }
}
